<?php
echo "SERVER_NAME: " . ($_SERVER['SERVER_NAME'] ?? 'NON_DEFINITO') . PHP_EOL;
echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'NON_DEFINITO') . PHP_EOL;
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'NON_DEFINITO') . PHP_EOL;

// Test se il file wp-config-local.php viene caricato
if (isset($_SERVER['SERVER_NAME']) && $_SERVER['SERVER_NAME'] === 'claryfin.local') {
    echo "wp-config-local.php DOVREBBE essere caricato" . PHP_EOL;
} else {
    echo "wp-config-local.php NON viene caricato" . PHP_EOL;
    echo "SERVER_NAME corrente: " . ($_SERVER['SERVER_NAME'] ?? 'NON_DEFINITO') . PHP_EOL;
}
?>
