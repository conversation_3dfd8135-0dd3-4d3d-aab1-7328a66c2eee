<?php
/**
 * Office Add-in Integration
 *
 * This file handles the integration between the WordPress plugin and the Excel add-in.
 * It provides AJAX endpoints for the Excel add-in to retrieve settings and process document analysis.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Office_Addin class
 */
class Office_Addin {
    
    /**
     * Rate limiter instance
     * @var Office_Addin_Rate_Limiter
     */
    private $rate_limiter;
    
    /**
     * Cache manager instance
     * @var Office_Addin_Cache_Manager
     */
    private $cache_manager;
    
    /**
     * Error reporter instance
     * @var Office_Addin_Error_Reporter
     */
    private $error_reporter;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Include required classes
        require_once plugin_dir_path(__FILE__) . 'includes/class-document-analyzer.php';
        require_once plugin_dir_path(__FILE__) . 'includes/class-office-addin-rate-limiter.php';
        require_once plugin_dir_path(__FILE__) . 'includes/class-office-addin-cache-manager.php';
        require_once plugin_dir_path(__FILE__) . 'includes/class-office-addin-error-reporter.php';
        
        // Include AJAX security status handler
        require_once plugin_dir_path(__FILE__) . 'ajax-security-status.php';
        
        // Include security status tabs (non-invasive)
        if (file_exists(plugin_dir_path(__FILE__) . 'includes/class-office-addin-security-tabs.php')) {
            require_once plugin_dir_path(__FILE__) . 'includes/class-office-addin-security-tabs.php';
        }
        
        // Initialize components
        $this->rate_limiter = new Office_Addin_Rate_Limiter();
        $this->cache_manager = new Office_Addin_Cache_Manager();
        $this->error_reporter = new Office_Addin_Error_Reporter();
        
        // Register AJAX actions with unique names to avoid conflicts
        add_action('wp_ajax_office_addin_get_settings', array($this, 'get_office_addin_settings'));
        add_action('wp_ajax_nopriv_office_addin_get_settings', array($this, 'get_office_addin_settings'));

        add_action('wp_ajax_office_addin_get_queries', array($this, 'get_predefined_queries'));
        add_action('wp_ajax_nopriv_office_addin_get_queries', array($this, 'get_predefined_queries'));

        add_action('wp_ajax_office_addin_analyze', array($this, 'analyze_excel_data'));
        add_action('wp_ajax_nopriv_office_addin_analyze', array($this, 'analyze_excel_data'));
        
        // Add new AJAX handler for preview script
        add_action('wp_ajax_office_addin_preview_script', array($this, 'get_preview_script'));
        add_action('wp_ajax_nopriv_office_addin_preview_script', array($this, 'get_preview_script'));
        
        // Add AJAX handler for preview content
        add_action('wp_ajax_office_addin_preview', array($this, 'preview_content'));
        
        // Add AJAX handler for AI status check (using balance-sheet widget method)
        add_action('wp_ajax_check_ai_status', array($this, 'check_ai_status'));
        add_action('wp_ajax_nopriv_check_ai_status', array($this, 'check_ai_status'));

        // Register the Office Add-in endpoint
        add_action('init', array($this, 'register_office_addin_endpoint'));
    }

    /**
     * Register the Office Add-in endpoint
     */
    public function register_office_addin_endpoint() {
        add_rewrite_rule(
            'office-addin/?$',
            'index.php?office_addin=1',
            'top'
        );

        add_rewrite_tag('%office_addin%', '([^&]+)');

        // Add template include filter
        add_filter('template_include', array($this, 'office_addin_template'));
    }

    /**
     * Office Add-in template
     */
    public function office_addin_template($template) {
        if (get_query_var('office_addin')) {
            // Get the Office Add-in HTML content
            $office_addin_content = get_option('office_addin_content');

            if (empty($office_addin_content)) {
                // If no content exists, load the default content
                if (class_exists('Document_Viewer_Settings')) {
                    $settings = new Document_Viewer_Settings();
                    if (method_exists($settings, 'get_default_office_addin_content')) {
                        $office_addin_content = $settings->get_default_office_addin_content();
                    } else {
                        // Fallback HTML di base
                        $office_addin_content = '
                        <div class="excel-addin-container">
                            <div class="section">
                                <h2>Financial Advisor Excel Add-in</h2>
                                <p>Extract and analyze financial data from your Excel spreadsheets.</p>

                                <button id="extract-text" class="primary-button">Extract Selected Cells</button>

                                <div id="extracted-text-container" style="display: none;">
                                    <h3>Extracted Text</h3>
                                    <pre id="extracted-text"></pre>
                                </div>
                            </div>

                            <div class="section">
                                <h3>Analysis Options</h3>

                                <div class="form-group">
                                    <label for="predefined-query">Select a predefined query:</label>
                                    <select id="predefined-query" class="form-control"></select>
                                </div>

                                <div class="form-group">
                                    <label for="custom-query">Or enter a custom question:</label>
                                    <input type="text" id="custom-query" class="form-control" placeholder="E.g., What are the key trends?">
                                </div>

                                <button id="analyze-button" class="primary-button">Analyze Data</button>
                            </div>

                            <div class="section">
                                <h3>Analysis Results</h3>
                                <div id="analysis-results">
                                    <p class="placeholder">Results will appear here after analysis.</p>
                                </div>
                            </div>

                            <div class="section">
                                <h3>API Connection</h3>
                                <p>Status: <span id="api-status">Not connected</span></p>
                                <p>Model: <span id="selected-model">Not selected</span></p>
                            </div>
                            
                            <!-- Security Status Section - Added automatically -->
                            <div class="section security-status-section" id="office-addin-security-section">
                                <div class="security-header">
                                    <h3>🔒 Security Status</h3>
                                    <span class="security-indicator" id="security-overall-status">
                                        <span class="status-dot checking"></span>
                                        Checking...
                                    </span>
                                </div>
                                
                                <div class="security-tabs">
                                    <button class="tab-button active" data-tab="overview">Overview</button>
                                    <button class="tab-button" data-tab="protection">Protection</button>
                                    <button class="tab-button" data-tab="monitoring">Monitoring</button>
                                    <button class="tab-button" data-tab="performance">Performance</button>
                                </div>
                                
                                <div class="tab-content">
                                    <!-- Overview Tab -->
                                    <div class="tab-pane active" id="tab-overview">
                                        <div class="security-summary">
                                            <div class="security-metric">
                                                <span class="metric-label">Security Layer:</span>
                                                <span class="metric-value" id="security-layer-status">Loading...</span>
                                            </div>
                                            <div class="security-metric">
                                                <span class="metric-label">Active Protections:</span>
                                                <span class="metric-value" id="active-protections-count">-/-</span>
                                            </div>
                                            <div class="security-metric">
                                                <span class="metric-label">Last Check:</span>
                                                <span class="metric-value" id="last-security-check">-</span>
                                            </div>
                                        </div>
                                        
                                        <div class="security-alerts" id="security-alerts-container">
                                            <div class="security-alert info">
                                                <span class="alert-icon">ℹ️</span>
                                                <span class="alert-message">Loading security status...</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Protection Tab -->
                                    <div class="tab-pane" id="tab-protection">
                                        <div class="protection-grid" id="protection-status-grid">
                                            <div class="protection-item">
                                                <div class="protection-icon">⏳</div>
                                                <div class="protection-info">
                                                    <div class="protection-name">Loading Protection Status...</div>
                                                    <div class="protection-status">Please wait</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Monitoring Tab -->
                                    <div class="tab-pane" id="tab-monitoring">
                                        <div class="monitoring-stats" id="monitoring-stats-container">
                                            <p>Loading monitoring statistics...</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Performance Tab -->
                                    <div class="tab-pane" id="tab-performance">
                                        <div class="performance-metrics" id="performance-metrics-container">
                                            <p>Loading performance metrics...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>';
                    }
                } else {
                    // Fallback HTML di base
                    $office_addin_content = '
                    <div class="excel-addin-container">
                        <div class="section">
                            <h2>Financial Advisor Excel Add-in</h2>
                            <p>Extract and analyze financial data from your Excel spreadsheets.</p>

                            <button id="extract-text" class="primary-button">Extract Selected Cells</button>

                            <div id="extracted-text-container" style="display: none;">
                                <h3>Extracted Text</h3>
                                <pre id="extracted-text"></pre>
                            </div>
                        </div>

                        <div class="section">
                            <h3>Analysis Options</h3>

                            <div class="form-group">
                                <label for="predefined-query">Select a predefined query:</label>
                                <select id="predefined-query" class="form-control"></select>
                            </div>

                            <div class="form-group">
                                <label for="custom-query">Or enter a custom question:</label>
                                <input type="text" id="custom-query" class="form-control" placeholder="E.g., What are the key trends?">
                            </div>

                            <button id="analyze-button" class="primary-button">Analyze Data</button>
                        </div>

                        <div class="section">
                            <h3>Analysis Results</h3>
                            <div id="analysis-results">
                                <p class="placeholder">Results will appear here after analysis.</p>
                            </div>
                        </div>

                        <div class="section">
                            <h3>API Connection</h3>
                            <p>Status: <span id="api-status">Not Connected</span></p>
                            <p>Model: <span id="selected-model">Not selected</span></p>
                        </div>
                    </div>';
                }
            }

            // Get the Office Add-in CSS content
            $office_addin_css = get_option('office_addin_css', '');
            if (empty($office_addin_css)) {
                // If no CSS exists, load the default CSS
                if (class_exists('Document_Viewer_Settings')) {
                    $settings = new Document_Viewer_Settings();
                    if (method_exists($settings, 'get_default_office_addin_css')) {
                        $office_addin_css = $settings->get_default_office_addin_css();
                    } else {
                        // Fallback CSS di base
                        $office_addin_css = '
                        .excel-addin-container {
                            font-family: "Segoe UI", Arial, sans-serif;
                            padding: 15px;
                            max-width: 320px;
                            margin: 0 auto;
                        }
                        .section {
                            margin-bottom: 20px;
                            padding: 15px;
                            border: 1px solid #e0e0e0;
                            border-radius: 4px;
                            background-color: #f9f9f9;
                        }';
                    }
                } else {
                    // CSS standard per Excel add-in
                    $office_addin_css = '/* CSS standard per Excel add-in - Non modificare */
/* Questi stili sono necessari per il corretto funzionamento dell\'add-in */
.excel-addin-container {
    font-family: "Segoe UI", Arial, sans-serif;
    padding: 10px;
    width: 320px;
    margin: 0 auto;
    box-sizing: border-box;
}
.section {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
}
h2 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    font-size: 18px;
}
h3 {
    color: #3498db;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
}
.form-group {
    margin-bottom: 10px;
}
label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}
.primary-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}
.secondary-button {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
}';
                }
            }

            // Output the Office Add-in HTML
            header('Content-Type: text/html');
            echo '<!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <title>Financial Advisor Excel Add-in</title>
                <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
                <script src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js"></script>
                <style>
                    /* Reset di base per l\'add-in */
                    body {
                        margin: 0;
                        padding: 0;
                        font-family: "Segoe UI", Arial, sans-serif;
                        font-size: 14px;
                        line-height: 1.4;
                        color: #333;
                        background-color: #fff;
                    }

                    /* Dimensioni standard per un Excel add-in */
                    .excel-addin-container {
                        width: 320px;
                        height: 100%;
                        overflow-y: auto;
                        box-sizing: border-box;
                    }

                    /* CSS personalizzato dell\'add-in */
                    ' . $office_addin_css . '
                    
                    /* AI Status indicator styles */
                    .ai-status-connected {
                        color: #28a745 !important;
                        font-weight: bold;
                    }
                    
                    .ai-status-checking {
                        color: #ffc107 !important;
                        font-weight: bold;
                    }
                    
                    .ai-status-error {
                        color: #dc3545 !important;
                        font-weight: bold;
                    }
                    
                    /* Security Status Styles */
                    .security-status-section {
                        margin-top: 20px;
                        padding: 20px;
                        background: #f8f9fa;
                        border: 1px solid #dee2e6;
                        border-radius: 8px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }
                    
                    .security-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 20px;
                        padding-bottom: 10px;
                        border-bottom: 2px solid #007cba;
                    }
                    
                    .security-header h3 {
                        margin: 0;
                        color: #23282d;
                        font-size: 18px;
                    }
                    
                    .security-indicator {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        font-weight: 600;
                        font-size: 14px;
                    }
                    
                    .status-dot {
                        width: 10px;
                        height: 10px;
                        border-radius: 50%;
                        display: inline-block;
                    }
                    
                    .status-dot.active { background-color: #00a32a; }
                    .status-dot.warning { background-color: #dba617; }
                    .status-dot.error { background-color: #d63638; }
                    .status-dot.checking { 
                        background-color: #007cba;
                        animation: pulse 1.5s infinite;
                    }
                    
                    @keyframes pulse {
                        0% { opacity: 1; }
                        50% { opacity: 0.5; }
                        100% { opacity: 1; }
                    }
                    
                    .security-tabs {
                        display: flex;
                        margin-bottom: 20px;
                        border-bottom: 1px solid #ddd;
                    }
                    
                    .tab-button {
                        background: none;
                        border: none;
                        padding: 12px 20px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        color: #666;
                        border-bottom: 3px solid transparent;
                        transition: all 0.3s ease;
                    }
                    
                    .tab-button:hover {
                        color: #007cba;
                        background-color: #f0f6fc;
                    }
                    
                    .tab-button.active {
                        color: #007cba;
                        border-bottom-color: #007cba;
                        background-color: #f0f6fc;
                    }
                    
                    .tab-content {
                        min-height: 200px;
                    }
                    
                    .tab-pane {
                        display: none;
                    }
                    
                    .tab-pane.active {
                        display: block;
                    }
                    
                    .security-summary {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 15px;
                        margin-bottom: 20px;
                    }
                    
                    .security-metric {
                        background: white;
                        padding: 15px;
                        border-radius: 6px;
                        border: 1px solid #e1e5e9;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    
                    .metric-label {
                        font-weight: 600;
                        color: #23282d;
                    }
                    
                    .metric-value {
                        color: #007cba;
                        font-weight: 500;
                    }
                    
                    .security-alerts {
                        margin-top: 20px;
                    }
                    
                    .security-alert {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        padding: 12px 16px;
                        border-radius: 6px;
                        margin-bottom: 10px;
                        font-size: 14px;
                    }
                    
                    .security-alert.info {
                        background-color: #e7f3ff;
                        border: 1px solid #b8daff;
                        color: #004085;
                    }
                    
                    .security-alert.success {
                        background-color: #d1ecf1;
                        border: 1px solid #b8daff;
                        color: #155724;
                    }
                    
                    .security-alert.warning {
                        background-color: #fff3cd;
                        border: 1px solid #ffeaa7;
                        color: #856404;
                    }
                    
                    .security-alert.error {
                        background-color: #f8d7da;
                        border: 1px solid #f5c6cb;
                        color: #721c24;
                    }
                    
                    .protection-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                        gap: 15px;
                    }
                    
                    .protection-item {
                        background: white;
                        padding: 16px;
                        border-radius: 6px;
                        border: 1px solid #e1e5e9;
                        display: flex;
                        align-items: center;
                        gap: 12px;
                    }
                    
                    .protection-icon {
                        font-size: 24px;
                        width: 40px;
                        text-align: center;
                    }
                    
                    .protection-info {
                        flex: 1;
                    }
                    
                    .protection-name {
                        font-weight: 600;
                        color: #23282d;
                        margin-bottom: 4px;
                    }
                    
                    .protection-status {
                        font-size: 13px;
                        color: #666;
                    }
                    
                    .monitoring-stats, .performance-metrics {
                        background: white;
                        padding: 20px;
                        border-radius: 6px;
                        border: 1px solid #e1e5e9;
                    }
                    
                    .monitoring-grid, .performance-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 15px;
                        margin-top: 15px;
                    }
                    
                    .monitoring-stat, .performance-metric {
                        background: #f8f9fa;
                        padding: 12px;
                        border-radius: 4px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    
                    .monitoring-stat label, .performance-metric label {
                        font-weight: 600;
                        color: #555;
                        margin: 0;
                    }
                    
                    .monitoring-stat span, .performance-metric span {
                        color: #007cba;
                        font-weight: 500;
                    }
                    
                    @media (max-width: 768px) {
                        .security-tabs {
                            flex-wrap: wrap;
                        }
                        
                        .tab-button {
                            padding: 10px 15px;
                            font-size: 13px;
                        }
                        
                        .security-summary {
                            grid-template-columns: 1fr;
                        }
                        
                        .protection-grid {
                            grid-template-columns: 1fr;
                        }
                    }
                </style>
                <script>
                    // Office Add-in initialization
                    Office.onReady(function(info) {
                        // Initialize the Office Add-in
                        if (info.host === Office.HostType.Excel) {
                            // Excel-specific initialization
                            initializeExcelAddin();
                        }
                    });

                    // Initialize the Excel Add-in
                    function initializeExcelAddin() {
                        window.apiSettingsNonce = "' . wp_create_nonce('office_addin_get_settings') . '";
                        window.ajaxUrl = "' . admin_url('admin-ajax.php') . '";
                        
                        // Load the API settings
                        loadApiSettings();

                        // Load the predefined queries
                        loadPredefinedQueries();
                        
                        // Check AI status using balance-sheet widget method
                        checkAIStatus();

                        // Set up event handlers
                        $("#extract-text").click(extractSelectedText);
                        $("#analyze-button").click(analyzeData);
                    }

                    // Load API settings
                    function loadApiSettings() {
                        $.ajax({
                            url: window.ajaxUrl,
                            type: "POST",
                            data: {
                                action: "office_addin_get_settings",
                                nonce: window.apiSettingsNonce
                            },
                            success: function(response) {
                                if (response.success) {
                                    // Update the UI with the API settings
                                    $("#selected-model").text(response.data.model || "Not selected");
                                    // Note: API status is handled by checkAIStatus() function

                                    // Store the API settings for later use
                                    window.apiSettings = response.data;
                                } else {
                                    console.warn("Settings load failed:", response.data);
                                }
                            },
                            error: function() {
                                console.warn("Settings load error");
                            }
                        });
                    }

                    // AI Status check function using balance-sheet widget method
                    function checkAIStatus() {
                        console.log("Checking AI connection status...");

                        $.ajax({
                            url: window.ajaxUrl,
                            type: "POST",
                            data: {
                                action: "check_ai_status",
                                nonce: window.apiSettingsNonce
                            },
                            success: function(response) {
                                if (response.success) {
                                    updateAIStatusIndicator("connected", response.data.message || "AI connected and ready");
                                } else {
                                    updateAIStatusIndicator("error", response.data || "AI connection error");
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error("AI status check failed:", error);
                                updateAIStatusIndicator("error", "Unable to verify AI connection");
                            }
                        });
                    }

                    // Update AI status indicator function using balance-sheet widget method
                    function updateAIStatusIndicator(status, message) {
                        console.log("Updating AI status indicator:", {
                            status: status,
                            message: message
                        });

                        const $indicator = $("#api-status");
                        if ($indicator.length === 0) {
                            console.error("API status indicator element not found!");
                            return;
                        }

                        let statusText, className;
                        switch (status) {
                            case "connected":
                                statusText = "Connected";
                                className = "ai-status-connected";
                                break;
                            case "checking":
                                statusText = "Checking...";
                                className = "ai-status-checking";
                                break;
                            case "error":
                                statusText = "Not Connected";
                                className = "ai-status-error";
                                break;
                            default:
                                statusText = "Unknown";
                                className = "ai-status-error";
                        }

                        // Add styling class and update text
                        $indicator.removeClass("ai-status-checking ai-status-connected ai-status-error")
                                 .addClass(className)
                                 .text(statusText);
                                 
                        console.log("AI status indicator updated:", statusText);
                    }

                    // Load predefined queries
                    function loadPredefinedQueries() {
                        $.ajax({
                            url: "' . admin_url('admin-ajax.php') . '",
                            type: "POST",
                            data: {
                                action: "office_addin_get_queries"
                            },
                            success: function(response) {
                                if (response.success) {
                                    // Populate the predefined queries dropdown
                                    var select = $("#predefined-query");
                                    select.empty().append("<option value=\\"\\">Select a query...</option>");
                                    
                                    if (response.data && response.data.length > 0) {
                                        $.each(response.data, function(index, query) {
                                            select.append($("<option></option>")
                                                .attr("value", query.id)
                                                .text(query.title));
                                        });
                                    } else {
                                        select.append("<option value=\\"\\">No queries available</option>");
                                    }
                                } else {
                                    console.error("Failed to load queries:", response.data.message);
                                }
                            },
                            error: function() {
                                console.error("Error loading predefined queries");
                            }
                        });
                    }

                    // Extract selected text from Excel
                    function extractSelectedText() {
                        Excel.run(function(context) {
                            var range = context.workbook.getSelectedRange();
                            range.load("text");

                            return context.sync().then(function() {
                                var text = "";

                                // Convert the 2D array to a string
                                for (var i = 0; i < range.text.length; i++) {
                                    for (var j = 0; j < range.text[i].length; j++) {
                                        if (range.text[i][j]) {
                                            text += range.text[i][j] + " ";
                                        }
                                    }
                                    text += "\\n";
                                }

                                // Display the extracted text
                                $("#extracted-text").text(text);
                                $("#extracted-text-container").show();
                            });
                        }).catch(function(error) {
                            console.log("Error: " + error);
                        });
                    }

                    // Analyze the extracted data
                    function analyzeData() {
                        var extractedText = $("#extracted-text").text();
                        var queryId = $("#predefined-query").val();
                        var customQuery = $("#custom-query").val();

                        if (!extractedText) {
                            alert("Please extract text from Excel first.");
                            return;
                        }

                        if (!queryId && !customQuery) {
                            alert("Please select a predefined query or enter a custom question.");
                            return;
                        }

                        // Show loading indicator
                        $("#analysis-results").html("<p>Analyzing data...</p>");

                        $.ajax({
                            url: "' . admin_url('admin-ajax.php') . '",
                            type: "POST",
                            data: {
                                action: "office_addin_analyze",
                                text: extractedText,
                                query_id: queryId,
                                custom_query: customQuery
                            },
                            success: function(response) {
                                if (response.success) {
                                    // Display the analysis results
                                    $("#analysis-results").html("<div>" + response.data.result + "</div>");
                                } else {
                                    // Display error message
                                    $("#analysis-results").html("<p>Error: " + response.data.message + "</p>");
                                }
                            },
                            error: function() {
                                $("#analysis-results").html("<p>Error connecting to the server.</p>");
                            }
                        });
                    }
                </script>
                
                <script>
                    // Security tabs functionality - Auto-initialize
                    jQuery(document).ready(function($) {
                        console.log("Security tabs initializing...");
                        
                        // Tab switching functionality
                        $(document).on("click", ".tab-button", function() {
                            var tabId = $(this).data("tab");
                            
                            // Update active tab button
                            $(".tab-button").removeClass("active");
                            $(this).addClass("active");
                            
                            // Update active tab content
                            $(".tab-pane").removeClass("active");
                            $("#tab-" + tabId).addClass("active");
                        });
                        
                        // Load security status
                        function loadSecurityStatus() {
                            console.log("Loading security status...");
                            
                            $.ajax({
                                url: "' . admin_url('admin-ajax.php') . '",
                                type: "POST",
                                data: {
                                    action: "get_security_status",
                                    nonce: "' . wp_create_nonce('security_status_nonce') . '"
                                },
                                success: function(response) {
                                    console.log("Security status loaded:", response);
                                    
                                    if (response.success) {
                                        updateSecurityDisplay(response.data);
                                    } else {
                                        showSecurityError("Failed to load security status");
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error("Security status error:", error);
                                    showSecurityError("Connection error: " + error);
                                }
                            });
                        }
                        
                        function updateSecurityDisplay(data) {
                            // Update overall status
                            var overallStatus = $("#security-overall-status");
                            var statusDot = overallStatus.find(".status-dot");
                            
                            if (data.overall_status === "active") {
                                statusDot.removeClass("checking warning error").addClass("active");
                                overallStatus.find("span:last").text("Secure");
                            } else if (data.overall_status === "warning") {
                                statusDot.removeClass("checking active error").addClass("warning");
                                overallStatus.find("span:last").text("Warning");
                            } else {
                                statusDot.removeClass("checking active warning").addClass("error");
                                overallStatus.find("span:last").text("Issues Detected");
                            }
                            
                            // Update metrics
                            $("#security-layer-status").text(data.security_layer || "Unknown");
                            $("#active-protections-count").text(data.active_protections + "/" + data.total_protections);
                            $("#last-security-check").text(data.last_check || "Never");
                            
                            // Update alerts
                            updateSecurityAlerts(data.alerts || []);
                            
                            // Update protection status
                            updateProtectionStatus(data.protections || []);
                            
                            // Update monitoring stats
                            updateMonitoringStats(data.monitoring || {});
                            
                            // Update performance metrics
                            updatePerformanceMetrics(data.performance || {});
                        }
                        
                        function updateSecurityAlerts(alerts) {
                            var container = $("#security-alerts-container");
                            container.empty();
                            
                            if (alerts.length === 0) {
                                container.append("<div class=\"security-alert success\"><span class=\"alert-icon\">✅</span><span class=\"alert-message\">No security issues detected</span></div>");
                                return;
                            }
                            
                            alerts.forEach(function(alert) {
                                var alertClass = alert.type || "info";
                                var icon = getAlertIcon(alert.type);
                                
                                container.append(
                                    "<div class=\"security-alert " + alertClass + "\">" +
                                    "<span class=\"alert-icon\">" + icon + "</span>" +
                                    "<span class=\"alert-message\">" + alert.message + "</span>" +
                                    "</div>"
                                );
                            });
                        }
                        
                        function updateProtectionStatus(protections) {
                            var container = $("#protection-status-grid");
                            container.empty();
                            
                            if (protections.length === 0) {
                                // Add default protection items
                                var defaultProtections = [
                                    {name: "CSRF Protection", status: "active"},
                                    {name: "Rate Limiting", status: "active"},
                                    {name: "Input Validation", status: "active"},
                                    {name: "Security Logging", status: "active"}
                                ];
                                protections = defaultProtections;
                            }
                            
                            protections.forEach(function(protection) {
                                var icon = getProtectionIcon(protection.status);
                                var statusText = getProtectionStatusText(protection.status);
                                
                                container.append(
                                    "<div class=\"protection-item\">" +
                                    "<div class=\"protection-icon\">" + icon + "</div>" +
                                    "<div class=\"protection-info\">" +
                                    "<div class=\"protection-name\">" + protection.name + "</div>" +
                                    "<div class=\"protection-status\">" + statusText + "</div>" +
                                    "</div>" +
                                    "</div>"
                                );
                            });
                        }
                        
                        function updateMonitoringStats(monitoring) {
                            var container = $("#monitoring-stats-container");
                            var html = "<h4>Security Monitoring Statistics</h4>";
                            
                            html += "<div class=\"monitoring-grid\">";
                            html += "<div class=\"monitoring-stat\"><label>Requests Blocked:</label><span>" + (monitoring.blocked_requests || 0) + "</span></div>";
                            html += "<div class=\"monitoring-stat\"><label>Rate Limit Hits:</label><span>" + (monitoring.rate_limit_hits || 0) + "</span></div>";
                            html += "<div class=\"monitoring-stat\"><label>CSRF Attempts:</label><span>" + (monitoring.csrf_attempts || 0) + "</span></div>";
                            html += "<div class=\"monitoring-stat\"><label>Last 24h Events:</label><span>" + (monitoring.recent_events || 0) + "</span></div>";
                            html += "</div>";
                            
                            container.html(html);
                        }
                        
                        function updatePerformanceMetrics(performance) {
                            var container = $("#performance-metrics-container");
                            var html = "<h4>Security Layer Performance</h4>";
                            
                            html += "<div class=\"performance-grid\">";
                            html += "<div class=\"performance-metric\"><label>Average Response Time:</label><span>" + (performance.avg_response_time || "N/A") + "</span></div>";
                            html += "<div class=\"performance-metric\"><label>Security Overhead:</label><span>" + (performance.security_overhead || "N/A") + "</span></div>";
                            html += "<div class=\"performance-metric\"><label>Memory Usage:</label><span>" + (performance.memory_usage || "N/A") + "</span></div>";
                            html += "<div class=\"performance-metric\"><label>Cache Hit Rate:</label><span>" + (performance.cache_hit_rate || "N/A") + "</span></div>";
                            html += "</div>";
                            
                            container.html(html);
                        }
                        
                        function getAlertIcon(type) {
                            switch(type) {
                                case "success": return "✅";
                                case "warning": return "⚠️";
                                case "error": return "❌";
                                default: return "ℹ️";
                            }
                        }
                        
                        function getProtectionIcon(status) {
                            switch(status) {
                                case "active": return "🛡️";
                                case "warning": return "⚠️";
                                case "error": return "❌";
                                default: return "⏳";
                            }
                        }
                        
                        function getProtectionStatusText(status) {
                            switch(status) {
                                case "active": return "Active and protecting";
                                case "warning": return "Active with warnings";
                                case "error": return "Error - not protecting";
                                default: return "Status unknown";
                            }
                        }
                        
                        function showSecurityError(message) {
                            $("#security-overall-status .status-dot").removeClass("checking active warning").addClass("error");
                            $("#security-overall-status span:last").text("Error");
                            
                            $("#security-alerts-container").html(
                                "<div class=\"security-alert error\">" +
                                "<span class=\"alert-icon\">❌</span>" +
                                "<span class=\"alert-message\">" + message + "</span>" +
                                "</div>"
                            );
                        }
                        
                        // Initial load with delay to ensure DOM is ready
                        setTimeout(function() {
                            loadSecurityStatus();
                        }, 1000);
                        
                        // Auto-refresh every 30 seconds
                        setInterval(loadSecurityStatus, 30000);
                        
                        console.log("Security tabs initialized successfully");
                    });
                </script>
                </script>
            </head>
            <body>
                ' . $office_addin_content . '
            </body>
            </html>';
            exit;
        }

        return $template;
    }

    /**
     * Get Office Add-in settings
     */
    public function get_office_addin_settings() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('get_settings');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for get_settings',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_send_json_error([
                'message' => $rate_check['error'],
                'rate_limit' => $rate_check
            ]);
            return;
        }
        
        try {
            // Try to get from cache first
            $cached_settings = $this->cache_manager->get_settings();
            if ($cached_settings !== false) {
                wp_send_json_success($cached_settings);
                return;
            }

            // Set timeout for database operations
            $start_time = microtime(true);

            // Get the API settings (without exposing sensitive data) with timeout protection
            $api_key = get_option('document_viewer_api_key', '');
            $api_endpoint = get_option('document_viewer_api_endpoint', '');
            $model = get_option('document_viewer_model', '');

            $execution_time = microtime(true) - $start_time;

            // Log slow option retrieval
            if ($execution_time > 2.0) {
                $this->error_reporter->report_error(
                    'Slow settings retrieval detected: ' . $execution_time . 's',
                    Office_Addin_Error_Reporter::TYPE_DATABASE_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_WARNING,
                    ['execution_time' => $execution_time]
                );
            }

            // Return only non-sensitive settings
            $settings = [
                'model' => $model,
                'api_configured' => !empty($api_key) && !empty($api_endpoint),
                'endpoint_configured' => !empty($api_endpoint),
                'load_time' => round($execution_time, 3)
            ];

            // Cache the settings with extended duration for better performance
            $this->cache_manager->set_settings($settings);

            wp_send_json_success($settings);
            
        } catch (Exception $e) {
            $this->error_reporter->report_error(
                'Exception in get_office_addin_settings: ' . $e->getMessage(),
                Office_Addin_Error_Reporter::TYPE_API_ERROR,
                Office_Addin_Error_Reporter::LEVEL_ERROR,
                ['exception' => $e->getTraceAsString()]
            );
            wp_send_json_error(['message' => __('Failed to load settings', 'document-viewer-plugin')]);
        }
    }

    /**
     * Get predefined queries
     */
    public function get_predefined_queries() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('get_queries');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for get_queries',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_send_json_error([
                'message' => $rate_check['error'],
                'rate_limit' => $rate_check
            ]);
            return;
        }
        
        try {
            // Try to get from cache first
            $cached_queries = $this->cache_manager->get_queries();
            if ($cached_queries !== false) {
                wp_send_json_success($cached_queries);
                return;
            }

            global $wpdb;
            $table_name = $wpdb->prefix . 'document_preset_queries';

            // Set database timeout for this operation
            $wpdb->query("SET SESSION wait_timeout = 30");
            $wpdb->query("SET SESSION interactive_timeout = 30");

            // Check if table exists with timeout handling
            $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table_name));

            if ($wpdb->last_error) {
                $this->error_reporter->report_error(
                    'Database timeout checking table existence: ' . $wpdb->last_error,
                    Office_Addin_Error_Reporter::TYPE_DATABASE_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_ERROR
                );
                wp_send_json_error(['message' => __('Database connection timeout', 'document-viewer-plugin')]);
                return;
            }

            if ($table_exists != $table_name) {
                $this->error_reporter->report_error(
                    'Predefined queries table does not exist',
                    Office_Addin_Error_Reporter::TYPE_DATABASE_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_WARNING
                );
                wp_send_json_success([]);
                return;
            }

            // Get the predefined queries with timeout protection
            $start_time = microtime(true);
            $queries = $wpdb->get_results(
                "SELECT id, title, query_text FROM $table_name ORDER BY id ASC LIMIT 100",
                ARRAY_A
            );
            $execution_time = microtime(true) - $start_time;

            // Log slow queries for monitoring
            if ($execution_time > 5.0) {
                $this->error_reporter->report_error(
                    'Slow database query detected: ' . $execution_time . 's',
                    Office_Addin_Error_Reporter::TYPE_DATABASE_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_WARNING,
                    ['execution_time' => $execution_time]
                );
            }

            if ($wpdb->last_error) {
                $this->error_reporter->report_error(
                    'Database error retrieving predefined queries: ' . $wpdb->last_error,
                    Office_Addin_Error_Reporter::TYPE_DATABASE_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_ERROR,
                    ['execution_time' => $execution_time]
                );
                wp_send_json_error(['message' => __('Database query failed', 'document-viewer-plugin')]);
                return;
            }

            if (empty($queries)) {
                wp_send_json_success([]);
                return;
            }

            // Cache the queries with extended duration for better performance
            $this->cache_manager->set_queries($queries);

            // Return the queries
            wp_send_json_success($queries);
            
        } catch (Exception $e) {
            $this->error_reporter->report_error(
                'Exception in get_predefined_queries: ' . $e->getMessage(),
                Office_Addin_Error_Reporter::TYPE_API_ERROR,
                Office_Addin_Error_Reporter::LEVEL_ERROR,
                ['exception' => $e->getTraceAsString()]
            );
            wp_send_json_error(['message' => __('Failed to retrieve queries', 'document-viewer-plugin')]);
        }
    }

    /**
     * Analyze Excel data
     */
    public function analyze_excel_data() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('analyze_excel_data');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for analyze_excel_data',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_send_json_error([
                'message' => $rate_check['error'],
                'rate_limit' => $rate_check
            ]);
            return;
        }
        
        try {
            // Get the request parameters
            $text = isset($_POST['text']) ? sanitize_textarea_field($_POST['text']) : '';
            $query_id = isset($_POST['query_id']) ? intval($_POST['query_id']) : 0;
            $custom_query = isset($_POST['custom_query']) ? sanitize_textarea_field($_POST['custom_query']) : '';

            if (empty($text)) {
                $this->error_reporter->report_error(
                    'No text provided for analysis',
                    Office_Addin_Error_Reporter::TYPE_VALIDATION_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_WARNING
                );
                wp_send_json_error(['message' => __('No text provided for analysis.', 'document-viewer-plugin')]);
                return;
            }

            // Get the query text
            $query_text = '';

            if (!empty($query_id)) {
                global $wpdb;
                $table_name = $wpdb->prefix . 'document_preset_queries';

                $query = $wpdb->get_row($wpdb->prepare("SELECT query_text FROM $table_name WHERE id = %d", $query_id), ARRAY_A);

                if ($query) {
                    $query_text = $query['query_text'];
                } else {
                    $this->error_reporter->report_error(
                        'Predefined query not found: ID ' . $query_id,
                        Office_Addin_Error_Reporter::TYPE_VALIDATION_ERROR,
                        Office_Addin_Error_Reporter::LEVEL_WARNING,
                        ['query_id' => $query_id]
                    );
                    wp_send_json_error(['message' => __('Query not found.', 'document-viewer-plugin')]);
                    return;
                }
            } elseif (!empty($custom_query)) {
                $query_text = $custom_query;
            } else {
                $this->error_reporter->report_error(
                    'No query provided for analysis',
                    Office_Addin_Error_Reporter::TYPE_VALIDATION_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_WARNING
                );
                wp_send_json_error(['message' => __('No query provided for analysis.', 'document-viewer-plugin')]);
                return;
            }

            // Check cache for existing analysis
            $text_hash = $this->cache_manager->generate_text_hash($text);
            $query_hash = $this->cache_manager->generate_query_hash($query_text);
            
            $cached_result = $this->cache_manager->get_analysis_result($text_hash, $query_hash);
            if ($cached_result !== false) {
                wp_send_json_success(['result' => $cached_result['response']]);
                return;
            }

            // Process the document analysis using the Document_Analyzer class
            $document_analyzer = new Document_Analyzer();
            $result = $document_analyzer->analyze_text($text, $query_text);

            // Return the analysis result
            if ($result['success']) {
                // Cache the successful result
                $this->cache_manager->set_analysis_result($text_hash, $query_hash, $result);
                
                wp_send_json_success(['result' => $result['response']]);
            } else {
                $this->error_reporter->report_error(
                    'Document analysis failed: ' . $result['error'],
                    Office_Addin_Error_Reporter::TYPE_API_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_ERROR,
                    ['text_length' => strlen($text), 'query_length' => strlen($query_text)]
                );
                wp_send_json_error(['message' => $result['error']]);
            }
            
        } catch (Exception $e) {
            $this->error_reporter->report_error(
                'Exception in analyze_excel_data: ' . $e->getMessage(),
                Office_Addin_Error_Reporter::TYPE_API_ERROR,
                Office_Addin_Error_Reporter::LEVEL_ERROR,
                ['exception' => $e->getTraceAsString()]
            );
            wp_send_json_error(['message' => __('Analysis failed due to an unexpected error', 'document-viewer-plugin')]);
        }
    }
    
    /**
     * Returns the preview script for Office Add-in
     */
    public function get_preview_script() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('get_preview_script');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for get_preview_script',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_send_json_error([
                'message' => $rate_check['error'],
                'rate_limit' => $rate_check
            ]);
            return;
        }
        
        // Verify nonce for security (only for authenticated requests)
        if (isset($_POST['nonce']) && !wp_verify_nonce($_POST['nonce'], 'office_addin_get_settings')) {
            wp_send_json_error(['message' => __('Security check failed', 'document-viewer-plugin')]);
            return;
        }
        
        header('Content-Type: application/javascript');
        ?>
        // Preview Script Helper Functions
        window.updatePreviewHelper = function() {
            if (typeof window.updatePreview === 'function') {
                window.updatePreview();
            } else {
                console.error('updatePreview function not available');
                // Try to find it in parent window
                if (window.parent && typeof window.parent.updatePreview === 'function') {
                    window.parent.updatePreview();
                } else {
                    console.error('updatePreview function not available in parent window either');
                }
            }
        };
        
        // Make sure jQuery is available
        window.loadjQuery = function(callback) {
            if (typeof jQuery === 'undefined') {
                var script = document.createElement('script');
                script.src = 'https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js';
                script.onload = callback;
            } else {
                callback();
            }
        };
        
        // API Connection methods using balance-sheet widget pattern
        window.ajaxUrl = "<?php echo admin_url('admin-ajax.php'); ?>";
        window.apiSettingsNonce = "<?php echo wp_create_nonce('office_addin_get_settings'); ?>";
        
        // AI Status check function using balance-sheet widget method
        window.checkAIStatus = function() {
            console.log("Checking AI connection status...");

            $.ajax({
                url: window.ajaxUrl,
                type: "POST",
                data: {
                    action: "check_ai_status",
                    nonce: window.apiSettingsNonce
                },
                success: function(response) {
                    if (response.success) {
                        window.updateAIStatusIndicator("connected", response.data.message || "AI connected and ready");
                    } else {
                        window.updateAIStatusIndicator("error", response.data || "AI connection error");
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AI status check failed:", error);
                    window.updateAIStatusIndicator("error", "Unable to verify AI connection");
                }
            });
        };
        
        // Update AI status indicator function using balance-sheet widget method
        window.updateAIStatusIndicator = function(status, message) {
            console.log("Updating AI status indicator:", {
                status: status,
                message: message
            });

            const $indicator = $("#api-status");
            if ($indicator.length === 0) {
                console.error("API status indicator element not found!");
                return;
            }

            let statusText, className;
            switch (status) {
                case "connected":
                    statusText = "Connected";
                    className = "ai-status-connected";
                    break;
                case "checking":
                    statusText = "Checking...";
                    className = "ai-status-checking";
                    break;
                case "error":
                    statusText = "Not Connected";
                    className = "ai-status-error";
                    break;
                default:
                    statusText = "Unknown";
                    className = "ai-status-error";
            }

            // Remove previous classes and add new one
            $indicator.removeClass("ai-status-checking ai-status-connected ai-status-error")
                     .addClass(className)
                     .text(statusText);
                     
            console.log("AI status indicator updated:", statusText);
        };
        <?php
        exit;
    }
    
    /**
     * Preview content for Office Add-in - using unified method like balance-sheet widget
     */
    public function preview_content() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('preview_content');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for preview_content',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_die(__('Rate limit exceeded. Please try again later.', 'document-viewer-plugin'));
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized', 'document-viewer-plugin'));
        }
        
        // Verify nonce for security if provided
        if (isset($_GET['nonce']) && !wp_verify_nonce($_GET['nonce'], 'office_addin_preview')) {
            wp_die(__('Security check failed', 'document-viewer-plugin'));
            return;
        }
        
        try {
            $content = isset($_GET['content']) ? wp_kses_post(urldecode($_GET['content'])) : get_option('office_addin_content', '');
            
            // Get default content if none exists
            if (empty($content)) {
                $content = '<h2>Office Add-in Preview</h2>
                <div class="status-bar">
                    API Status: <span id="api-status">Checking...</span>
                </div>
                <p>This is a preview of your Office Add-in content.</p>
                <button id="analyze-btn">Analyze Document</button>
                <button id="queries-btn">Load Queries</button>
                <div id="analysis-results"></div>';
            }
            
            $preview_script = $this->get_preview_script();
            
            echo '<!DOCTYPE html>
            <html>
            <head>
                <title>Office Add-in Preview</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
                <style>
                    body { 
                        font-family: Segoe UI, Tahoma, Geneva, Verdana, sans-serif; 
                        margin: 20px; 
                        background: #f5f5f5; 
                    }
                    .container { 
                        max-width: 800px; 
                        margin: 0 auto; 
                        background: white; 
                        padding: 20px; 
                        border-radius: 8px; 
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
                    }
                    .preview-content { margin-bottom: 20px; }
                    .status-bar { 
                        padding: 10px; 
                        background: #e9ecef; 
                        border-radius: 4px; 
                        margin-bottom: 20px; 
                    }
                    #api-status { font-weight: bold; }
                    .ai-status-connected { color: #28a745; }
                    .ai-status-error { color: #dc3545; }
                    .ai-status-checking { color: #ffc107; }
                    button { 
                        background: #007cba; 
                        color: white; 
                        border: none; 
                        padding: 10px 15px; 
                        margin: 5px; 
                        border-radius: 4px; 
                        cursor: pointer; 
                    }
                    button:hover { background: #005a87; }
                    button:disabled { background: #ccc; cursor: not-allowed; }
                    #analysis-results { 
                        margin-top: 15px; 
                        padding: 10px; 
                        background: #f8f9fa; 
                        border-radius: 4px; 
                        min-height: 50px; 
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="preview-content">
                        ' . $content . '
                    </div>
                </div>
                ' . $preview_script . '
            </body>
            </html>';
            
        } catch (Exception $e) {
            $this->error_reporter->report_error(
                'Exception in preview_content: ' . $e->getMessage(),
                Office_Addin_Error_Reporter::TYPE_API_ERROR,
                Office_Addin_Error_Reporter::LEVEL_ERROR,
                ['exception' => $e->getTraceAsString()]
            );
            wp_die(__('Preview failed due to an unexpected error', 'document-viewer-plugin'));
        }
        
        exit;
    }
    
    /**
     * Check AI status using balance-sheet widget method
     */
    public function check_ai_status() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('check_ai_status');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for check_ai_status',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_send_json_error([
                'message' => $rate_check['error'],
                'rate_limit' => $rate_check
            ]);
            return;
        }
        
        // Verify nonce for security (using same nonce as other office addin functions)
        if (isset($_POST['nonce']) && !wp_verify_nonce($_POST['nonce'], 'office_addin_get_settings')) {
            wp_send_json_error(['message' => __('Security check failed', 'document-viewer-plugin')]);
            return;
        }
        
        try {
            // Get the API settings to check if they are configured
            $api_key = get_option('document_viewer_api_key', '');
            $api_endpoint = get_option('document_viewer_api_endpoint', '');
            $model = get_option('document_viewer_model', '');

            // Check if all required settings are present
            if (empty($api_key) || empty($api_endpoint) || empty($model)) {
                wp_send_json_error([
                    'message' => __('API not configured. Please check settings.', 'document-viewer-plugin')
                ]);
                return;
            }

            // All settings are present, so API is configured and ready
            wp_send_json_success([
                'message' => __('AI connected and ready', 'document-viewer-plugin'),
                'api_configured' => true,
                'model' => $model
            ]);
            
        } catch (Exception $e) {
            $this->error_reporter->report_error(
                'Exception in check_ai_status: ' . $e->getMessage(),
                Office_Addin_Error_Reporter::TYPE_API_ERROR,
                Office_Addin_Error_Reporter::LEVEL_ERROR,
                ['exception' => $e->getTraceAsString()]
            );
            wp_send_json_error(['message' => __('Failed to check AI status', 'document-viewer-plugin')]);
        }
    }
}

// Initialize the Office Add-in
new Office_Addin();
