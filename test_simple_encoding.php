<?php
// Test diretto della funzione di encoding
$test_message = "Rate limit exceeded: user_2 attempted office_addin_get_settings 60 times (limit: 60)";

// Test conversione encoding
$utf8_message = mb_convert_encoding($test_message, 'UTF-8', 'auto');

// Scrivi nel debug log con encoding corretto
$log_message = '[2025-01-23 15:51:44] [OFFICE ADDIN] ' . $utf8_message . "\n";
file_put_contents('wp-content/debug.log', $log_message, FILE_APPEND | LOCK_EX);

echo "Messaggio scritto nel debug.log con encoding UTF-8\n";
echo "Messaggio originale: " . $test_message . "\n";
echo "Messaggio UTF-8: " . $utf8_message . "\n";
?>
