<?php
/**
 * Office Add-in Integration
 *
 * This file handles the integration between the WordPress plugin and the Excel add-in.
 * It provides AJAX endpoints for the Excel add-in to retrieve settings and process document analysis.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Office_Addin class
 */
class Office_Addin {
    
    /**
     * Rate limiter instance
     * @var Office_Addin_Rate_Limiter
     */
    private $rate_limiter;
    
    /**
     * Cache manager instance
     * @var Office_Addin_Cache_Manager
     */
    private $cache_manager;
    
    /**
     * Error reporter instance
     * @var Office_Addin_Error_Reporter
     */
    private $error_reporter;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Include required classes
        require_once plugin_dir_path(__FILE__) . 'includes/class-document-analyzer.php';
        require_once plugin_dir_path(__FILE__) . 'includes/class-office-addin-rate-limiter.php';
        require_once plugin_dir_path(__FILE__) . 'includes/class-office-addin-cache-manager.php';
        require_once plugin_dir_path(__FILE__) . 'includes/class-office-addin-error-reporter.php';
        
        // Initialize components
        // Aumenta la soglia: 50 richieste ogni 60 secondi
        $this->rate_limiter = new Office_Addin_Rate_Limiter(50, 60);
        $this->cache_manager = new Office_Addin_Cache_Manager();
        $this->error_reporter = new Office_Addin_Error_Reporter();
        
        // Register AJAX actions with unique names to avoid conflicts
        add_action('wp_ajax_office_addin_get_settings', array($this, 'get_office_addin_settings'));
        add_action('wp_ajax_nopriv_office_addin_get_settings', array($this, 'get_office_addin_settings'));

        add_action('wp_ajax_office_addin_get_queries', array($this, 'get_predefined_queries'));
        add_action('wp_ajax_nopriv_office_addin_get_queries', array($this, 'get_predefined_queries'));

        add_action('wp_ajax_office_addin_analyze', array($this, 'analyze_excel_data'));
        add_action('wp_ajax_nopriv_office_addin_analyze', array($this, 'analyze_excel_data'));
        
        // Add new AJAX handler for preview script
        add_action('wp_ajax_office_addin_preview_script', array($this, 'get_preview_script'));
        add_action('wp_ajax_nopriv_office_addin_preview_script', array($this, 'get_preview_script'));
        
        // Add AJAX handler for preview content
        add_action('wp_ajax_office_addin_preview', array($this, 'preview_content'));
        
        // Add AJAX handler for AI status check (using balance-sheet widget method)
        add_action('wp_ajax_check_ai_status', array($this, 'check_ai_status'));
        add_action('wp_ajax_nopriv_check_ai_status', array($this, 'check_ai_status'));

        // Register the Office Add-in endpoint
        add_action('init', array($this, 'register_office_addin_endpoint'));
    }

    /**
     * Register the Office Add-in endpoint
     */
    public function register_office_addin_endpoint() {
        add_rewrite_rule(
            'office-addin/?$',
            'index.php?office_addin=1',
            'top'
        );

        add_rewrite_tag('%office_addin%', '([^&]+)');

        // Add template include filter
        add_filter('template_include', array($this, 'office_addin_template'));
    }

    /**
     * Office Add-in template
     */
    public function office_addin_template($template) {
        if (get_query_var('office_addin')) {
            // Get the Office Add-in HTML content
            $office_addin_content = get_option('office_addin_content');

            if (empty($office_addin_content)) {
                // If no content exists, load the default content
                if (class_exists('Document_Viewer_Settings')) {
                    $settings = new Document_Viewer_Settings();
                    if (method_exists($settings, 'get_default_office_addin_content')) {
                        $office_addin_content = $settings->get_default_office_addin_content();
                    } else {
                        // Fallback HTML di base
                        $office_addin_content = '
                        <div class="excel-addin-container">
                            <div class="section">
                                <h2>Financial Advisor Excel Add-in</h2>
                                <p>Extract and analyze financial data from your Excel spreadsheets.</p>

                                <button id="extract-text" class="primary-button">Extract Selected Cells</button>

                                <div id="extracted-text-container" style="display: none;">
                                    <h3>Extracted Text</h3>
                                    <pre id="extracted-text"></pre>
                                </div>
                            </div>

                            <div class="section">
                                <h3>Analysis Options</h3>

                                <div class="form-group">
                                    <label for="predefined-query">Select a predefined query:</label>
                                    <select id="predefined-query" class="form-control"></select>
                                </div>

                                <div class="form-group">
                                    <label for="custom-query">Or enter a custom question:</label>
                                    <input type="text" id="custom-query" class="form-control" placeholder="E.g., What are the key trends?">
                                </div>

                                <button id="analyze-button" class="primary-button">Analyze Data</button>
                            </div>

                            <div class="section">
                                <h3>Analysis Results</h3>
                                <div id="analysis-results">
                                    <p class="placeholder">Results will appear here after analysis.</p>
                                </div>
                            </div>

                            <div class="section">
                                <h3>API Connection</h3>
                                <p>Status: <span id="api-status">Not connected</span></p>
                                <p>Model: <span id="selected-model">Not selected</span></p>
                            </div>
                        </div>';
                    }
                } else {
                    // Fallback HTML di base
                    $office_addin_content = '
                    <div class="excel-addin-container">
                        <div class="section">
                            <h2>Financial Advisor Excel Add-in</h2>
                            <p>Extract and analyze financial data from your Excel spreadsheets.</p>

                            <button id="extract-text" class="primary-button">Extract Selected Cells</button>

                            <div id="extracted-text-container" style="display: none;">
                                <h3>Extracted Text</h3>
                                <pre id="extracted-text"></pre>
                            </div>
                        </div>

                        <div class="section">
                            <h3>Analysis Options</h3>

                            <div class="form-group">
                                <label for="predefined-query">Select a predefined query:</label>
                                <select id="predefined-query" class="form-control"></select>
                            </div>

                            <div class="form-group">
                                <label for="custom-query">Or enter a custom question:</label>
                                <input type="text" id="custom-query" class="form-control" placeholder="E.g., What are the key trends?">
                            </div>

                            <button id="analyze-button" class="primary-button">Analyze Data</button>
                        </div>

                        <div class="section">
                            <h3>Analysis Results</h3>
                            <div id="analysis-results">
                                <p class="placeholder">Results will appear here after analysis.</p>
                            </div>
                        </div>

                        <div class="section">
                            <h3>API Connection</h3>
                            <p>Status: <span id="api-status">Not Connected</span></p>
                            <p>Model: <span id="selected-model">Not selected</span></p>
                        </div>
                    </div>';
                }
            }

            // Get the Office Add-in CSS content
            $office_addin_css = get_option('office_addin_css', '');
            if (empty($office_addin_css)) {
                // If no CSS exists, load the default CSS
                if (class_exists('Document_Viewer_Settings')) {
                    $settings = new Document_Viewer_Settings();
                    if (method_exists($settings, 'get_default_office_addin_css')) {
                        $office_addin_css = $settings->get_default_office_addin_css();
                    } else {
                        // Fallback CSS di base
                        $office_addin_css = '
                        .excel-addin-container {
                            font-family: "Segoe UI", Arial, sans-serif;
                            padding: 15px;
                            max-width: 320px;
                            margin: 0 auto;
                        }
                        .section {
                            margin-bottom: 20px;
                            padding: 15px;
                            border: 1px solid #e0e0e0;
                            border-radius: 4px;
                            background-color: #f9f9f9;
                        }';
                    }
                } else {
                    // CSS standard per Excel add-in
                    $office_addin_css = '/* CSS standard per Excel add-in - Non modificare */
/* Questi stili sono necessari per il corretto funzionamento dell\'add-in */
.excel-addin-container {
    font-family: "Segoe UI", Arial, sans-serif;
    padding: 10px;
    width: 320px;
    margin: 0 auto;
    box-sizing: border-box;
}
.section {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
}
h2 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    font-size: 18px;
}
h3 {
    color: #3498db;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
}
.form-group {
    margin-bottom: 10px;
}
label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}
.primary-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}
.secondary-button {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
}';
                }
            }

            // Output the Office Add-in HTML
            header('Content-Type: text/html');
            echo '<!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <title>Financial Advisor Excel Add-in</title>
                <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
                <script src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js"></script>
                <style>
                    /* Reset di base per l\'add-in */
                    body {
                        margin: 0;
                        padding: 0;
                        font-family: "Segoe UI", Arial, sans-serif;
                        font-size: 14px;
                        line-height: 1.4;
                        color: #333;
                        background-color: #fff;
                    }

                    /* Dimensioni standard per un Excel add-in */
                    .excel-addin-container {
                        width: 320px;
                        height: 100%;
                        overflow-y: auto;
                        box-sizing: border-box;
                    }

                    /* CSS personalizzato dell\'add-in */
                    ' . $office_addin_css . '
                    
                    /* AI Status indicator styles */
                    .ai-status-connected {
                        color: #28a745 !important;
                        font-weight: bold;
                    }
                    
                    .ai-status-checking {
                        color: #ffc107 !important;
                        font-weight: bold;
                    }
                    
                    .ai-status-error {
                        color: #dc3545 !important;
                        font-weight: bold;
                    }
                </style>
                <script>
                    Office.onReady(function(info) {
                        if (info.host === Office.HostType.Excel) {
                            initializeExcelAddin();
                        }
                    });

                    function initializeExcelAddin() {
                        window.apiSettingsNonce = "' . wp_create_nonce('office_addin_get_settings') . '";
                        window.ajaxUrl = "' . admin_url('admin-ajax.php') . '";
                        loadApiSettings();
                        loadPredefinedQueries();
                        checkAIStatus(); // Use balance-sheet widget AI status check method
                        $("#extract-text").click(extractSelectedText);
                        $("#analyze-button").click(analyzeData);
                    }

                    // AI Status check function using balance-sheet widget method
                    function checkAIStatus() {
                        console.log("Checking AI connection status...");

                        $.ajax({
                            url: window.ajaxUrl,
                            type: "POST",
                            data: {
                                action: "check_ai_status",
                                nonce: window.apiSettingsNonce
                            },
                            success: function(response) {
                                if (response.success) {
                                    updateAIStatusIndicator("connected", response.data.message || "AI connected and ready");
                                } else {
                                    updateAIStatusIndicator("error", response.data || "AI connection error");
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error("AI status check failed:", error);
                                updateAIStatusIndicator("error", "Unable to verify AI connection");
                            }
                        });
                    }

                    // Update AI status indicator function using balance-sheet widget method
                    function updateAIStatusIndicator(status, message) {
                        console.log("Updating AI status indicator:", {
                            status: status,
                            message: message
                        });

                        const $indicator = $("#api-status");
                        if ($indicator.length === 0) {
                            console.error("API status indicator element not found!");
                            return;
                        }

                        let statusText, className;
                        switch (status) {
                            case "connected":
                                statusText = "Connected";
                                className = "ai-status-connected";
                                break;
                            case "checking":
                                statusText = "Checking...";
                                className = "ai-status-checking";
                                break;
                            case "error":
                                statusText = "Not Connected";
                                className = "ai-status-error";
                                break;
                            default:
                                statusText = "Unknown";
                                className = "ai-status-error";
                        }

                        // Add styling class and update text
                        $indicator.removeClass("ai-status-checking ai-status-connected ai-status-error")
                                 .addClass(className)
                                 .text(statusText);
                                 
                        console.log("AI status indicator updated:", statusText);
                    }

                    // Load API settings
                    function loadApiSettings() {
                        $.ajax({
                            url: window.ajaxUrl,
                            type: "POST",
                            timeout: 10000, // 10 secondi
                            data: {
                                action: "office_addin_get_settings",
                                nonce: window.apiSettingsNonce
                            },
                            success: function(response) {
                                if (response.success) {
                                    $("#selected-model").text(response.data.model || "Not selected");
                                    // Note: API status is handled by checkAIStatus() function
                                    window.apiSettings = response.data;
                                } else {
                                    console.warn("Settings load failed:", response.data);
                                }
                            },
                            error: function(xhr, status, error) {
                                if (status === "timeout") {
                                    console.warn("API settings load timeout");
                                } else {
                                    console.warn("Settings load error:", error);
                                }
                            }
                        });
                    }

                    // Load predefined queries
                    function loadPredefinedQueries() {
                        $.ajax({
                            url: window.ajaxUrl,
                            type: "POST",
                            data: {
                                action: "office_addin_get_queries",
                                nonce: window.apiSettingsNonce
                            },
                            success: function(response) {
                                if (response.success) {
                                    // Populate the predefined queries dropdown
                                    var select = $("#predefined-query");
                                    select.empty().append("<option value=\\"\\">Select a query...</option>");
                                    
                                    if (response.data && response.data.length > 0) {
                                        $.each(response.data, function(index, query) {
                                            select.append($("<option></option>")
                                                .attr("value", query.id)
                                                .text(query.title));
                                        });
                                    } else {
                                        select.append("<option value=\\"\\">No queries available</option>");
                                    }
                                } else {
                                    console.error("Failed to load queries:", response.data ? response.data.message : "Unknown error");
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error("Error loading predefined queries:", status, error);
                            }
                        });
                    }

                    // Extract selected text from Excel
                    function extractSelectedText() {
                        Excel.run(function(context) {
                            var range = context.workbook.getSelectedRange();
                            range.load("text");

                            return context.sync().then(function() {
                                var text = "";

                                // Convert the 2D array to a string
                                for (var i = 0; i < range.text.length; i++) {
                                    for (var j = 0; j < range.text[i].length; j++) {
                                        if (range.text[i][j]) {
                                            text += range.text[i][j] + " ";
                                        }
                                    }
                                    text += "\\n";
                                }

                                // Display the extracted text
                                $("#extracted-text").text(text);
                                $("#extracted-text-container").show();
                            });
                        }).catch(function(error) {
                            console.log("Error: " + error);
                        });
                    }

                    // Analyze the extracted data
                    function analyzeData() {
                        var extractedText = $("#extracted-text").text();
                        var queryId = $("#predefined-query").val();
                        var customQuery = $("#custom-query").val();

                        if (!extractedText) {
                            alert("Please extract text from Excel first.");
                            return;
                        }

                        if (!queryId && !customQuery) {
                            alert("Please select a predefined query or enter a custom question.");
                            return;
                        }

                        // Show loading indicator
                        $("#analysis-results").html("<p>Analyzing data...</p>");

                        $.ajax({
                            url: window.ajaxUrl,
                            type: "POST",
                            data: {
                                action: "office_addin_analyze",
                                text: extractedText,
                                query_id: queryId,
                                custom_query: customQuery,
                                nonce: window.apiSettingsNonce
                            },
                            success: function(response) {
                                if (response.success) {
                                    // Display the analysis results
                                    $("#analysis-results").html("<div>" + response.data.result + "</div>");
                                } else {
                                    // Display error message
                                    var errorMsg = response.data && response.data.message ? response.data.message : "Analysis failed";
                                    $("#analysis-results").html("<p>Error: " + errorMsg + "</p>");
                                }
                            },
                            error: function(xhr, status, error) {
                                var errorText = "Error connecting to the server";
                                if (status === "timeout") {
                                    errorText = "Request timeout - please try again";
                                } else if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message) {
                                    errorText = xhr.responseJSON.data.message;
                                }
                                $("#analysis-results").html("<p>" + errorText + "</p>");
                            }
                        });
                    }
                </script>
            </head>
            <body>
                ' . $office_addin_content . '
            </body>
            </html>';
            exit;
        }

        return $template;
    }

    /**
     * Get Office Add-in settings
     */
    public function get_office_addin_settings() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('get_settings');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for get_settings',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_send_json_error([
                'message' => $rate_check['error'],
                'rate_limit' => $rate_check
            ]);
            return;
        }
        
        // Verify nonce for security (only for authenticated requests)
        if (isset($_POST['nonce']) && !wp_verify_nonce($_POST['nonce'], 'office_addin_get_settings')) {
            wp_send_json_error(['message' => __('Security check failed', 'document-viewer-plugin')]);
            return;
        }
        
        try {
            // Try to get from cache first
            $cached_settings = $this->cache_manager->get_settings();
            if ($cached_settings !== false) {
                wp_send_json_success($cached_settings);
                return;
            }
            
            // Get the API settings (without exposing sensitive data)
            $api_key = get_option('document_viewer_api_key', '');
            $api_endpoint = get_option('document_viewer_api_endpoint', '');
            $model = get_option('document_viewer_model', '');

            // Return only non-sensitive settings
            $settings = [
                'model' => $model,
                'api_configured' => !empty($api_key) && !empty($api_endpoint),
                'endpoint_configured' => !empty($api_endpoint)
            ];
            
            // Cache the settings
            $this->cache_manager->set_settings($settings);
            
            wp_send_json_success($settings);
            
        } catch (Exception $e) {
            $this->error_reporter->report_error(
                'Exception in get_office_addin_settings: ' . $e->getMessage(),
                Office_Addin_Error_Reporter::TYPE_API_ERROR,
                Office_Addin_Error_Reporter::LEVEL_ERROR,
                ['exception' => $e->getTraceAsString()]
            );
            wp_send_json_error(['message' => __('Failed to load settings', 'document-viewer-plugin')]);
        }
    }

    /**
     * Get predefined queries
     */
    public function get_predefined_queries() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('get_queries');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for get_queries',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_send_json_error([
                'message' => $rate_check['error'],
                'rate_limit' => $rate_check
            ]);
            return;
        }
        
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'office_addin_get_settings')) {
            wp_send_json_error(['message' => __('Security check failed', 'document-viewer-plugin')]);
            return;
        }
        
        try {
            // Try to get from cache first
            $cached_queries = $this->cache_manager->get_queries();
            if ($cached_queries !== false) {
                wp_send_json_success($cached_queries);
                return;
            }
            
            global $wpdb;
            $table_name = $wpdb->prefix . 'document_preset_queries';

            // Check if table exists
            if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
                $this->error_reporter->report_error(
                    'Predefined queries table does not exist',
                    Office_Addin_Error_Reporter::TYPE_DATABASE_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_WARNING
                );
                wp_send_json_success([]);
                return;
            }

            // Get the predefined queries with prepared statement
            $queries = $wpdb->get_results(
                "SELECT id, title, query_text FROM $table_name ORDER BY id ASC",
                ARRAY_A
            );

            if ($wpdb->last_error) {
                $this->error_reporter->report_error(
                    'Database error retrieving predefined queries: ' . $wpdb->last_error,
                    Office_Addin_Error_Reporter::TYPE_DATABASE_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_ERROR
                );
                wp_send_json_error(['message' => __('Database error occurred', 'document-viewer-plugin')]);
                return;
            }

            if (empty($queries)) {
                wp_send_json_success([]);
                return;
            }

            // Cache the queries
            $this->cache_manager->set_queries($queries);
            
            // Return the queries
            wp_send_json_success($queries);
            
        } catch (Exception $e) {
            $this->error_reporter->report_error(
                'Exception in get_predefined_queries: ' . $e->getMessage(),
                Office_Addin_Error_Reporter::TYPE_API_ERROR,
                Office_Addin_Error_Reporter::LEVEL_ERROR,
                ['exception' => $e->getTraceAsString()]
            );
            wp_send_json_error(['message' => __('Failed to retrieve queries', 'document-viewer-plugin')]);
        }
    }

    /**
     * Analyze Excel data
     */
    public function analyze_excel_data() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('analyze_excel_data');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for analyze_excel_data',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_send_json_error([
                'message' => $rate_check['error'],
                'rate_limit' => $rate_check
            ]);
            return;
        }
        
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'office_addin_get_settings')) {
            wp_send_json_error(['message' => __('Security check failed', 'document-viewer-plugin')]);
            return;
        }
        
        try {
            // Get the request parameters
            $text = isset($_POST['text']) ? sanitize_textarea_field($_POST['text']) : '';
            $query_id = isset($_POST['query_id']) ? intval($_POST['query_id']) : 0;
            $custom_query = isset($_POST['custom_query']) ? sanitize_textarea_field($_POST['custom_query']) : '';

            if (empty($text)) {
                $this->error_reporter->report_error(
                    'No text provided for analysis',
                    Office_Addin_Error_Reporter::TYPE_VALIDATION_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_WARNING
                );
                wp_send_json_error(['message' => __('No text provided for analysis.', 'document-viewer-plugin')]);
                return;
            }

            // Get the query text
            $query_text = '';

            if (!empty($query_id)) {
                global $wpdb;
                $table_name = $wpdb->prefix . 'document_preset_queries';

                $query = $wpdb->get_row($wpdb->prepare("SELECT query_text FROM $table_name WHERE id = %d", $query_id), ARRAY_A);

                if ($query) {
                    $query_text = $query['query_text'];
                } else {
                    $this->error_reporter->report_error(
                        'Predefined query not found: ID ' . $query_id,
                        Office_Addin_Error_Reporter::TYPE_VALIDATION_ERROR,
                        Office_Addin_Error_Reporter::LEVEL_WARNING,
                        ['query_id' => $query_id]
                    );
                    wp_send_json_error(['message' => __('Query not found.', 'document-viewer-plugin')]);
                    return;
                }
            } elseif (!empty($custom_query)) {
                $query_text = $custom_query;
            } else {
                $this->error_reporter->report_error(
                    'No query provided for analysis',
                    Office_Addin_Error_Reporter::TYPE_VALIDATION_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_WARNING
                );
                wp_send_json_error(['message' => __('No query provided for analysis.', 'document-viewer-plugin')]);
                return;
            }

            // Check cache for existing analysis
            $text_hash = $this->cache_manager->generate_text_hash($text);
            $query_hash = $this->cache_manager->generate_query_hash($query_text);
            
            $cached_result = $this->cache_manager->get_analysis_result($text_hash, $query_hash);
            if ($cached_result !== false) {
                wp_send_json_success(['result' => $cached_result['response']]);
                return;
            }

            // Process the document analysis using the Document_Analyzer class
            $document_analyzer = new Document_Analyzer();
            $result = $document_analyzer->analyze_text($text, $query_text);

            // Return the analysis result
            if ($result['success']) {
                // Cache the successful result
                $this->cache_manager->set_analysis_result($text_hash, $query_hash, $result);
                
                wp_send_json_success(['result' => $result['response']]);
            } else {
                $this->error_reporter->report_error(
                    'Document analysis failed: ' . $result['error'],
                    Office_Addin_Error_Reporter::TYPE_API_ERROR,
                    Office_Addin_Error_Reporter::LEVEL_ERROR,
                    ['text_length' => strlen($text), 'query_length' => strlen($query_text)]
                );
                wp_send_json_error(['message' => $result['error']]);
            }
            
        } catch (Exception $e) {
            $this->error_reporter->report_error(
                'Exception in analyze_excel_data: ' . $e->getMessage(),
                Office_Addin_Error_Reporter::TYPE_API_ERROR,
                Office_Addin_Error_Reporter::LEVEL_ERROR,
                ['exception' => $e->getTraceAsString()]
            );
            wp_send_json_error(['message' => __('Analysis failed due to an unexpected error', 'document-viewer-plugin')]);
        }
    }
    
    /**
     * Returns the preview script for Office Add-in
     */
    public function get_preview_script() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('get_preview_script');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for get_preview_script',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_send_json_error([
                'message' => $rate_check['error'],
                'rate_limit' => $rate_check
            ]);
            return;
        }
        
        // Verify nonce for security (only for authenticated requests)
        if (isset($_POST['nonce']) && !wp_verify_nonce($_POST['nonce'], 'office_addin_get_settings')) {
            wp_send_json_error(['message' => __('Security check failed', 'document-viewer-plugin')]);
            return;
        }
        
        header('Content-Type: application/javascript');
        ?>
        // Preview Script Helper Functions
        window.updatePreviewHelper = function() {
            if (typeof window.updatePreview === 'function') {
                window.updatePreview();
            } else {
                console.error('updatePreview function not available');
                // Try to find it in parent window
                if (window.parent && typeof window.parent.updatePreview === 'function') {
                    window.parent.updatePreview();
                } else {
                    console.error('updatePreview function not available in parent window either');
                }
            }
        };
        
        // Make sure jQuery is available
        window.loadjQuery = function(callback) {
            if (typeof jQuery === 'undefined') {
                var script = document.createElement('script');
                script.src = 'https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js';
                script.onload = callback;
            } else {
                callback();
            }
        };
        
        // API Connection methods using balance-sheet widget pattern
        window.ajaxUrl = "<?php echo admin_url('admin-ajax.php'); ?>";
        window.apiSettingsNonce = "<?php echo wp_create_nonce('office_addin_get_settings'); ?>";
        
        // AI Status check function using balance-sheet widget method
        window.checkAIStatus = function() {
            console.log("Checking AI connection status...");

            $.ajax({
                url: window.ajaxUrl,
                type: "POST",
                data: {
                    action: "check_ai_status",
                    nonce: window.apiSettingsNonce
                },
                success: function(response) {
                    if (response.success) {
                        window.updateAIStatusIndicator("connected", response.data.message || "AI connected and ready");
                    } else {
                        window.updateAIStatusIndicator("error", response.data || "AI connection error");
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AI status check failed:", error);
                    window.updateAIStatusIndicator("error", "Unable to verify AI connection");
                }
            });
        };
        
        // Update AI status indicator function using balance-sheet widget method
        window.updateAIStatusIndicator = function(status, message) {
            console.log("Updating AI status indicator:", {
                status: status,
                message: message
            });

            const $indicator = $("#api-status");
            if ($indicator.length === 0) {
                console.error("API status indicator element not found!");
                return;
            }

            let statusText, className;
            switch (status) {
                case "connected":
                    statusText = "Connected";
                    className = "ai-status-connected";
                    break;
                case "checking":
                    statusText = "Checking...";
                    className = "ai-status-checking";
                    break;
                case "error":
                    statusText = "Not Connected";
                    className = "ai-status-error";
                    break;
                default:
                    statusText = "Unknown";
                    className = "ai-status-error";
            }

            // Remove previous classes and add new one
            $indicator.removeClass("ai-status-checking ai-status-connected ai-status-error")
                     .addClass(className)
                     .text(statusText);
                     
            console.log("AI status indicator updated:", statusText);
        };
        <?php
        exit;
    }
    
    /**
     * Preview content for Office Add-in
     */
    public function preview_content() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('preview_content');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for preview_content',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_die(__('Rate limit exceeded. Please try again later.', 'document-viewer-plugin'));
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized', 'document-viewer-plugin'));
        }
        
        // Verify nonce for security if provided
        if (isset($_GET['nonce']) && !wp_verify_nonce($_GET['nonce'], 'office_addin_preview')) {
            wp_die(__('Security check failed', 'document-viewer-plugin'));
            return;
        }
        
        try {
            $content = isset($_GET['content']) ? wp_kses_post(urldecode($_GET['content'])) : get_option('office_addin_content', '');
            
            // Get default CSS if none exists
            $css = '';
            if (class_exists('Document_Viewer_Settings')) {
                $settings = new Document_Viewer_Settings();
                if (method_exists($settings, 'get_default_office_addin_css')) {
                    $css = $settings->get_default_office_addin_css();
                }
            }
            
            if (empty($css)) {
                $css = 'body { font-family: "Segoe UI", Arial, sans-serif; margin: 15px; }';
            }
            
            header('Content-Type: text/html; charset=utf-8');
            echo '<!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Office Add-in Preview</title>
                <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
                <style>' . esc_html($css) . '</style>
                <script>
                    // Load preview script with API connection methods
                    $(document).ready(function() {
                        // Load the preview script that includes API connection methods
                        $.getScript("' . admin_url('admin-ajax.php') . '?action=office_addin_preview_script")
                            .done(function() {
                                console.log("Preview script loaded successfully");
                                // Initialize AI status check using balance-sheet widget method
                                if (typeof window.checkAIStatus === "function") {
                                    window.checkAIStatus();
                                }
                            })
                            .fail(function() {
                                console.error("Failed to load preview script");
                            });
                    });
                </script>
            </head>
            <body>
                ' . $content . '
            </body>
            </html>';
            
        } catch (Exception $e) {
            $this->error_reporter->report_error(
                'Exception in preview_content: ' . $e->getMessage(),
                Office_Addin_Error_Reporter::TYPE_API_ERROR,
                Office_Addin_Error_Reporter::LEVEL_ERROR,
                ['exception' => $e->getTraceAsString()]
            );
            wp_die(__('Preview failed due to an unexpected error', 'document-viewer-plugin'));
        }
        
        exit;
    }
    
    /**
     * Check AI status using balance-sheet widget method
     */
    public function check_ai_status() {
        // Check rate limiting
        $rate_check = $this->rate_limiter->is_request_allowed('check_ai_status');
        if (!$rate_check['allowed']) {
            $this->error_reporter->report_error(
                'Rate limit exceeded for check_ai_status',
                Office_Addin_Error_Reporter::TYPE_RATE_LIMIT_ERROR,
                Office_Addin_Error_Reporter::LEVEL_WARNING,
                ['remaining' => $rate_check['remaining'], 'reset_time' => $rate_check['reset_time']]
            );
            wp_send_json_error([
                'message' => $rate_check['error'],
                'rate_limit' => $rate_check
            ]);
            return;
        }
        
        // Verify nonce for security (using same nonce as other office addin functions)
        if (isset($_POST['nonce']) && !wp_verify_nonce($_POST['nonce'], 'office_addin_get_settings')) {
            wp_send_json_error(['message' => __('Security check failed', 'document-viewer-plugin')]);
            return;
        }
        
        try {
            // Get the API settings to check if they are configured
            $api_key = get_option('document_viewer_api_key', '');
            $api_endpoint = get_option('document_viewer_api_endpoint', '');
            $model = get_option('document_viewer_model', '');

            // Check if all required settings are present
            if (empty($api_key) || empty($api_endpoint) || empty($model)) {
                wp_send_json_error([
                    'message' => __('API not configured. Please check settings.', 'document-viewer-plugin')
                ]);
                return;
            }

            // All settings are present, so API is configured and ready
            wp_send_json_success([
                'message' => __('AI connected and ready', 'document-viewer-plugin'),
                'api_configured' => true,
                'model' => $model
            ]);
            
        } catch (Exception $e) {
            $this->error_reporter->report_error(
                'Exception in check_ai_status: ' . $e->getMessage(),
                Office_Addin_Error_Reporter::TYPE_API_ERROR,
                Office_Addin_Error_Reporter::LEVEL_ERROR,
                ['exception' => $e->getTraceAsString()]
            );
            wp_send_json_error(['message' => __('Failed to check AI status', 'document-viewer-plugin')]);
        }
    }
}

// Initialize the Office Add-in
new Office_Addin();
