<?php
/**
 * Input Validator Class
 * 
 * Provides enhanced input validation and sanitization for Office Add-in
 * 
 * @package Financial_Advisor_V4
 * @subpackage Security
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Input_Validator {
    
    /**
     * Allowed HTML tags for Office Add-in content
     * @var array
     */
    private $allowed_office_tags = [
        'div' => [
            'class' => [],
            'id' => [],
            'style' => []
        ],
        'h1' => [],
        'h2' => [],
        'h3' => [],
        'h4' => [],
        'h5' => [],
        'h6' => [],
        'p' => ['class' => []],
        'span' => ['class' => [], 'id' => []],
        'br' => [],
        'hr' => [],
        'button' => [
            'class' => [],
            'id' => [],
            'type' => []
        ],
        'input' => [
            'type' => [],
            'class' => [],
            'id' => [],
            'placeholder' => [],
            'value' => []
        ],
        'select' => [
            'class' => [],
            'id' => []
        ],
        'option' => [
            'value' => []
        ],
        'label' => [
            'for' => []
        ],
        'pre' => [],
        'code' => [],
        'strong' => [],
        'em' => [],
        'ul' => [],
        'ol' => [],
        'li' => [],
        'table' => ['class' => []],
        'tr' => [],
        'td' => [],
        'th' => []
    ];
    
    /**
     * Maximum content length
     * @var int
     */
    private $max_content_length = 1048576; // 1MB
    
    /**
     * Constructor
     */
    public function __construct() {
        // Allow filtering of allowed tags
        $this->allowed_office_tags = apply_filters('office_addin_allowed_html_tags', $this->allowed_office_tags);
        $this->max_content_length = apply_filters('office_addin_max_content_length', $this->max_content_length);
    }
    
    /**
     * Sanitize Office Add-in content
     */
    public function sanitize_office_content($content) {
        if (empty($content)) {
            return '';
        }
        
        // Check content length
        if (strlen($content) > $this->max_content_length) {
            throw new InvalidArgumentException('Content exceeds maximum allowed length');
        }
        
        // Remove dangerous script tags and attributes
        $content = $this->remove_dangerous_content($content);
        
        // Sanitize with WordPress kses
        $content = wp_kses($content, $this->allowed_office_tags);
        
        // Additional sanitization
        $content = $this->additional_sanitization($content);
        
        return $content;
    }
    
    /**
     * Validate file upload
     */
    public function validate_file_upload($file) {
        if (!is_array($file) || !isset($file['tmp_name'])) {
            throw new InvalidArgumentException('Invalid file upload');
        }
        
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new InvalidArgumentException('File upload error: ' . $this->get_upload_error_message($file['error']));
        }
        
        // Validate file type
        $this->validate_file_type($file);
        
        // Validate file size
        $this->validate_file_size($file);
        
        // Validate file content
        $this->validate_file_content($file);
        
        return true;
    }
    
    /**
     * Sanitize text input
     */
    public function sanitize_text_input($input, $max_length = 1000) {
        if (empty($input)) {
            return '';
        }
        
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        // Trim whitespace
        $input = trim($input);
        
        // Limit length
        if (strlen($input) > $max_length) {
            $input = substr($input, 0, $max_length);
        }
        
        // Sanitize
        $input = sanitize_text_field($input);
        
        return $input;
    }
    
    /**
     * Validate email address
     */
    public function validate_email($email) {
        $email = sanitize_email($email);
        
        if (!is_email($email)) {
            throw new InvalidArgumentException('Invalid email address');
        }
        
        return $email;
    }
    
    /**
     * Validate URL
     */
    public function validate_url($url) {
        $url = esc_url_raw($url);
        
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new InvalidArgumentException('Invalid URL');
        }
        
        // Check for allowed protocols
        $allowed_protocols = ['http', 'https'];
        $protocol = parse_url($url, PHP_URL_SCHEME);
        
        if (!in_array($protocol, $allowed_protocols)) {
            throw new InvalidArgumentException('Invalid URL protocol');
        }
        
        return $url;
    }
    
    /**
     * Validate numeric input
     */
    public function validate_numeric($value, $min = null, $max = null) {
        if (!is_numeric($value)) {
            throw new InvalidArgumentException('Value must be numeric');
        }
        
        $value = floatval($value);
        
        if ($min !== null && $value < $min) {
            throw new InvalidArgumentException("Value must be at least {$min}");
        }
        
        if ($max !== null && $value > $max) {
            throw new InvalidArgumentException("Value must be at most {$max}");
        }
        
        return $value;
    }
    
    /**
     * Remove dangerous content
     */
    private function remove_dangerous_content($content) {
        // Remove script tags
        $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $content);
        
        // Remove javascript: URLs
        $content = preg_replace('/javascript:/i', '', $content);
        
        // Remove vbscript: URLs
        $content = preg_replace('/vbscript:/i', '', $content);
        
        // Remove data: URLs (except images)
        $content = preg_replace('/data:(?!image)/i', '', $content);
        
        // Remove dangerous attributes
        $dangerous_attrs = ['onload', 'onerror', 'onclick', 'onmouseover', 'onfocus', 'onblur'];
        foreach ($dangerous_attrs as $attr) {
            $content = preg_replace('/' . $attr . '\s*=\s*["\'][^"\']*["\']/i', '', $content);
        }
        
        return $content;
    }
    
    /**
     * Additional sanitization
     */
    private function additional_sanitization($content) {
        // Remove excessive whitespace
        $content = preg_replace('/\s+/', ' ', $content);
        
        // Remove empty paragraphs
        $content = preg_replace('/<p[^>]*>\s*<\/p>/i', '', $content);
        
        // Remove empty divs
        $content = preg_replace('/<div[^>]*>\s*<\/div>/i', '', $content);
        
        return trim($content);
    }
    
    /**
     * Validate file type
     */
    private function validate_file_type($file) {
        $allowed_types = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.ms-excel', // .xls
            'text/csv', // .csv
            'application/json' // .json
        ];
        
        $file_type = $file['type'];
        
        // Also check file extension
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['xlsx', 'xls', 'csv', 'json'];
        
        if (!in_array($file_type, $allowed_types) || !in_array($file_extension, $allowed_extensions)) {
            throw new InvalidArgumentException('Invalid file type. Only Excel, CSV, and JSON files are allowed.');
        }
        
        // Verify MIME type matches extension
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detected_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if ($detected_type !== $file_type) {
            throw new InvalidArgumentException('File type mismatch detected');
        }
    }
    
    /**
     * Validate file size
     */
    private function validate_file_size($file) {
        $max_size = 10 * 1024 * 1024; // 10MB
        $max_size = apply_filters('office_addin_max_file_size', $max_size);
        
        if ($file['size'] > $max_size) {
            $max_size_mb = $max_size / (1024 * 1024);
            throw new InvalidArgumentException("File size exceeds maximum allowed size of {$max_size_mb}MB");
        }
    }
    
    /**
     * Validate file content
     */
    private function validate_file_content($file) {
        // Check for malicious content in file headers
        $handle = fopen($file['tmp_name'], 'rb');
        if (!$handle) {
            throw new InvalidArgumentException('Unable to read file');
        }
        
        $header = fread($handle, 1024);
        fclose($handle);
        
        // Check for script tags in file content
        if (preg_match('/<script/i', $header)) {
            throw new InvalidArgumentException('File contains potentially malicious content');
        }
        
        // Check for PHP tags
        if (preg_match('/<\?php/i', $header)) {
            throw new InvalidArgumentException('File contains potentially malicious content');
        }
    }
    
    /**
     * Validate request data for AJAX calls
     * 
     * @param array $data Request data to validate
     * @return array Validation result with 'valid' boolean and 'reason' if invalid
     */
    public function validate_request_data($data) {
        $result = ['valid' => true, 'reason' => ''];
        
        if (!is_array($data)) {
            return ['valid' => false, 'reason' => 'Invalid data format'];
        }
        
        // Check for common injection patterns
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Check for SQL injection patterns
                if (preg_match('/(\bunion\b|\bselect\b|\binsert\b|\bdelete\b|\bdrop\b|\btable\b)/i', $value)) {
                    return ['valid' => false, 'reason' => 'Potential SQL injection detected'];
                }
                
                // Check for XSS patterns
                if (preg_match('/<script[^>]*>|javascript:|on\w+\s*=/i', $value)) {
                    return ['valid' => false, 'reason' => 'Potential XSS attempt detected'];
                }
                
                // Check for excessively long strings (potential buffer overflow)
                if (strlen($value) > 10000) {
                    return ['valid' => false, 'reason' => 'Input too long'];
                }
            }
        }
        
        return $result;
    }
    
    /**
     * Get upload error message
     */
    private function get_upload_error_message($error_code) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize directive';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE directive';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }
}
