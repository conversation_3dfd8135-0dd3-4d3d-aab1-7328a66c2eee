# Translation of Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) in Italian
# This file is distributed under the same license as the Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-05-07 16:49:56+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: it\n"
"Project-Id-Version: Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release)\n"

#: class-akismet-compatible-plugins.php:86
msgid "Error getting compatible plugins."
msgstr "Errore durante il recupero dei plugin compatibili."

#: views/notice.php:70
msgid "Upgrade plan"
msgstr "Migliora il tuo piano"

#. translators: The placeholder is a URL to the contact form.
#: views/notice.php:64
msgid "If you believe your site should not be classified as commercial, <a href=\"%s\">please get in touch</a>."
msgstr "Se credi che il tuo sito non debba essere classificato come commerciale, <a href=\"%s\">contattaci</a>."

#. translators: The placeholder is a URL.
#: views/notice.php:58
msgid "Your current subscription is for <a href=\"%s\">personal, non-commercial use</a>. Please upgrade your plan to continue using Akismet."
msgstr "Il tuo abbonamento attuale è per <a href=\"%s\">uso personale, non commerciale</a>. Aggiorna il tuo piano per continuare a usare Akismet."

#: views/notice.php:54
msgid "We detected commercial activity on your site"
msgstr "Abbiamo rilevato attività commerciale sul tuo sito"

#: views/notice.php:27
msgid "Almost done! Configure Akismet and say goodbye to spam"
msgstr "Quasi finito! Configura Akismet e dì addio allo spam"

#: class.akismet-admin.php:761
msgid "This comment was not sent to Akismet when it was submitted because it was caught by the comment disallowed list."
msgstr "Questo commento non è stato inviato ad Akismet perché è stato intercettato dall'elenco dei commenti non ammessi."

#: class.akismet-admin.php:758
msgid "This comment was not sent to Akismet when it was submitted because it was caught by something else."
msgstr "Questo commento non è stato inviato ad Akismet perché è stato intercettato da qualcos'altro."

#. translators: the placeholder is the URL to the Akismet pricing page.
#: views/notice.php:180
msgid "Please <a href=\"%s\" target=\"_blank\">choose a plan</a> to get started with Akismet."
msgstr "<a href=\"%s\" target=\"_blank\">Scegli un piano</a> per iniziare a lavorare con Akismet."

#: views/notice.php:176
msgid "Your API key must have an Akismet plan before it can protect your site from spam."
msgstr "La tua chiave API deve disporre di un piano Akismet per poter proteggere il sito dallo spam."

#: class.akismet-rest-api.php:509
msgid "Multiple comments matched request."
msgstr "Richiesta di più commenti corrispondenti."

#: class.akismet-rest-api.php:499
msgid "Could not find matching comment."
msgstr "Impossibile trovare commento corrispondente."

#: class.akismet-rest-api.php:457
msgid "The 'comments' parameter must be an array."
msgstr "Il parametro \"commenti\" deve essere una matrice."

#: class.akismet-admin.php:755
msgid "Akismet cleared this comment during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "Akismet ha ripulito questo commento durante una verifica. Non ha aggiornato lo stato del commento perché era già stato modificato da un altro utente o plugin."

#: class.akismet-admin.php:752
msgid "Akismet determined this comment was spam during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "Akismet ha contrassegnato questo commento come spam durante una verifica. Non ha aggiornato lo stato del commento perché era già stato modificato da un altro utente o plugin."

#: class.akismet-admin.php:749
msgid "Akismet cleared this comment and updated its status via webhook."
msgstr "Akismet ha cancellato questo commento e aggiornato il suo stato tramite webhook."

#: class.akismet-admin.php:746
msgid "Akismet caught this comment as spam and updated its status via webhook."
msgstr "Akismet ha segnalato questo commento come spam e aggiornato il suo stato tramite webhook."

#: views/notice.php:198
msgid "Akismet is now protecting your site from spam."
msgstr "Akismet adesso protegge il tuo sito dallo spam."

#: views/config.php:304
msgid "Account overview"
msgstr "Panoramica dell'account"

#. translators: %1$s: spam folder link, %2$d: delete interval in days
#: views/config.php:192
msgid "Spam in the %1$s older than %2$d day is deleted automatically."
msgid_plural "Spam in the %1$s older than %2$d days is deleted automatically."
msgstr[0] "Lo spam più vecchio di %2$d giorno nella %1$s viene automaticamente eliminato."
msgstr[1] "Lo spam più vecchio di %2$d giorni nella %1$s viene automaticamente eliminato."

#: views/config.php:187
msgid "spam folder"
msgstr "cartella dello spam"

#: views/stats.php:11
msgid "Akismet detailed stats"
msgstr "Statistiche Akismet dettagliate"

#: views/stats.php:6
msgid "Back to settings"
msgstr "Torna alle impostazioni"

#: views/config.php:268
msgid "Subscription type"
msgstr "Tipo di abbonamento"

#: views/config.php:232
msgid "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms."
msgstr "Per aiutare il tuo sito a mantenere la trasparenza e a seguire le norme in materia di privacy come il GDPR, Akismet può mostrare un avviso sotto i moduli di commento."

#: views/config.php:154
msgid "Spam filtering"
msgstr "Filtro per lo spam"

#: views/config.php:94 views/enter.php:9
msgid "API key"
msgstr "Chiave API"

#: views/config.php:44
msgid "Akismet stats"
msgstr "Statistiche Akismet"

#. Author of the plugin
#: akismet.php
msgid "Automattic - Anti-spam Team"
msgstr "Automattic - Squadra antispam"

#. Plugin Name of the plugin
#: akismet.php
msgid "Akismet Anti-spam: Spam Protection"
msgstr "Akismet Anti-spam: Spam Protection"

#: views/notice.php:47
msgid "WP-Cron has been disabled using the DISABLE_WP_CRON constant. Comment rechecks may not work properly."
msgstr "WP-Cron è stato disabilitato usando la costante DISABLE_WP_CRON. Le riverifiche dei commenti potrebbero non funzionare correttamente."

#. translators: %1$s is a human-readable time difference, like "3 hours ago",
#. and %2$s is an already-translated phrase describing how a comment's status
#. changed, like "This comment was reported as spam."
#: class.akismet-admin.php:793
msgid "%1$s - %2$s"
msgstr "%1$s - %2$s"

#: views/get.php:17
msgid "(opens in a new tab)"
msgstr "(si apre in una nuova scheda)"

#. translators: The placeholder is the name of a subscription level, like
#. "Plus" or "Enterprise" .
#: views/notice.php:341
msgid "Upgrade to %s"
msgstr "Aggiorna a %s"

#: views/notice.php:336
msgid "Upgrade your subscription level"
msgstr "Aggiorna il livello del tuo abbonamento"

#: views/notice.php:293 views/notice.php:301 views/notice.php:309
#: views/notice.php:318
msgid "Learn more about usage limits."
msgstr "Scopri di più sui limiti di utilizzo."

#. translators: The first placeholder is a date, the second is a (formatted)
#. number, the third is another formatted number.
#: views/notice.php:285
msgid "Since %1$s, your account made %2$s API calls, compared to your plan&#8217;s limit of %3$s."
msgstr "Da %1$s, il tuo account ha effettuato %2$s chiamate API, rispetto al limite del tuo piano di %3$s."

#: views/notice.php:315
msgid "Your Akismet usage has been over your plan&#8217;s limit for three consecutive months. We have restricted your account for the rest of the month. Upgrade your plan so Akismet can continue blocking spam."
msgstr "Hai superato il limite consentito dal tuo piano Akismet per tre mesi consecutivi. Abbiamo limitato il tuo account per il resto del mese. Aggiorna il tuo piano in modo che Akismet possa continuare a bloccare lo spam."

#: views/notice.php:306
msgid "Your Akismet usage is nearing your plan&#8217;s limit for the third consecutive month. We will restrict your account after you reach the limit. Upgrade your plan so Akismet can continue blocking spam."
msgstr "Sei vicino al limite consentito dal tuo piano per il terzo mese consecutivo. Limiteremo il tuo account una volta raggiunto il tetto massimo. Aggiorna il tuo piano in modo che Akismet possa continuare a bloccare lo spam."

#: views/notice.php:298
msgid "Your Akismet usage has been over your plan&#8217;s limit for two consecutive months. Next month, we will restrict your account after you reach the limit. Please consider upgrading your plan."
msgstr "Hai superato il limite consentito dal tuo piano Akismet per due mesi consecutivi. Il mese prossimo, limiteremo il tuo account una volta raggiunto il tetto massimo. Considera il passaggio a un piano superiore."

#: views/notice.php:272
msgid "Your account has been restricted"
msgstr "Il tuo account è stato limitato"

#: views/notice.php:268
msgid "Your Akismet account usage is approaching your plan&#8217;s limit"
msgstr "Sei vicino al limite di utilizzo consentito dal tuo piano Akismet"

#: views/notice.php:265
msgid "Your Akismet account usage is over your plan&#8217;s limit"
msgstr "Hai superato il limite di utilizzo consentito dal tuo piano Akismet"

#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:228
msgid "Please enter a new key or <a href=\"%s\" target=\"_blank\">contact Akismet support</a>."
msgstr "Inserisci una nuova chiave o <a href=\"%s\" target=\"_blank\">contatta l'assistenza Akismet</a>."

#: views/notice.php:222
msgid "Your API key is no longer valid."
msgstr "La tua chiave API non è più valida."

#. translators: The placeholder is for showing how much of the process has
#. completed, as a percent. e.g., "Checking for Spam (40%)"
#: class.akismet-admin.php:481
msgid "Checking for Spam (%1$s%)"
msgstr "Controllo dello spam (%1$s%)"

#: class.akismet-admin.php:809
msgid "No comment history."
msgstr "Nessuna cronologia dei commenti."

#: class.akismet-admin.php:742
msgid "Akismet was unable to recheck this comment."
msgstr "Akismet non ha potuto ricontrollare questo commento."

#: class.akismet-admin.php:734
msgid "Akismet was unable to check this comment but will automatically retry later."
msgstr "Akismet non ha potuto controllare questo commento ma proverà in automatico più tardi."

#. translators: The placeholder is a WordPress PHP function name.
#: class.akismet-admin.php:703
msgid "Comment was caught by %s."
msgstr "Il commento è stato preso da %s."

#: class.akismet.php:802
msgid "Akismet is not configured. Please enter an API key."
msgstr "Akismet non è configurato. Inserisci una chiave API."

#: views/enter.php:7
msgid "Enter your API key"
msgstr "Inserisci la tua chiave API"

#: views/connect-jp.php:92
msgid "Set up a different account"
msgstr "Configura un account diverso"

#: views/setup.php:2
msgid "Set up your Akismet account to enable spam filtering on this site."
msgstr "Configura il tuo account Akismet per abilitare il filtro spam su questo sito web."

#: class.akismet-admin.php:1332
msgid "Akismet could not recheck your comments for spam."
msgstr "Akismet non ha potuto ricontrollare lo spam nei tuoi commenti."

#: class.akismet-admin.php:514
msgid "You don&#8217;t have permission to do that."
msgstr "Non hai l'autorizzazione per fare questo."

#: class.akismet-cli.php:167
msgid "Stats response could not be decoded."
msgstr "La risposta delle statistiche non può essere decodificata."

#: class.akismet-cli.php:161
msgid "Currently unable to fetch stats. Please try again."
msgstr "Attualmente non è in grado di recuperare le statistiche. Riprova."

#: class.akismet-cli.php:135
msgid "API key must be set to fetch stats."
msgstr "La chiave API deve essere impostata per recuperare le statistiche."

#: views/config.php:225
msgid "Do not display privacy notice."
msgstr "Non visualizzare l'informativa sulla privacy."

#: views/config.php:217
msgid "Display a privacy notice under your comment forms."
msgstr "Visualizza l'informativa sulla privacy nei form dei commenti."

#: views/config.php:211
msgid "Akismet privacy notice"
msgstr "Informativa sulla privacy di Akismet"

#: views/config.php:206
msgid "Privacy"
msgstr "Privacy"

#. translators: %s: Akismet privacy URL
#: class.akismet.php:1917
msgid "This site uses Akismet to reduce spam. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Learn how your comment data is processed.</a>"
msgstr "Questo sito utilizza Akismet per ridurre lo spam. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Scopri come vengono elaborati i dati derivati dai commenti</a>."

#: class.akismet-admin.php:108
msgid "We collect information about visitors who comment on Sites that use our Akismet Anti-spam service. The information we collect depends on how the User sets up Akismet for the Site, but typically includes the commenter's IP address, user agent, referrer, and Site URL (along with other information directly provided by the commenter such as their name, username, email address, and the comment itself)."
msgstr "Raccogliamo informazioni sui visitatori che lasciano commenti sui siti che utilizzano il servizio antispam di Akismet. Le informazioni che raccogliamo dipendono da come l'utente ha configurato Akismet per il sito, ma solitamente comprendono l'indirizzo IP del commentatore, lo user agent, il referrer e l'URL del sito (insieme ad altre informazioni fornite direttamente dal commentatore come il nome, il nome utente, l'indirizzo e-mail e il commento stesso)."

#: class.akismet.php:430
msgid "Comment discarded."
msgstr "Commento scartato."

#: class.akismet-rest-api.php:206
msgid "This site's API key is hardcoded and cannot be deleted."
msgstr "La chiave API di questo sito è scritta direttamente nel codice sorgente e non può essere eliminata."

#: class.akismet-rest-api.php:190
msgid "The value provided is not a valid and registered API key."
msgstr "Il valore fornito non corrisponde ad una chiave API valida e registrata."

#: class.akismet-rest-api.php:184
msgid "This site's API key is hardcoded and cannot be changed via the API."
msgstr "La API key di questo sito è scritta direttamente nel codice sorgente e non può essere modificata tramite API."

#: class.akismet-rest-api.php:84 class.akismet-rest-api.php:97
msgid "The time period for which to retrieve stats. Options: 60-days, 6-months, all"
msgstr "Lasso di tempo per recuperare le statistiche. Opzioni: 60-giorni, 6-mesi, tutti"

#: class.akismet-rest-api.php:65
msgid "If true, show the number of approved comments beside each comment author in the comments list page."
msgstr "Se è impostato su \"true\", mostra nella pagina dei commenti il numero di commenti approvati accanto ad ogni autore. "

#: class.akismet-rest-api.php:60
msgid "If true, Akismet will automatically discard the worst spam automatically rather than putting it in the spam folder."
msgstr "Se impostato su true, Akismet scarterà automaticamente lo spam peggiore piuttosto che spostarlo nella cartella di spam."

#: class.akismet-rest-api.php:31 class.akismet-rest-api.php:122
#: class.akismet-rest-api.php:135 class.akismet-rest-api.php:148
msgid "A 12-character Akismet API key. Available at akismet.com/get/"
msgstr "Una API key di Akismet di 12 caratteri. Disponibile su akismet.com/get/"

#: views/notice.php:109
msgid "Your site can&#8217;t connect to the Akismet servers."
msgstr "Il tuo sito non riesce a connettersi ai server di Akismet."

#. translators: %s is the wp-config.php file
#: views/predefined.php:7
msgid "An Akismet API key has been defined in the %s file for this site."
msgstr "Una chiave API di Akismet è stata definita nel file %s di questo sito."

#: views/predefined.php:2
msgid "Manual Configuration"
msgstr "Configurazione manuale"

#: class.akismet-admin.php:275
msgid "On this page, you are able to update your Akismet settings and view spam stats."
msgstr "In questa pagina, puoi aggiornare le tue impostazioni di Akismet e vedere le statistiche dello spam."

#. Description of the plugin
#: akismet.php
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Akismet Anti-spam keeps your site protected even while you sleep. To get started: activate the Akismet plugin and then go to your Akismet Settings page to set up your API key."
msgstr "Usato da milioni di persone, Akismet è probabilmente il miglior modo al mondo per <strong>proteggere il tuo blog dallo spam</strong>. Akismet Anti-spam protegge il tuo sito anche mentre dormi. Per iniziare: attiva il plugin e poi vai alla pagina delle Impostazioni di Akismet per impostare la tua chiave API."

#: class.akismet-admin.php:135 class.akismet-admin.php:137
msgid "Akismet Anti-spam"
msgstr "Akismet Anti-spam"

#: views/enter.php:10
msgid "Connect with API key"
msgstr "Collega con la chiave API"

#. translators: %s is the WordPress.com username
#: views/connect-jp.php:25 views/connect-jp.php:79
msgid "You are connected as %s."
msgstr "Sei collegato come %s."

#: views/connect-jp.php:10 views/connect-jp.php:18 views/connect-jp.php:38
#: views/connect-jp.php:72 views/connect-jp.php:91
msgid "Connect with Jetpack"
msgstr "Collega con Jetpack"

#: views/connect-jp.php:12 views/connect-jp.php:32 views/connect-jp.php:67
msgid "Use your Jetpack connection to set up Akismet."
msgstr "Usa il tuo collegamento Jetpack per impostare Akismet."

#: views/title.php:2
msgid "Eliminate spam from your site"
msgstr "Elimina lo spam dal tuo sito"

#. translators: The placeholder is a URL for checking pending comments.
#: views/notice.php:205
msgid "Would you like to <a href=\"%s\">check pending comments</a>?"
msgstr "Vuoi <a href=\"%s\">controllare i commenti in attesa</a>?"

#: views/notice.php:25
msgid "Set up your Akismet account"
msgstr "Imposta il tuo account Akismet"

#: views/config.php:36
msgid "Detailed stats"
msgstr "Statistiche dettagliate"

#: views/config.php:31
msgid "Statistics"
msgstr "Statistiche"

#: class.akismet-admin.php:1448
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started, just go to <a href=\"admin.php?page=akismet-key-config\">your Akismet Settings page</a> to set up your API key."
msgstr "Usato da milioni di persone, Akismet è probabilmente il miglior modo al mondo di <strong>proteggere il tuo blog dallo spam</strong>. Protegge il tuo sito anche mentre dormi. Per iniziare vai alla <a href=\"admin.php?page=akismet-key-config\">pagina delle Impostazioni di Akismet</a> per impostare la tua chiave API."

#: class.akismet-admin.php:1446
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Your site is fully configured and being protected, even while you sleep."
msgstr "Usato da milioni di persone, Akismet è probabilmente il miglior modo al mondo per <strong>proteggere il tuo blog dallo spam</strong>. Il tuo sito è configurato e protetto completamente, anche mentre dormi. "

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1326
msgid "%s comment was caught as spam."
msgid_plural "%s comments were caught as spam."
msgstr[0] "%s commento è stato segnato come spam."
msgstr[1] "%s commenti sono stati segnati come spam."

#: class.akismet-admin.php:1323
msgid "No comments were caught as spam."
msgstr "Nessun commento è stato segnato come spam. "

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1319
msgid "Akismet checked %s comment."
msgid_plural "Akismet checked %s comments."
msgstr[0] "Akismet ha controllato %s commento."
msgstr[1] "Akismet ha controllato %s commenti."

#: class.akismet-admin.php:1316
msgid "There were no comments to check. Akismet will only check comments awaiting moderation."
msgstr "Non c'erano commenti da controllare. Akismet controlla solo i commenti in attesa di moderazione."

#: class.akismet.php:808
msgid "Comment not found."
msgstr "Non è stato trovato il commento."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:89
msgid "%d comment could not be checked."
msgid_plural "%d comments could not be checked."
msgstr[0] "%d commento non può essere verificato."
msgstr[1] "%d commenti non possono essere verificati."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:85
msgid "%d comment moved to Spam."
msgid_plural "%d comments moved to Spam."
msgstr[0] "%d commento è stato spostato nello Spam."
msgstr[1] "%d commenti sono stati spostati nello Spam."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:82
msgid "Processed %d comment."
msgid_plural "Processed %d comments."
msgstr[0] "Processato %d commento."
msgstr[1] "Processati %d commenti."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:45
msgid "Comment #%d could not be checked."
msgstr "Il commento #%d non può essere verificato."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:42
msgid "Failed to connect to Akismet."
msgstr "La connessione a Akismet è fallita."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:39
msgid "Comment #%d is not spam."
msgstr "Il commento #%d non è spam."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:36
msgid "Comment #%d is spam."
msgstr "Il commento #%d è spam."

#. translators: %s: number of false positive spam flagged by Akismet
#: views/config.php:66
msgid "%s false positive"
msgid_plural "%s false positives"
msgstr[0] "%s falso positivo"
msgstr[1] "%s falsi positivi"

#. translators: %s: number of spam missed by Akismet
#: views/config.php:64
msgid "%s missed spam"
msgid_plural "%s missed spam"
msgstr[0] "%s di spam non rilevato"
msgstr[1] "%s di spam non rilevati"

#: views/notice.php:175
msgid "You don&#8217;t have an Akismet plan."
msgstr "Non disponi di un piano Akismet."

#: views/notice.php:142
msgid "Your Akismet subscription is suspended."
msgstr "La sottoscrizione Akismet è sospesa."

#: views/notice.php:131
msgid "Your Akismet plan has been cancelled."
msgstr "Il tuo piano Akismet è stato annullato."

#. translators: The placeholder is a URL.
#: views/notice.php:124
msgid "We cannot process your payment. Please <a href=\"%s\" target=\"_blank\">update your payment details</a>."
msgstr "Impossibile elaborare il pagamento. <a href=\"%s\" target=\"_blank\">Aggiorna i dettagli del pagamento</a>."

#: views/notice.php:120
msgid "Please update your payment information."
msgstr "Aggiorna le informazioni di pagamento."

#. translators: %s: Number of minutes.
#: class.akismet-admin.php:1226
msgid "Akismet has saved you %d minute!"
msgid_plural "Akismet has saved you %d minutes!"
msgstr[0] "Akismet ti ha fatto risparmiare %d minuto!"
msgstr[1] "Akismet ti ha fatto risparmiare %d minuti!"

#. translators: %s: Number of hours.
#: class.akismet-admin.php:1223
msgid "Akismet has saved you %d hour!"
msgid_plural "Akismet has saved you %d hours!"
msgstr[0] "Akismet ti ha fatto risparmiare %d ora!"
msgstr[1] "Akismet ti ha fatto risparmiare %d ore!"

#. translators: %s: Number of days.
#: class.akismet-admin.php:1220
msgid "Akismet has saved you %s day!"
msgid_plural "Akismet has saved you %s days!"
msgstr[0] "Akismet ti ha fatto risparmiare %s giorno!"
msgstr[1] "Akismet ti ha fatto risparmiare %s giorni!"

#: class.akismet-admin.php:224 class.akismet-admin.php:262
#: class.akismet-admin.php:274
msgid "Akismet filters out spam, so you can focus on more important things."
msgstr "Akismet filtra lo spam, in modo da poterti far concentrare su cose più importanti."

#. translators: The placeholder is a URL.
#: views/notice.php:245
msgid "The connection to akismet.com could not be established. Please refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a> and check your server configuration."
msgstr "Non è possibile connettersi ad akismet.com. Fai riferimento alla <a href=\"%s\" target=\"_blank\">nostra guida sui firewall</a> e verifica la configurazione del tuo server."

#: views/notice.php:239
msgid "The API key you entered could not be verified."
msgstr "Non è stato possibile verificare la chiave inserita."

#: views/config.php:121
msgid "All systems functional."
msgstr "Tutti i sistemi funzionanti."

#: views/config.php:120
msgid "Enabled."
msgstr "Abilitato."

#: views/config.php:118
msgid "Akismet encountered a problem with a previous SSL request and disabled it temporarily. It will begin using SSL for requests again shortly."
msgstr "Akismet ha incontrato un problema con una precedente richiesta SSL e lo ha temporaneamente disabilitato. Ricomincerà a breve ad utilizzare nuovamente SSL per le richieste."

#: views/config.php:117
msgid "Temporarily disabled."
msgstr "Temporaneamente disabilitato."

#: views/config.php:112
msgid "Your Web server cannot make SSL requests; contact your Web host and ask them to add support for SSL requests."
msgstr "Il tuo Web server non può effettuare richieste SSL; contatta il tuo fornitore e chiedi l'aggiunta del supporto alle richieste SSL."

#: views/config.php:111
msgid "Disabled."
msgstr "Disabilitato."

#: views/config.php:108
msgid "SSL status"
msgstr "Stato SSL"

#: class.akismet-admin.php:720
msgid "This comment was reported as not spam."
msgstr "Questo commento è stato segnalato come non spam."

#: class.akismet-admin.php:712
msgid "This comment was reported as spam."
msgstr "Questo commento è stato segnalato come spam."

#. Author URI of the plugin
#: akismet.php
msgid "https://automattic.com/wordpress-plugins/"
msgstr "https://automattic.com/wordpress-plugins/"

#. Plugin URI of the plugin
#: akismet.php
msgid "https://akismet.com/"
msgstr "https://akismet.com/"

#: views/enter.php:2
msgid "Manually enter an API key"
msgstr "Inserisci manualmente una chiave API"

#: views/connect-jp.php:53 views/notice.php:333
msgid "Contact Akismet support"
msgstr "Contatta il supporto Akismet"

#: views/connect-jp.php:64
msgid "No worries! Get in touch and we&#8217;ll sort this out."
msgstr "Nessun problema! Contattataci e provvederemo a risolvere la questione."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:60
msgid "Your subscription for %s is suspended."
msgstr "Il tuo abbonamento per %s è sospeso. "

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:45
msgid "Your subscription for %s is cancelled."
msgstr "La tua iscrizione a %s è stata annullata. "

#: views/notice.php:217
msgid "The key you entered is invalid. Please double-check it."
msgstr "La chiave che hai inserito non è valida. Per favore verificala."

#: views/notice.php:164
msgid "There is a problem with your API key."
msgstr "C'è un problema con la tua chiave API."

#. translators: the placeholder is a clickable URL to the Akismet account
#. upgrade page.
#: views/notice.php:157
msgid "You can help us fight spam and upgrade your account by <a href=\"%s\" target=\"_blank\">contributing a token amount</a>."
msgstr "Puoi aiutarci a combattere lo spam e fare l'upgrade dell'account, <a href=\"%s\" target=\"_blank\"> versando un contributo simbolico </a>."

#. translators: The placeholder is a URL.
#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:146 views/notice.php:168
msgid "Please contact <a href=\"%s\" target=\"_blank\">Akismet support</a> for assistance."
msgstr "Contatta <a href=\"%s\" target=\"_blank\">il supporto di Akismet</a> per assistenza."

#. translators: The placeholder is a URL.
#: views/notice.php:135
msgid "Please visit your <a href=\"%s\" target=\"_blank\">Akismet account page</a> to reactivate your subscription."
msgstr "Visita la tua <a href=\"%s\" target=\"_blank\">pagina account di Akismet</a> per riattivare il tuo abbonamento."

#. translators: The placeholder is a URL.
#: views/notice.php:113
msgid "Your firewall may be blocking Akismet from connecting to its API. Please contact your host and refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a>."
msgstr "Il tuo firewall potrebbe impedire ad Akismet di connettersi alle proprie API. Contatta il tuo host e fai riferimento alla <a href=\"%s\" target=\"_blank\">nostra guida sui firewall</a>."

#. translators: The placeholder is a URL.
#: views/notice.php:102
msgid "Your web host or server administrator has disabled PHP&#8217;s <code>gethostbynamel</code> function.  <strong>Akismet cannot work correctly until this is fixed.</strong>  Please contact your web host or firewall administrator and give them <a href=\"%s\" target=\"_blank\">this information about Akismet&#8217;s system requirements</a>."
msgstr "Il tuo host web o l'amministratore del server hanno disattivato la funzione PHP <code> gethostbynamel</code>. <strong>Akismet non può funzionare correttamente fino a quando la funzione non viene attivata.</strong> Contatta il tuo host web o il tuo amministratore del firewall per dare loro <a href=\"%s\" target=\"_blank\">queste informazioni sui requisiti di sistema di Akismet</a>."

#: views/notice.php:98
msgid "Network functions are disabled."
msgstr "Le funzionalità di rete sono disabilitate."

#. translators: the placeholder is a clickable URL that leads to more
#. information regarding an error code.
#: views/notice.php:83
msgid "For more information: %s"
msgstr "Per ulteriori informazioni: %s"

#. translators: The placeholder is an error code returned by Akismet.
#: views/notice.php:78
msgid "Akismet error code: %s"
msgstr "Codice di errore Akismet:%s"

#: views/notice.php:37
msgid "Some comments have not yet been checked for spam by Akismet. They have been temporarily held for moderation and will automatically be rechecked later."
msgstr "Alcuni commenti non sono stati ancora verificati da Akismet per determinare se sono spam. Sono stati provvisoriamente posti in moderazione e verranno automaticamente ricontrollati successivamente."

#: views/notice.php:36 views/notice.php:46
msgid "Akismet has detected a problem."
msgstr "Akismet ha identificato un problema."

#: views/config.php:312
msgid "Change"
msgstr "Cambia"

#: views/config.php:312
msgid "Upgrade"
msgstr "Aggiorna"

#: views/config.php:293
msgid "Next billing date"
msgstr "Data della prossima fatturazione"

#: views/config.php:286
msgid "Active"
msgstr "Attivo"

#: views/config.php:284
msgid "No subscription found"
msgstr "Nessun abbonamento trovato"

#: views/config.php:282
msgid "Missing"
msgstr "Manca"

#: views/config.php:280
msgid "Suspended"
msgstr "Sospeso"

#: views/config.php:278
msgid "Cancelled"
msgstr "Annullato"

#: views/config.php:249
msgid "Save changes"
msgstr "Salva modifiche"

#: views/config.php:241
msgid "Disconnect this account"
msgstr "Scollegare questo account"

#: views/config.php:180
msgid "Note:"
msgstr "Nota:"

#: views/config.php:173
msgid "Always put spam in the Spam folder for review."
msgstr "Inserisci sempre lo spam nella cartella Spam per controlli."

#: views/config.php:165
msgid "Silently discard the worst and most pervasive spam so I never see it."
msgstr "Nascondi per sempre i peggiori e più persuasivi messaggi spam in modo da non vederli mai più."

#: views/config.php:159
msgid "Akismet Anti-spam strictness"
msgstr "Severità antispam Akismet"

#: views/config.php:146
msgid "Show the number of approved comments beside each comment author."
msgstr "Mostra il numero di commenti approvati accanto ad ogni autore del commento."

#: views/config.php:59
msgid "Accuracy"
msgstr "Precisione"

#: views/config.php:54
msgid "All time"
msgstr "Dall'inizio"

#: views/config.php:51 views/config.php:56
msgid "Spam blocked"
msgid_plural "Spam blocked"
msgstr[0] "Spam bloccato"
msgstr[1] "Spam bloccati"

#: views/config.php:49
msgid "Past six months"
msgstr "Ultimi sei mesi"

#. translators: 1: WordPress documentation URL, 2: Akismet download URL.
#: class.akismet.php:1732
msgid "Please <a href=\"%1$s\">upgrade WordPress</a> to a current version, or <a href=\"%2$s\">downgrade to version 2.4 of the Akismet plugin</a>."
msgstr "Per favore <a href=\"%1$s\"> aggiornare WordPress </a> ad una versione recente, o fare il <a href=\"%2$s\"> downgrade alla versione 2.4 del plugin Akismet </a>."

#. translators: 1: Current Akismet version number, 2: Minimum WordPress version
#. number required.
#: class.akismet.php:1730
msgid "Akismet %1$s requires WordPress %2$s or higher."
msgstr "Akismet %1$s richiede WordPress %2$s o successivo."

#: class.akismet-admin.php:727
msgid "Akismet cleared this comment during an automatic retry."
msgstr "Akismet ha ripulito questo commento durante un nuovo tentativo automatico."

#: class.akismet-admin.php:724
msgid "Akismet caught this comment as spam during an automatic retry."
msgstr "Akismet ha identificato questi commenti come spam durante un ricontrollo automatico."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:718
msgid "%s reported this comment as not spam."
msgstr "%s ha segnalato questo commento come non di spam."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:710
msgid "%s reported this comment as spam."
msgstr "%s ha segnalato questo commento come spam."

#. translators: %1$s is a username; %2$s is a short string (like 'spam' or
#. 'approved') denoting the new comment status.
#: class.akismet-admin.php:775
msgid "%1$s changed the comment status to %2$s."
msgstr "%1$s ha cambiato lo stato del commento in %2$s."

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:732
msgid "Akismet was unable to check this comment (response: %s) but will automatically retry later."
msgstr "Akismet non ha potuto riverificare questo commento (risposta: %s) ma riproverà automaticamente più tardi."

#: class.akismet-admin.php:697
msgid "Akismet cleared this comment."
msgstr "Akismet ha azzerato questo commento."

#. translators: The placeholder is a short string (like 'spam' or 'approved')
#. denoting the new comment status.
#: class.akismet-admin.php:769
msgid "Comment status was changed to %s"
msgstr "Lo stato del commento è stato cambiato in %s"

#: class.akismet-admin.php:691
msgid "Akismet caught this comment as spam."
msgstr "Akismet ha identificato questo commento come spam."

#. translators: The placeholder is the number of pieces of spam blocked by
#. Akismet.
#: class.akismet-widget.php:135
msgid "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgid_plural "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgstr[0] "<strong class=\"count\">%1$s spam</strong> bloccato da <strong>Akismet</strong>"
msgstr[1] "<strong class=\"count\">%1$s spam</strong> bloccati da <strong>Akismet</strong>"

#: class.akismet-widget.php:99
msgid "Title:"
msgstr "Titolo:"

#: class.akismet-widget.php:94 class.akismet-widget.php:116
msgid "Spam Blocked"
msgstr "Spam bloccato"

#: class.akismet-widget.php:17
msgid "Display the number of spam comments Akismet has caught"
msgstr "Visualizza il numero di commenti di spam identificati da Akismet"

#: class.akismet-widget.php:16
msgid "Akismet Widget"
msgstr "Widget Akismet"

#: class.akismet-admin.php:1216
msgid "Cleaning up spam takes time."
msgstr "Ripulire lo spam richiede tempo."

#. translators: The Akismet configuration page URL.
#: class.akismet-admin.php:1088
msgid "Please check your <a href=\"%s\">Akismet configuration</a> and contact your web host if problems persist."
msgstr "Verifica la tua <a href=\"%s\">configurazione di Akismet </a> e contatta il tuo hoster nel caso il problema persista."

#. translators: The placeholder is an amount of time, like "7 seconds" or "3
#. days" returned by the function human_time_diff().
#: class.akismet-admin.php:789
msgid "%s ago"
msgstr "%s fa"

#. translators: %s: Number of comments.
#: class.akismet-admin.php:664
msgid "%s approved"
msgid_plural "%s approved"
msgstr[0] "%s approvato"
msgstr[1] "%s approvati"

#: class.akismet-admin.php:638
msgid "History"
msgstr "Cronologia"

#: class.akismet-admin.php:638 class.akismet-admin.php:646
msgid "View comment history"
msgstr "Visualizza la cronologia del commento"

#. translators: %s: Username.
#: class.akismet-admin.php:625
msgid "Un-spammed by %s"
msgstr "Rimosso dallo spam da %s"

#. translators: %s: Username.
#: class.akismet-admin.php:622
msgid "Flagged as spam by %s"
msgstr "Contrassegnato come spam da %s"

#: class.akismet-admin.php:616
msgid "Cleared by Akismet"
msgstr "Pulito da Akismet"

#: class.akismet-admin.php:614
msgid "Flagged as spam by Akismet"
msgstr "Contrassegnato come spam da Akismet"

#: class.akismet-admin.php:610
msgid "Awaiting spam check"
msgstr "In attesa della verifica spam"

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:740
msgid "Akismet was unable to recheck this comment (response: %s)."
msgstr "Akismet non ha potuto riverificare questo commento (risposta: %s)."

#: class.akismet-admin.php:694
msgid "Akismet re-checked and cleared this comment."
msgstr "Akismet ha ri-verificato e azzerato questo commento."

#: class.akismet-admin.php:688
msgid "Akismet re-checked and caught this comment as spam."
msgstr "Akismet ha ri-controllato ed identificato questo commento come spam."

#: class.akismet-admin.php:498
msgid "Check for Spam"
msgstr "Controlla lo spam"

#. translators: %s: Comments page URL.
#: class.akismet-admin.php:440
msgid "There&#8217;s nothing in your <a href='%s'>spam queue</a> at the moment."
msgstr "Al momento non vi è nulla nella tua <a href='%s'>coda di spam</a>."

#. translators: 1: Number of comments, 2: Comments page URL.
#: class.akismet-admin.php:429
msgid "There&#8217;s <a href=\"%2$s\">%1$s comment</a> in your spam queue right now."
msgid_plural "There are <a href=\"%2$s\">%1$s comments</a> in your spam queue right now."
msgstr[0] "Al momento è presente <a href=\"%2$s\">%1$s commento</a> nella tua coda di spam."
msgstr[1] "Al momento sono presenti <a href=\"%2$s\">%1$s commenti</a> nella tua coda di spam."

#. translators: %s: Akismet website URL.
#: class.akismet-admin.php:421
msgid "<a href=\"%s\">Akismet</a> blocks spam from getting to your blog. "
msgstr "<a href=\"%s\">Akismet</a> impedisce allo spam di raggiungere il tuo blog. "

#. translators: 1: Akismet website URL, 2: Number of spam comments.
#: class.akismet-admin.php:410
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comment already. "
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comments already. "
msgstr[0] "<a href=\"%1$s\">Akismet</a> ha protetto il tuo sito da %2$s commento di spam. "
msgstr[1] "<a href=\"%1$s\">Akismet</a> ha protetto il tuo sito da %2$s commenti di spam. "

#. translators: 1: Akismet website URL, 2: Comments page URL, 3: Number of spam
#. comments.
#: class.akismet-admin.php:393
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comment</a>."
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comments</a>."
msgstr[0] "<a href=\"%1$s\">Akismet</a> ha protetto il tuo sito da <a href=\"%2$s\">%3$s commento spam</a>."
msgstr[1] "<a href=\"%1$s\">Akismet</a> ha protetto il tuo sito da <a href=\"%2$s\">%3$s commenti spam</a>."

#: class.akismet-admin.php:389
msgctxt "comments"
msgid "Spam"
msgstr "Spam"

#: class.akismet-admin.php:316
msgid "Cheatin&#8217; uh?"
msgstr "Vuoi barare, eh?"

#: class.akismet-admin.php:310
msgid "Akismet Support"
msgstr "Supporto Akismet"

#: class.akismet-admin.php:309
msgid "Akismet FAQ"
msgstr "Akismet FAQ"

#: class.akismet-admin.php:308
msgid "For more information:"
msgstr "Per maggiori informazioni:"

#: class.akismet-admin.php:299
msgid "The subscription status - active, cancelled or suspended"
msgstr "Stato dell'abbonamento - attivo, annullato o sospeso"

#: class.akismet-admin.php:299 views/config.php:274
msgid "Status"
msgstr "Stato"

#: class.akismet-admin.php:298
msgid "The Akismet subscription plan"
msgstr "Piano di abbonamento Akismet"

#: class.akismet-admin.php:298
msgid "Subscription Type"
msgstr "Tipo di abbonamento"

#: class.akismet-admin.php:295 views/config.php:260
msgid "Account"
msgstr "Account"

#: class.akismet-admin.php:287
msgid "Choose to either discard the worst spam automatically or to always put all spam in spam folder."
msgstr "Scegliere se scartare lo spam peggiore automaticamente oppure mettere sempre tutto lo spam nella cartella spam."

#: class.akismet-admin.php:287
msgid "Strictness"
msgstr "Precisione"

#: class.akismet-admin.php:286
msgid "Show the number of approved comments beside each comment author in the comments list page."
msgstr "Mostra il numero di commenti approvati accanto a ogni autore del commento nella pagina di elenco dei commenti."

#: class.akismet-admin.php:286 views/config.php:131
msgid "Comments"
msgstr "Commenti"

#: class.akismet-admin.php:285
msgid "Enter/remove an API key."
msgstr "Immettere/rimuovere una chiave API."

#: class.akismet-admin.php:285
msgid "API Key"
msgstr "Chiave API"

#: class.akismet-admin.php:273 class.akismet-admin.php:284
#: class.akismet-admin.php:297
msgid "Akismet Configuration"
msgstr "Configurazione Akismet"

#: class.akismet-admin.php:263
msgid "On this page, you are able to view stats on spam filtered on your site."
msgstr "In questa pagina è possibile visualizzare le statistiche sullo spam filtrato nel tuo sito."

#: class.akismet-admin.php:261
msgid "Akismet Stats"
msgstr "Statistiche Akismet"

#: class.akismet-admin.php:250
msgid "Click the Use this Key button."
msgstr "Fai clic sul pulsante Usa questa chiave."

#: class.akismet-admin.php:249
msgid "Copy and paste the API key into the text field."
msgstr "Copia e incolla la chiave API nel campo di testo."

#: class.akismet-admin.php:247
msgid "If you already have an API key"
msgstr "Se hai già una chiave API"

#: class.akismet-admin.php:244
msgid "Enter an API Key"
msgstr "Inserisci una chiave API"

#. translators: %s: a link to the signup page with the text 'Akismet.com'.
#: class.akismet-admin.php:237
msgid "Sign up for an account on %s to get an API Key."
msgstr "Iscriviti per un account su %s per ricevere una chiave API."

#: class.akismet-admin.php:235
msgid "You need to enter an API key to activate the Akismet service on your site."
msgstr "È necessario immettere una chiave API per attivare il servizio Akismet sul tuo sito."

#: class.akismet-admin.php:232
msgid "New to Akismet"
msgstr "Nuovo per Akismet"

#: class.akismet-admin.php:225
msgid "On this page, you are able to set up the Akismet plugin."
msgstr "Su questa pagina, puoi impostare il plugin Akismet."

#: class.akismet-admin.php:223 class.akismet-admin.php:234
#: class.akismet-admin.php:246
msgid "Akismet Setup"
msgstr "Setup Akismet"

#: class.akismet-admin.php:221 class.akismet-admin.php:259
#: class.akismet-admin.php:271
msgid "Overview"
msgstr "Panoramica"

#: class.akismet-admin.php:190
msgid "Re-adding..."
msgstr "Ri-aggiungi..."

#: class.akismet-admin.php:189
msgid "(undo)"
msgstr "(annulla)"

#: class.akismet-admin.php:188
msgid "URL removed"
msgstr "URL rimosso"

#: class.akismet-admin.php:187
msgid "Removing..."
msgstr "Rimozione in corso..."

#: class.akismet-admin.php:186
msgid "Remove this URL"
msgstr "Rimuovi questo URL"

#: class.akismet-admin.php:107 class.akismet-admin.php:1463
msgid "Akismet"
msgstr "Akismet"

#: class.akismet-admin.php:128 class.akismet-admin.php:282
#: class.akismet-admin.php:816 views/config.php:83
msgid "Settings"
msgstr "Impostazioni"

#: class.akismet-admin.php:103
msgid "Comment History"
msgstr "Cronologia commento"
