<?php
// Test per creare la tabella CSRF manualmente
define('WP_USE_THEMES', false);
require_once('wp-load.php');

global $wpdb;

$table_name = $wpdb->prefix . 'office_addin_csrf_tokens';
$charset_collate = $wpdb->get_charset_collate();

echo "Tentativo di creare tabella: {$table_name}\n";

$sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    token varchar(64) NOT NULL,
    action varchar(50) NOT NULL,
    user_id bigint(20) NOT NULL,
    ip_address varchar(45) NOT NULL,
    user_agent varchar(255) NOT NULL,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY token (token),
    KEY user_action (user_id, action),
    KEY created_at (created_at)
) $charset_collate;";

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
$result = dbDelta($sql);

echo "Risultato dbDelta:\n";
print_r($result);

// Verifica se la tabella esiste ora
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'");
echo "\nTabella esiste: " . ($table_exists ? 'SÌ' : 'NO') . "\n";

if ($wpdb->last_error) {
    echo "Errore MySQL: " . $wpdb->last_error . "\n";
}
?>
