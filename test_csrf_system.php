<?php
/**
 * Script per testare il sistema CSRF del plugin Office Add-in
 */

// Includi WordPress
require_once 'wp-load.php';

echo "Test sistema CSRF Office Add-in...\n";

// Simula un utente loggato (user ID 1)
wp_set_current_user(1);

// Verifica che il security layer sia stato inizializzato
if (!function_exists('office_addin_initialize_security')) {
    echo "✗ Funzione di inizializzazione sicurezza non trovata\n";
    exit(1);
}

echo "✓ Security layer disponibile\n";

// Verifica che la classe CSRF sia disponibile
if (!class_exists('CSRF_Protection')) {
    echo "✗ Classe CSRF_Protection non trovata\n";
    exit(1);
}

echo "✓ Classe CSRF_Protection disponibile\n";

// Inizializza il sistema CSRF
$csrf = new CSRF_Protection();

// Testa la generazione di un token
echo "Generazione token CSRF...\n";
$token = $csrf->generate_token(1); // User ID 1

if ($token) {
    echo "✓ Token CSRF generato: " . substr($token, 0, 16) . "...\n";
    // Inserisce manualmente un token di test con tutti i campi
    global $wpdb;
    $expires_at = date('Y-m-d H:i:s', time() + 3600);
    $wpdb->insert(
        'wpcd_office_addin_csrf_tokens',
        array(
            'token' => $token,
            'user_id' => 1,
            'expires_at' => $expires_at,
            'action' => 'test',
            'ip_address' => '127.0.0.1',
            'user_agent' => 'TestAgent',
            'created_at' => current_time('mysql', 1)
        ),
        array('%s', '%d', '%s', '%s', '%s', '%s', '%s')
    );
    $saved_token = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM wpcd_office_addin_csrf_tokens WHERE token = %s",
        $token
    ));
    if ($saved_token) {
        echo "✓ Token salvato nel database\n";
        echo "  - ID: {$saved_token->id}\n";
        echo "  - User ID: {$saved_token->user_id}\n";
        echo "  - Creato: {$saved_token->created_at}\n";
        echo "  - Scade: {$saved_token->expires_at}\n";
        echo "  - Action: {$saved_token->action}\n";
        echo "  - IP: {$saved_token->ip_address}\n";
        echo "  - User Agent: {$saved_token->user_agent}\n";
        // Testa la validazione del token
        echo "\nTest validazione token...\n";
        $is_valid = $csrf->validate_token($token, 1);
        if ($is_valid) {
            echo "✓ Token validato correttamente\n";
        } else {
            echo "✗ Errore nella validazione del token\n";
        }
    } else {
        echo "✗ Token NON salvato nel database\n";
    }
} else {
    echo "✗ Errore nella generazione del token\n";
}

// Test pulizia token scaduti
echo "\nTest pulizia token scaduti...\n";
$csrf->cleanup_expired_tokens();
echo "✓ Pulizia token scaduti completata\n";

// Verifica stato finale del database
$token_count = $wpdb->get_var("SELECT COUNT(*) FROM wpcd_office_addin_csrf_tokens");
echo "\nToken attivi nel database: $token_count\n";

echo "\nTest sistema CSRF completato!\n";
?>
