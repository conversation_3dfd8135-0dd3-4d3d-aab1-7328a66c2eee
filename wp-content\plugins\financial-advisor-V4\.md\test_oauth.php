
<a href="https://accounts.google.com/o/oauth2/v2/auth?scope=openid%20email%20profile&access_type=offline&include_granted_scopes=true&response_type=code&state=state_parameter_passthrough_value&redirect_uri=https://app.theoremia.com/google-callback.php&client_id=*************-agas5sqlf6g6ejn7apkgv9djdarp2c7n.apps.googleusercontent.com">
  Login with Google
</a>

  Login with Google
</a>

<?php
// CONFIGURAZIONE
$client_id = '*************-agas5sqlf6g6ejn7apkgv9djdarp2c7n.apps.googleusercontent.com';
$client_secret = '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';
$redirect_uri = 'https://app.theoremia.com/google-callback.php'; // Dove Google ti rimanda

// 1. <PERSON>vi il "code" dopo il login
if (isset($_GET['code'])) {
    $code = $_GET['code'];

    // 2. Scambia il "code" per l'access_token
    $token_url = 'https://oauth2.googleapis.com/token';
    $data = [
        'code' => $code,
        'client_id' => $client_id,
        'client_secret' => $client_secret,
        'redirect_uri' => $redirect_uri,
        'grant_type' => 'authorization_code'
    ];

    $options = [
        'http' => [
            'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
            'method'  => 'POST',
            'content' => http_build_query($data),
        ],
    ];

    $context  = stream_context_create($options);
    $response = file_get_contents($token_url, false, $context);
    $response_data = json_decode($response, true);

    if (isset($response_data['access_token'])) {
        $access_token = $response_data['access_token'];

        // 3. Usa l'access_token per ottenere i dati utente
        $user_info_url = 'https://www.googleapis.com/oauth2/v2/userinfo';

        $opts = [
            'http' => [
                'header' => "Authorization: Bearer " . $access_token,
            ],
        ];
        $context = stream_context_create($opts);
        $user_info = file_get_contents($user_info_url, false, $context);
        $user = json_decode($user_info, true);

        // Ora hai i dati utente!
        echo "<h1>Benvenuto, " . htmlspecialchars($user['name']) . "!</h1>";
        echo "<p>Email: " . htmlspecialchars($user['email']) . "</p>";
        echo "<img src='" . htmlspecialchars($user['picture']) . "' alt='Foto profilo'>";
        
        // A questo punto puoi salvarlo nel tuo database (es: MySQL)
        // salvaUtenteNelDatabase($user['id'], $user['email'], $user['name'], $user['picture']);
    } else {
        echo "Errore nell'ottenere l'access token.";
    }
} else {
    echo "Nessun codice ricevuto.";
}
?>

