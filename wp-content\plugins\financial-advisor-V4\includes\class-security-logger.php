<?php
/**
 * Security Logger Class
 * 
 * Provides comprehensive logging functionality for security events
 * 
 * @package Financial_Advisor_V4
 * @subpackage Security
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Security_Logger {
    
    /**
     * Log levels
     */
    const LEVEL_INFO = 'INFO';
    const LEVEL_WARNING = 'WARNING';
    const LEVEL_ERROR = 'ERROR';
    const LEVEL_CRITICAL = 'CRITICAL';
    
    /**
     * Log file path
     * @var string
     */
    private $log_file;
    
    /**
     * Database table name
     * @var string
     */
    private $table_name;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        
        $this->log_file = WP_CONTENT_DIR . '/logs/office-addin-security.log';
        $this->table_name = $wpdb->prefix . 'office_addin_security_logs';
        
        // Ensure log directory exists
        $this->ensure_log_directory();
        
        // Create database table if needed
        $this->create_log_table();
    }
    
    /**
     * Log a security event
     */
    public function log_security_event($event_type, $details, $severity = self::LEVEL_INFO) {
        $timestamp = current_time('mysql');
        $user_id = get_current_user_id();
        $ip = $this->get_client_ip();
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        
        $log_entry = [
            'timestamp' => $timestamp,
            'event_type' => $event_type,
            'severity' => $severity,
            'user_id' => $user_id,
            'ip_address' => $ip,
            'details' => is_array($details) ? json_encode($details) : $details,
            'user_agent' => $user_agent
        ];
        
        // Write to file
        $this->write_log_file($log_entry);
        
        // Write to database
        $this->write_log_database($log_entry);
        
        // Send alerts for critical events
        if ($severity === self::LEVEL_CRITICAL) {
            $this->send_security_alert($log_entry);
        }
    }
    
    /**
     * Log event - wrapper for log_security_event to maintain compatibility
     */
    public function log_event($event_type, $details, $severity = self::LEVEL_INFO) {
        return $this->log_security_event($event_type, $details, $severity);
    }
    
    /**
     * Get recent security logs
     */
    public function get_recent_logs($limit = 50) {
        global $wpdb;
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name} ORDER BY timestamp DESC LIMIT %d",
            $limit
        );
        
        return $wpdb->get_results($sql, ARRAY_A);
    }
    
    /**
     * Get logs by severity
     */
    public function get_logs_by_severity($severity, $limit = 50) {
        global $wpdb;
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE severity = %s ORDER BY timestamp DESC LIMIT %d",
            $severity,
            $limit
        );
        
        return $wpdb->get_results($sql, ARRAY_A);
    }
    
    /**
     * Get logs by event type
     */
    public function get_logs_by_event_type($event_type, $limit = 50) {
        global $wpdb;
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE event_type = %s ORDER BY timestamp DESC LIMIT %d",
            $event_type,
            $limit
        );
        
        return $wpdb->get_results($sql, ARRAY_A);
    }
    
    /**
     * Clean old logs
     */
    public function clean_old_logs($days = 90) {
        global $wpdb;
        
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $sql = $wpdb->prepare(
            "DELETE FROM {$this->table_name} WHERE timestamp < %s",
            $cutoff_date
        );
        
        return $wpdb->query($sql);
    }
    
    /**
     * Ensure log directory exists
     */
    private function ensure_log_directory() {
        $log_dir = dirname($this->log_file);
        
        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
        }
        
        // Add .htaccess to prevent direct access
        $htaccess_file = $log_dir . '/.htaccess';
        if (!file_exists($htaccess_file)) {
            file_put_contents($htaccess_file, "Deny from all\n");
        }
    }
    
    /**
     * Create database table for logs
     */
    private function create_log_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            timestamp datetime NOT NULL,
            event_type varchar(100) NOT NULL,
            severity enum('INFO','WARNING','ERROR','CRITICAL') NOT NULL DEFAULT 'INFO',
            user_id bigint(20) DEFAULT NULL,
            ip_address varchar(45) NOT NULL,
            details text,
            user_agent text,
            PRIMARY KEY (id),
            KEY timestamp (timestamp),
            KEY event_type (event_type),
            KEY severity (severity),
            KEY ip_address (ip_address)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Write log entry to file
     */
    private function write_log_file($log_entry) {
        $log_line = sprintf(
            "[%s] %s - %s - IP: %s - User: %s - %s\n",
            $log_entry['timestamp'],
            $log_entry['severity'],
            $log_entry['event_type'],
            $log_entry['ip_address'],
            $log_entry['user_id'] ?: 'Guest',
            $log_entry['details']
        );
        
        file_put_contents($this->log_file, $log_line, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Write log entry to database
     */
    private function write_log_database($log_entry) {
        global $wpdb;
        
        $wpdb->insert(
            $this->table_name,
            $log_entry,
            [
                '%s', // timestamp
                '%s', // event_type
                '%s', // severity
                '%d', // user_id
                '%s', // ip_address
                '%s', // details
                '%s'  // user_agent
            ]
        );
    }
    
    /**
     * Send security alert email
     */
    private function send_security_alert($log_entry) {
        $admin_email = get_option('admin_email');
        if (!$admin_email) {
            return;
        }
        
        $subject = sprintf(
            '[SECURITY ALERT] %s - %s',
            get_bloginfo('name'),
            $log_entry['event_type']
        );
        
        $message = $this->format_alert_message($log_entry);
        
        wp_mail($admin_email, $subject, $message);
    }
    
    /**
     * Format alert message
     */
    private function format_alert_message($log_entry) {
        $message = "A critical security event has been detected on your website.\n\n";
        $message .= "Event Details:\n";
        $message .= "- Type: " . $log_entry['event_type'] . "\n";
        $message .= "- Severity: " . $log_entry['severity'] . "\n";
        $message .= "- Time: " . $log_entry['timestamp'] . "\n";
        $message .= "- IP Address: " . $log_entry['ip_address'] . "\n";
        $message .= "- User: " . ($log_entry['user_id'] ? get_userdata($log_entry['user_id'])->user_login : 'Guest') . "\n";
        $message .= "- Details: " . $log_entry['details'] . "\n";
        $message .= "- User Agent: " . $log_entry['user_agent'] . "\n\n";
        $message .= "Please review your security logs and take appropriate action if necessary.\n\n";
        $message .= "Admin URL: " . admin_url('admin.php?page=office-addin-security') . "\n";
        
        return $message;
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $headers = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];
        
        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
}
