<?php

/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the website, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/
 *
 * @package WordPress
 */

// If this is a local development environment, load local config first
// Controlla se siamo in ambiente Docker o locale
$is_docker_env = (
    gethostname() === 'a10d7636ccd2' || // hostname del container Docker
    (isset($_SERVER['SERVER_NAME']) && $_SERVER['SERVER_NAME'] === 'claryfin.local') ||
    getenv('DOCKER_ENV') === 'true' ||
    file_exists('/.dockerenv') // file presente nei container Docker
);

if ($is_docker_env) {
    if (file_exists(__DIR__ . '/wp-config-local.php')) {
        require_once __DIR__ . '/wp-config-local.php';
    }
}

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
if (!defined('DB_NAME')) define( 'DB_NAME', 'glopvhmy_wp249' );

/** Database username */
if (!defined('DB_USER')) define( 'DB_USER', 'glopvhmy_wp249' );

/** Database password */
if (!defined('DB_PASSWORD')) define( 'DB_PASSWORD', 'p68@BSa4q!' );

/** Database hostname */
if (!defined('DB_HOST')) define( 'DB_HOST', 'localhost' );

/** Database charset to use in creating database tables. */
if (!defined('DB_CHARSET')) define( 'DB_CHARSET', 'utf8mb4' );

/** The database collate type. Don't change this if in doubt. */
if (!defined('DB_COLLATE')) define( 'DB_COLLATE', '' );

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',         'arhkm4lmo1spqfvnbg04uirmxr43vhuoozu0wkjj9ovruvqes3hydvyivihvs2sp' );
define( 'SECURE_AUTH_KEY',  'yyhb7877kli3feivm9jiqm74r5oj7pzwg5bii8rxir46tunynjgdsrdhzxub4u8c' );
define( 'LOGGED_IN_KEY',    'txcx1655wpul5pkcdcogk3o15nj2f6vsxxjljkk6zzebhycedkbsc1jyeaqbvr5g' );
define( 'NONCE_KEY',        'bpnugmkt9wxbnmcvxnp37gxqrt3wcbemb899v7zwleryrwba6xyzsmdqmmpfhxep' );
define( 'AUTH_SALT',        'pndjd0iwkzy2qffo1otxtklj8xkmfx2pdojgstiombc2ayapwicoilx5z30pucu0' );
define( 'SECURE_AUTH_SALT', 'mhgdtkrdpxdeu3p5nnbzjtsyy1ov58ae7pbrw0dae0k4nmivadymhtom2alp740y' );
define( 'LOGGED_IN_SALT',   'hxqcuodupvomixjopjexvtw17eoy9vx0dhzrqgjdpjcuphevxf64lqm9omwlj4ti' );
define( 'NONCE_SALT',       '769j7ff7jaweu4l3coazjbsvvu9jof8zh4fcmnopm947he1hfibvw9dnyacriebb' );

/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 *
 * At the installation time, database tables are created with the specified prefix.
 * Changing this value after WordPress is installed will make your site think
 * it has not been installed.
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/#table-prefix
 */
$table_prefix = 'wpcd_';

/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the documentation.
 *
 * @link https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/
 */

if (!defined('WP_DEBUG')) define('WP_DEBUG', true);
if (!defined('WP_DEBUG_LOG')) define('WP_DEBUG_LOG', true);
if (!defined('WP_DEBUG_DISPLAY')) define('WP_DEBUG_DISPLAY', true);

// Set UTF-8 encoding for debug log
ini_set('default_charset', 'UTF-8');
ini_set('internal_encoding', 'UTF-8');
ini_set('output_encoding', 'UTF-8');

/* Add any custom values between this line and the "stop editing" line. */

define( 'SURECART_ENCRYPTION_KEY', 'txcx1655wpul5pkcdcogk3o15nj2f6vsxxjljkk6zzebhycedkbsc1jyeaqbvr5g' );

/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	if (!defined('ABSPATH')) define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';
