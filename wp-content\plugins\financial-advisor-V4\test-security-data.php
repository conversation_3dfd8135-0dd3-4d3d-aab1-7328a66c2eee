<?php
/**
 * Test Security Data Generator
 * 
 * This script generates sample security data for testing the security tabs
 * Run this via WP-CLI or directly to populate test data
 */

// Only run if accessed via WordPress admin or WP-CLI
if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

/**
 * Generate test security data
 */
function generate_test_security_data() {
    // Check if Security_Logger class exists
    if (!class_exists('Security_Logger')) {
        return ['error' => 'Security_Logger class not found'];
    }
    
    $security_logger = new Security_Logger();
    $results = [];
    
    // Sample security events to create
    $sample_events = [
        [
            'type' => 'system_check',
            'details' => json_encode(['status' => 'healthy', 'checks' => 15]),
            'severity' => 'INFO'
        ],
        [
            'type' => 'rate_limit_exceeded', 
            'details' => json_encode(['ip' => '*************', 'action' => 'office_addin_analyze', 'attempts' => 35]),
            'severity' => 'WARNING'
        ],
        [
            'type' => 'blocked_request',
            'details' => json_encode(['ip' => '*********', 'reason' => 'suspicious_pattern', 'endpoint' => '/office-addin/']),
            'severity' => 'ERROR'
        ],
        [
            'type' => 'csrf_token_mismatch',
            'details' => json_encode(['ip' => '***********', 'action' => 'office_addin_get_settings']),
            'severity' => 'WARNING'
        ],
        [
            'type' => 'sql_injection_attempt',
            'details' => json_encode(['ip' => '************', 'payload' => 'UNION SELECT', 'blocked' => true]),
            'severity' => 'ERROR'
        ],
        [
            'type' => 'xss_attempt',
            'details' => json_encode(['ip' => '*************', 'payload' => '<script>alert', 'blocked' => true]),
            'severity' => 'ERROR'
        ],
        [
            'type' => 'security_scan',
            'details' => json_encode(['scan_type' => 'automated', 'threats_found' => 0, 'files_scanned' => 1250]),
            'severity' => 'INFO'
        ],
        [
            'type' => 'response_time',
            'details' => json_encode(['endpoint' => '/office-addin/', 'response_time' => 45, 'status' => 'ok']),
            'severity' => 'INFO'
        ],
        [
            'type' => 'ip_blocked',
            'details' => json_encode(['ip' => '************', 'reason' => 'multiple_violations', 'duration' => '24h']),
            'severity' => 'WARNING'
        ],
        [
            'type' => 'system_update',
            'details' => json_encode(['component' => 'security_layer', 'version' => '1.2.0', 'status' => 'success']),
            'severity' => 'INFO'
        ]
    ];
    
    // Create events with realistic timestamps (spread over last 24 hours)
    foreach ($sample_events as $i => $event) {
        try {
            // Create events at different times over the last 24 hours
            $hours_ago = rand(1, 24);
            $minutes_ago = rand(0, 59);
            
            // Temporarily set timestamp for realistic data
            $_SERVER['REQUEST_TIME'] = time() - ($hours_ago * 3600) - ($minutes_ago * 60);
            
            $security_logger->log_security_event(
                $event['type'],
                $event['details'],
                $event['severity']
            );
            
            $results[] = "Created {$event['type']} event ({$hours_ago}h {$minutes_ago}m ago)";
            
        } catch (Exception $e) {
            $results[] = "Error creating {$event['type']}: " . $e->getMessage();
        }
    }
    
    // Reset REQUEST_TIME
    $_SERVER['REQUEST_TIME'] = time();
    
    return $results;
}

/**
 * Generate test rate limit data
 */
function generate_test_rate_limit_data() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'office_addin_rate_limits';
    
    // Check if table exists
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") !== $table_name) {
        return ['error' => 'Rate limit table does not exist'];
    }
    
    $results = [];
    
    // Create sample rate limit entries
    $sample_ips = ['*************', '*********', '***********', '************'];
    $actions = ['office_addin_analyze', 'office_addin_get_settings', 'office_addin_get_queries'];
    
    foreach ($sample_ips as $ip) {
        foreach ($actions as $action) {
            $entries = rand(1, 5);
            for ($i = 0; $i < $entries; $i++) {
                $timestamp = time() - rand(300, 3600); // Last hour
                
                $wpdb->insert(
                    $table_name,
                    [
                        'identifier' => $ip,
                        'action' => $action,
                        'timestamp' => $timestamp,
                        'count' => 1
                    ]
                );
            }
        }
    }
    
    $results[] = "Created rate limit entries for " . count($sample_ips) . " IPs";
    return $results;
}

/**
 * Display test results
 */
function display_security_test_results() {
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    echo '<div class="wrap">';
    echo '<h1>Security Data Test Generator</h1>';
    
    if (isset($_POST['generate_data'])) {
        echo '<h2>Security Events Results:</h2>';
        $results = generate_test_security_data();
        echo '<ul>';
        foreach ($results as $result) {
            echo '<li>' . esc_html($result) . '</li>';
        }
        echo '</ul>';
        
        echo '<h2>Rate Limit Results:</h2>';
        $rate_results = generate_test_rate_limit_data();
        echo '<ul>';
        foreach ($rate_results as $result) {
            echo '<li>' . esc_html($result) . '</li>';
        }
        echo '</ul>';
        
        echo '<div class="notice notice-success"><p>Test data generated successfully!</p></div>';
    }
    
    echo '<form method="post">';
    echo '<p>This will generate sample security data for testing the security tabs.</p>';
    echo '<p><input type="submit" name="generate_data" class="button button-primary" value="Generate Test Data" /></p>';
    echo '</form>';
    
    echo '</div>';
}

// Add admin menu for testing (only in debug mode)
if (defined('WP_DEBUG') && WP_DEBUG && is_admin()) {
    add_action('admin_menu', function() {
        add_submenu_page(
            'tools.php',
            'Security Test Data',
            'Security Test Data',
            'manage_options',
            'security-test-data',
            'display_security_test_results'
        );
    });
}
