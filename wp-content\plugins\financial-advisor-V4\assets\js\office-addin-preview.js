/**
 * Office Add-in Preview JavaScript
 *
 * Questo file gestisce la funzionalità di anteprima live dell'Office Add-in
 * nel pannello di amministrazione WordPress.
 */

(function($) {
    if (window.officeAddinPreviewLoaded) {
        console.warn('office-addin-preview.js: already loaded, skipping initialization');
        return;
    }
    window.officeAddinPreviewLoaded = true;
    'use strict';

    // Debug: Check if office_addin_preview_params is available
    console.log('📋 Office Add-in Preview Script Loading...');
    console.log('📋 office_addin_preview_params available:', typeof window.office_addin_preview_params !== 'undefined');
    if (typeof window.office_addin_preview_params !== 'undefined') {
        console.log('📋 Available params:', window.office_addin_preview_params);
    } else {
        console.warn('⚠️ office_addin_preview_params not found - AJAX requests may fail');
    }

    // Variabili globali
    var previewFrame = null;
    var previewMode = 'normal'; // Can be 'normal', 'large', or 'fullheight'
    var loadedQueries = []; // Store loaded queries for dropdown access
    var sampleExcelData = "Financial Report Q2 2023\n\nRevenue: $1,250,000\nExpenses: $780,000\nProfit: $470,000\n\nProduct Breakdown:\nProduct A: $520,000\nProduct B: $380,000\nProduct C: $350,000\n\nRegional Sales:\nNorth America: $650,000\nEurope: $420,000\nAsia: $180,000";

    // Excel Grid variables
    var excelGrid = null;
    var selectedCells = [];
    var gridData = [];
    var gridRows = 20;
    var gridCols = 10;
    var isDragging = false;
    var dragStartCell = null;

    // Esponi le funzioni globalmente per permettere l'accesso da altri script e dall'iframe
    window.updatePreview = updatePreview;
    window.initializeGridHandlers = initializeGridHandlers;
    window.extractSelectedCellsText = extractSelectedCellsText;
    window.selectAllCells = selectAllCells;
    window.clearCellSelection = clearCellSelection;
    window.loadSampleData = loadSampleData;
    window.clearGrid = clearGrid;
    window.initializeExternalGrid = initializeExternalGrid;
    window.syncWithAddin = syncWithAddin;

    /**
     * Create Excel-like grid
     */
    function createExcelGrid() {
        // Initialize grid data
        gridData = [];
        for (var i = 0; i < gridRows; i++) {
            gridData[i] = [];
            for (var j = 0; j < gridCols; j++) {
                gridData[i][j] = '';
            }
        }

        // Populate with sample data
        populateGridWithSampleData();

        var gridHtml = '<div class="excel-grid-container">';
        gridHtml += '<div class="excel-grid-header">Excel-like Data Grid</div>';
        gridHtml += '<div class="excel-grid-controls">';
        gridHtml += '<button id="clear-grid" class="grid-button">Clear Grid</button>';
        gridHtml += '<button id="paste-data" class="grid-button">Paste Data</button>';
        gridHtml += '<button id="load-sample" class="grid-button">Load Sample</button>';
        gridHtml += '</div>';
        gridHtml += '<div class="excel-grid-wrapper">';
        gridHtml += '<table class="excel-grid" id="excel-grid">';

        // Header row with column letters
        gridHtml += '<thead><tr><th class="row-header"></th>';
        for (var col = 0; col < gridCols; col++) {
            gridHtml += '<th class="col-header">' + getColumnLetter(col) + '</th>';
        }
        gridHtml += '</tr></thead>';

        // Data rows
        gridHtml += '<tbody>';
        for (var row = 0; row < gridRows; row++) {
            gridHtml += '<tr>';
            gridHtml += '<td class="row-header">' + (row + 1) + '</td>';
            for (var col = 0; col < gridCols; col++) {
                var cellId = 'cell-' + row + '-' + col;
                var cellValue = gridData[row][col] || '';
                gridHtml += '<td class="grid-cell" data-row="' + row + '" data-col="' + col + '" id="' + cellId + '">';
                gridHtml += '<input type="text" class="cell-input" value="' + escapeHtml(cellValue) + '">';
                gridHtml += '</td>';
            }
            gridHtml += '</tr>';
        }
        gridHtml += '</tbody>';
        gridHtml += '</table>';
        gridHtml += '</div>';
        gridHtml += '<div class="grid-selection-info">';
        gridHtml += '<span id="selection-info">No cells selected</span>';
        gridHtml += '</div>';
        gridHtml += '</div>';

        return gridHtml;
    }

    /**
     * Get column letter (A, B, C, ..., Z, AA, AB, ...)
     */
    function getColumnLetter(col) {
        var letter = '';
        while (col >= 0) {
            letter = String.fromCharCode(65 + (col % 26)) + letter;
            col = Math.floor(col / 26) - 1;
        }
        return letter;
    }

    /**
     * Escape HTML characters
     */
    function escapeHtml(text) {
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    /**
     * Populate grid with sample data
     */
    function populateGridWithSampleData() {
        // Sample financial data
        var sampleData = [
            ['Financial Report Q2 2023', '', '', ''],
            ['', '', '', ''],
            ['Category', 'Amount', 'Percentage', 'Notes'],
            ['Revenue', '1250000', '100%', 'Total Revenue'],
            ['Expenses', '780000', '62.4%', 'Operating Expenses'],
            ['Profit', '470000', '37.6%', 'Net Profit'],
            ['', '', '', ''],
            ['Product Breakdown', '', '', ''],
            ['Product A', '520000', '41.6%', 'Best Seller'],
            ['Product B', '380000', '30.4%', 'Growing'],
            ['Product C', '350000', '28%', 'Stable'],
            ['', '', '', ''],
            ['Regional Sales', '', '', ''],
            ['North America', '650000', '52%', 'Main Market'],
            ['Europe', '420000', '33.6%', 'Expanding'],
            ['Asia', '180000', '14.4%', 'New Market']
        ];

        for (var i = 0; i < sampleData.length && i < gridRows; i++) {
            for (var j = 0; j < sampleData[i].length && j < gridCols; j++) {
                gridData[i][j] = sampleData[i][j] || '';
            }
        }
    }

    /**
     * Initialize grid event handlers
     */
    function initializeGridHandlers(frameDoc) {
    var $frameDoc = $(frameDoc);

        $frameDoc.on('click', '#load-sample', function() {
            loadSampleData(frameDoc);
        });

        $frameDoc.on('click', '#paste-data', function() {
            pasteDataToGrid(frameDoc);
        });

        // Keyboard shortcuts and navigation
        $frameDoc.on('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                if (e.key === 'a') {
                    e.preventDefault();
                    selectAllCells(frameDoc);
                } else if (e.key === 'c') {
                    e.preventDefault();
                    copySelectedCells();
                } else if (e.key === 'v') {
                    e.preventDefault();
                    pasteDataToGrid(frameDoc);
                }
            } else if (selectedCells.length === 1) {
                // Arrow key navigation
                var $currentCell = selectedCells[0];
                var currentRow = parseInt($currentCell.data('row'));
                var currentCol = parseInt($currentCell.data('col'));
                var newRow = currentRow;
                var newCol = currentCol;

                switch(e.key) {
                    case 'ArrowUp':
                        e.preventDefault();
                        newRow = Math.max(0, currentRow - 1);
                        break;
                    case 'ArrowDown':
                        e.preventDefault();
                        newRow = Math.min(gridRows - 1, currentRow + 1);
                        break;
                    case 'ArrowLeft':
                        e.preventDefault();
                        newCol = Math.max(0, currentCol - 1);
                        break;
                    case 'ArrowRight':
                        e.preventDefault();
                        newCol = Math.min(gridCols - 1, currentCol + 1);
                        break;
                }

                if (newRow !== currentRow || newCol !== currentCol) {
                    var $newCell = $frameDoc.find('#cell-' + newRow + '-' + newCol);
                    if ($newCell.length) {
                        clearCellSelection();
                        selectCell($newCell);
                        updateSelectionInfo(frameDoc);

                        // Scroll into view if necessary
                        $newCell[0].scrollIntoView({ block: 'nearest', inline: 'nearest' });
                    }
                }
            }
        });
    }

    /**
     * Select a single cell
     */
    function selectCell($cell) {
        $cell.addClass('selected');
        selectedCells = [$cell];
    }

    /**
     * Toggle cell selection
     */
    function toggleCellSelection($cell) {
        if ($cell.hasClass('selected')) {
            $cell.removeClass('selected');
            selectedCells = selectedCells.filter(function(cell) {
                return !cell.is($cell);
            });
        } else {
            $cell.addClass('selected');
            selectedCells.push($cell);
        }
    }

    /**
     * Select range of cells
     */
    function selectCellRange($startCell, $endCell) {
        var startRow = parseInt($startCell.data('row'));
        var startCol = parseInt($startCell.data('col'));
        var endRow = parseInt($endCell.data('row'));
        var endCol = parseInt($endCell.data('col'));

        var minRow = Math.min(startRow, endRow);
        var maxRow = Math.max(startRow, endRow);
        var minCol = Math.min(startCol, endCol);
        var maxCol = Math.max(startCol, endCol);

        clearCellSelection();

        for (var row = minRow; row <= maxRow; row++) {
            for (var col = minCol; col <= maxCol; col++) {
                var $cell = $('#cell-' + row + '-' + col);
                if ($cell.length) {
                    $cell.addClass('selected');
                    selectedCells.push($cell);
                }
            }
        }
    }

    /**
     * Clear cell selection
     */
    function clearCellSelection() {
        $('.grid-cell.selected').removeClass('selected');
        selectedCells = [];
    }

    /**
     * Select all cells
     */
    function selectAllCells(frameDoc) {
        var $frameDoc = $(frameDoc);
        clearCellSelection();
        $frameDoc.find('.grid-cell').each(function() {
            $(this).addClass('selected');
            selectedCells.push($(this));
        });
        updateSelectionInfo(frameDoc);
    }

    /**
     * Update selection info display
     */
    function updateSelectionInfo(frameDoc) {
        var $frameDoc = $(frameDoc);
        var $info = $frameDoc.find('#selection-info');

        if (selectedCells.length === 0) {
            $info.text('No cells selected');
        } else if (selectedCells.length === 1) {
            var $cell = selectedCells[0];
            var row = parseInt($cell.data('row')) + 1;
            var col = getColumnLetter(parseInt($cell.data('col')));
            $info.text('Selected: ' + col + row);
        } else {
            $info.text('Selected: ' + selectedCells.length + ' cells');
        }
    }

    /**
     * Clear grid data
     */
    function clearGrid(frameDoc) {
        var $frameDoc = $(frameDoc);

        // Clear grid data
        for (var i = 0; i < gridRows; i++) {
            for (var j = 0; j < gridCols; j++) {
                gridData[i][j] = '';
            }
        }

        // Clear UI
        $frameDoc.find('.cell-input').val('');
        clearCellSelection();
        updateSelectionInfo(frameDoc);
    }

    /**
     * Load sample data into grid
     */
    function loadSampleData(frameDoc) {
        var $frameDoc = $(frameDoc);

        // Reload sample data
        populateGridWithSampleData();

        // Update UI
        for (var i = 0; i < gridRows; i++) {
            for (var j = 0; j < gridCols; j++) {
                var $input = $frameDoc.find('#cell-' + i + '-' + j + ' .cell-input');
                if ($input.length) {
                    $input.val(gridData[i][j] || '');
                }
            }
        }
    }

    /**
     * Copy selected cells to clipboard
     */
    function copySelectedCells() {
        if (selectedCells.length === 0) {
            return;
        }

        var copyData = [];
        selectedCells.forEach(function($cell) {
            var row = parseInt($cell.data('row'));
            var col = parseInt($cell.data('col'));
            var value = gridData[row][col] || '';
            copyData.push({
                row: row,
                col: col,
                value: value
            });
        });

        // Store in a global variable for paste operation
        window.copiedCells = copyData;

        // Try to use clipboard API if available
        if (navigator.clipboard && navigator.clipboard.writeText) {
            var textData = copyData.map(function(cell) {
                return cell.value;
            }).join('\t');

            navigator.clipboard.writeText(textData).catch(function(err) {
                console.log('Could not copy to clipboard:', err);
            });
        }
    }

    /**
     * Paste data to grid
     */
    function pasteDataToGrid(frameDoc) {
        var $frameDoc = $(frameDoc);

        // Try to read from clipboard first
        if (navigator.clipboard && navigator.clipboard.readText) {
            navigator.clipboard.readText().then(function(text) {
                processPastedText(text, frameDoc);
            }).catch(function(err) {
                console.log('Could not read from clipboard:', err);
                // Fallback to manual input
                promptForPasteData(frameDoc);
            });
        } else {
            // Fallback to manual input
            promptForPasteData(frameDoc);
        }
    }

    /**
     * Prompt user for paste data
     */
    function promptForPasteData(frameDoc) {
        var data = prompt('Paste your data here (tab-separated for columns, newline-separated for rows):');
        if (data) {
            processPastedText(data, frameDoc);
        }
    }

    /**
     * Process pasted text data
     */
    function processPastedText(text, frameDoc) {
        var $frameDoc = $(frameDoc);
        var lines = text.split('\n');
        var startRow = 0;
        var startCol = 0;

        // If cells are selected, use first selected cell as start position
        if (selectedCells.length > 0) {
            var $firstCell = selectedCells[0];
            startRow = parseInt($firstCell.data('row'));
            startCol = parseInt($firstCell.data('col'));
        }

        // Process each line
        for (var i = 0; i < lines.length; i++) {
            var row = startRow + i;
            if (row >= gridRows) break;

            var cells = lines[i].split('\t');
            for (var j = 0; j < cells.length; j++) {
                var col = startCol + j;
                if (col >= gridCols) break;

                var value = cells[j].trim();
                gridData[row][col] = value;

                // Update UI
                var $input = $frameDoc.find('#cell-' + row + '-' + col + ' .cell-input');
                if ($input.length) {
                    $input.val(value);
                }
            }
        }
    }

    /**
     * Extract text from selected cells (replaces Excel extraction)
     */
    function extractSelectedCellsText() {
        if (selectedCells.length === 0) {
            return 'No cells selected. Please select cells in the grid above.';
        }

        var extractedText = '';
        var cellsByRow = {};

        // Group cells by row
        selectedCells.forEach(function($cell) {
            var row = parseInt($cell.data('row'));
            var col = parseInt($cell.data('col'));
            var value = gridData[row][col] || '';

            if (!cellsByRow[row]) {
                cellsByRow[row] = {};
            }
            cellsByRow[row][col] = value;
        });

        // Build text row by row
        var rows = Object.keys(cellsByRow).sort(function(a, b) { return parseInt(a) - parseInt(b); });

        rows.forEach(function(row) {
            var cols = Object.keys(cellsByRow[row]).sort(function(a, b) { return parseInt(a) - parseInt(b); });
            var rowText = cols.map(function(col) {
                return cellsByRow[row][col];
            }).join(' ');

            if (rowText.trim()) {
                extractedText += rowText + '\n';
            }
        });

        return extractedText.trim() || 'Selected cells are empty.';
    }

    /**
     * Initialize external grid (outside the add-in panel)
     */
    function initializeExternalGrid() {
        console.log('Initializing external Excel grid...');

        var $container = $('#external-excel-grid-container');
        if ($container.length === 0) {
            console.warn('External grid container not found');
            return;
        }

        // Create and insert the grid
        var gridHtml = createExcelGrid();
        $container.html(gridHtml);

        // Initialize grid handlers for the external grid
        initializeGridHandlers(document);

        // Initialize test buttons
        initializeTestButtons();

        console.log('External Excel grid initialized successfully');
    }

    /**
     * Initialize sync button for the external grid
     */
    function initializeTestButtons() {
        // Sync with Add-in button
        $('#sync-with-addin').off('click').on('click', function() {
            syncWithAddin();
        });
    }

    /**
     * Sync external grid with add-in (Real Production Behavior)
     */
    function syncWithAddin() {
        console.log('Syncing external grid with add-in...');

        // Extract text from selected cells
        var extractedText = extractSelectedCellsText();

        if (!extractedText || extractedText === 'No cells selected. Please select cells in the grid above.' || extractedText === 'Selected cells are empty.') {
            alert('Please select some cells with data before syncing with the add-in.');
            return;
        }

        // Show the extracted text in the external results
        $('#extracted-text-display').text(extractedText);
        $('#extraction-results').show();

        // Update the add-in iframe with the extracted text
        var $iframe = $('#addin-preview-frame');
        if ($iframe.length > 0) {
            try {
                var frameDoc = $iframe[0].contentDocument || $iframe[0].contentWindow.document;
                if (frameDoc) {
                    // Update extracted text in iframe
                    var $extractedTextDiv = $(frameDoc).find('#extracted-text');
                    var $extractedTextContainer = $(frameDoc).find('#extracted-text-container');

                    if ($extractedTextDiv.length > 0) {
                        $extractedTextDiv.text(extractedText);
                        $extractedTextContainer.show();

                        // Scroll the iframe to show the extracted text
                        $extractedTextContainer[0].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }

                    // Focus on the add-in panel to indicate sync completion
                    var $analyzeButton = $(frameDoc).find('#analyze-button');
                    if ($analyzeButton.length > 0) {
                        $analyzeButton.focus();
                    }
                }
            } catch (error) {
                console.log('Could not update iframe content:', error);
                alert('Sync completed, but could not update the add-in display. The extracted text is available in the results section.');
            }
        }

        // Show success message
        var selectedCount = selectedCells.length;
        var cellText = selectedCount !== 1 ? 's' : '';
        var message = '✅ Synced ' + selectedCount + ' cell' + cellText + ' with add-in. You can now use the analysis features in the add-in panel.';

        // Temporarily show success message
        var $button = $('#sync-with-addin');
        var originalText = $button.text();
        $button.text('✅ Synced!').prop('disabled', true);

        setTimeout(function() {
            $button.text(originalText).prop('disabled', false);
        }, 2000);

        console.log('Sync completed. Extracted text:', extractedText.substring(0, 100) + '...');
    }

    // Rendi disponibili variabili AJAX per l'iframe
    window.ajaxurl = window.office_addin_preview_params ? window.office_addin_preview_params.ajax_url : '/wp-admin/admin-ajax.php';
    console.log('Main window ajaxurl:', window.ajaxurl);

    /**
     * Carica gli script di anteprima in modo affidabile
     *
     * @return {Promise} Promise che si risolve quando lo script è caricato
     */
    function loadPreviewScript() {
        return new Promise(function(resolve, reject) {
            if (typeof window.updatePreview === 'function') {
                return resolve();
            }

            var script = document.createElement('script');
            script.src = (window.ajaxurl || '/wp-admin/admin-ajax.php') + '?action=office_addin_preview_script&_=' + new Date().getTime();
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Funzione di inizializzazione principale
    function initializePreview() {
        console.log('Initializing Office Add-in preview...');

        // Inizializza il frame di anteprima
        previewFrame = document.getElementById('addin-preview-frame');

        if (!previewFrame) {
            console.error('Preview frame not found during initialization');
            // Tenta di trovare il frame dopo un breve ritardo
            setTimeout(function() {
                previewFrame = document.getElementById('addin-preview-frame');
                if (previewFrame) {
                    console.log('Preview frame found after delay');
                    setupEventHandlers();
                    checkAndUpdatePreview();
                } else {
                    console.error('Preview frame still not found after delay');
                    showErrorMessage('Preview frame not found. Please reload the page and try again.');
                }
            }, 500);
            return;
        }

        setupEventHandlers();
        checkAndUpdatePreview();
    }

    // Configura i gestori eventi
    function setupEventHandlers() {
        console.log('Setting up event handlers');

        // Aggiungi gestori eventi per i pulsanti
        $('#preview-changes').off('click.previewhandler').on('click.previewhandler', function() {
            console.log('Preview changes button clicked');
            updatePreview();
        });

        $('#refresh-preview').off('click.previewhandler').on('click.previewhandler', function() {
            console.log('Refresh preview button clicked');
            updatePreview();
        });

        $('#toggle-preview-size').off('click.previewhandler').on('click.previewhandler', function() {
            console.log('Toggle preview size button clicked');
            togglePreviewSize();
        });

        // NOTA: La gestione dei tab è stata spostata nel file class-menu-manager.php
        // per evitare conflitti. Questo file si occupa solo della gestione del preview.
        
        // Ascolta eventi personalizzati invece di gestire direttamente i click sui tab
        $(document).off('office-addin-tab-changed.previewhandler').on('office-addin-tab-changed.previewhandler', function(e, tabId) {
            if (tabId === 'preview-tab' || tabId === '#preview-tab') {
                console.log('Preview tab activated via custom event, updating preview...');
                setTimeout(function() {
                    updatePreview();
                }, 300);
            }
        });
    }

    // Controlla se siamo nella tab di anteprima e aggiorna se necessario
    function checkAndUpdatePreview() {
        console.log('Checking if preview tab is active');
        if ($('#preview-tab').hasClass('active') || $('#preview-tab').is(':visible')) {
            console.log('Preview tab is active, updating preview...');
            setTimeout(updatePreview, 300);
        } else {
            console.log('Preview tab is not active');
        }
    }

    // Funzione globale per inizializzare le tab in modo sicuro
    window.initializeOfficeTabs = function() {
        if (!window.tabsInitialized) {
            console.log('Initializing Office Add-in tabs from global function...');
            $('.office-addin-tabs .nav-tab').first().trigger('click');
            window.tabsInitialized = true;
        }
    };

    // Inizializza quando il documento è pronto
    $(document).ready(function() {
        console.log('Document ready, initializing preview...');
        initializePreview();

        // Aggiungi un gestore per il caricamento completo della pagina
        $(window).on('load', function() {
            console.log('Window loaded, checking preview...');
            // Verifica nuovamente dopo il caricamento completo della pagina
            setTimeout(checkAndUpdatePreview, 500);

            // Ensure tabs are initialized
            if (typeof window.initializeOfficeTabs === 'function') {
                window.initializeOfficeTabs();
            }
        });
    });

    /**
     * Check AI connection status with enhanced monitoring
     */
    function checkAIConnectionStatus() {
        console.log('🔍 Checking AI connection status for Office Add-in...');

        // Show connection checking status
        updateApiStatusInFrame('checking', 'Checking connection...');

        $.ajax({
            url: window.ajaxurl || '/wp-admin/admin-ajax.php',
            type: 'POST',
            timeout: 15000, // Extended timeout
            data: {
                action: 'check_ai_status',
                nonce: window.office_addin_preview_params ? window.office_addin_preview_params.nonce : ''
            },
            success: function(response) {
                console.log('✅ AI status check response:', response);
                if (response.success) {
                    updateApiStatusInFrame('connected', response.data.message || 'AI Connected');

                    // Store connection status
                    window.lastConnectionCheck = Date.now();
                    window.connectionStatus = 'connected';

                    // Only load settings if connection is successful
                    loadRealApiSettings();

                    // Start periodic connection monitoring
                    startPeriodicConnectionCheck();
                } else {
                    updateApiStatusInFrame('error', response.data.message || 'AI Connection Failed');
                    window.previewApiSettings = { api_configured: false };
                    window.connectionStatus = 'error';
                }
            },
            error: function(xhr, status, error) {
                console.error('❌ AI status check failed:', error, 'Status:', status);
                var errorMessage = 'Cannot verify AI connection';

                if (status === 'timeout') {
                    errorMessage = 'Connection timeout';
                } else if (status === 'error') {
                    errorMessage = 'Network error';
                }

                updateApiStatusInFrame('error', errorMessage);
                window.previewApiSettings = { api_configured: false };
                window.connectionStatus = 'error';

                // Start retry mechanism
                scheduleConnectionRetry();
            }
        });
    }

    /**
     * Load real API settings from the WordPress settings with enhanced error handling
     */
    function loadRealApiSettings() {
        console.log('🔧 Loading real API settings...');
        console.log('🔧 Available nonces:', window.office_addin_preview_params);

        $.ajax({
            url: window.ajaxurl || '/wp-admin/admin-ajax.php',
            type: 'POST',
            timeout: 15000, // Extended timeout for API settings
            data: {
                action: 'office_addin_get_settings',
                nonce: window.office_addin_preview_params ?
                    (window.office_addin_preview_params.settings_nonce || window.office_addin_preview_params.nonce) : ''
            },
            success: function(response) {
                console.log('📡 API settings loaded:', response);
                if (response.success && response.data) {
                    window.previewApiSettings = response.data;

                    // Only update if API is properly configured
                    if (response.data.api_configured) {
                        setTimeout(function() {
                            updateApiStatusDisplay(response.data);
                        }, 500);
                    } else {
                        // API settings exist but not configured
                        updateApiStatusInFrame('error', 'API not configured');
                    }
                } else {
                    console.warn('API settings not available:', response);
                    window.previewApiSettings = { api_configured: false };
                    updateApiStatusInFrame('error', 'Settings unavailable');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading API settings:', error, 'Status:', status);
                // Nessun retry, nessun polling: solo una richiesta iniziale
                window.previewApiSettings = { api_configured: false };
                updateApiStatusInFrame('error', 'Settings load failed');
            }
        });
    }

    /**
     * Retry API settings loading with exponential backoff
     */
    function retryApiSettingsLoad(attempt) {
        var maxAttempts = 3;
        var baseDelay = 2000; // 2 second base delay

        if (attempt > maxAttempts) {
            console.warn('Max retry attempts reached for API settings, using fallback');
            window.previewApiSettings = { api_configured: false };
            updateApiStatusInFrame('error', 'Connection timeout - check network');
            return;
        }

        var delay = baseDelay * Math.pow(2, attempt - 1);
        console.log(`API settings retry attempt ${attempt}/${maxAttempts} in ${delay}ms...`);

        setTimeout(function() {
            $.ajax({
                url: window.ajaxurl || '/wp-admin/admin-ajax.php',
                type: 'POST',
                timeout: 15000 + (attempt * 5000), // Progressive timeout increase
                data: {
                    action: 'office_addin_get_settings',
                    nonce: window.office_addin_preview_params ?
                        (window.office_addin_preview_params.settings_nonce || window.office_addin_preview_params.nonce) : ''
                },
                success: function(response) {
                    console.log('📡 API settings loaded on retry:', response);
                    if (response.success && response.data) {
                        window.previewApiSettings = response.data;

                        if (response.data.api_configured) {
                            updateApiStatusDisplay(response.data);
                        } else {
                            updateApiStatusInFrame('error', 'API not configured');
                        }
                    } else {
                        window.previewApiSettings = { api_configured: false };
                        updateApiStatusInFrame('error', 'Settings unavailable');
                    }
                },
                error: function(retryXhr, retryStatus, retryError) {
                    console.error('API settings retry attempt ' + attempt + ' failed:', retryError);
                    retryApiSettingsLoad(attempt + 1);
                }
            });
        }, delay);
    }

    /**
     * Update API status display in iframe
     */
    function updateApiStatusDisplay(apiSettings) {
        if (!previewFrame) return;

        try {
            var frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
            if (!frameDoc) return;

            var $apiStatus = $(frameDoc).find('#api-status');
            var $selectedModel = $(frameDoc).find('#selected-model');

            if ($apiStatus.length > 0) {
                if (apiSettings.api_configured) {
                    $apiStatus.text('Connected').removeClass('api-disconnected').addClass('api-connected');
                    if ($selectedModel.length > 0 && apiSettings.model) {
                        $selectedModel.text(apiSettings.model);
                    }
                } else {
                    $apiStatus.text('Not Connected').removeClass('api-connected').addClass('api-disconnected');
                    if ($selectedModel.length > 0) {
                        $selectedModel.text('None');
                    }
                }
            }
        } catch (error) {
            console.error('Error updating API status display:', error);
        }
    }

    /**
     * Update API status in frame (generic function) with enhanced status handling
     */
    function updateApiStatusInFrame(status, message) {
        if (!previewFrame) return;

        try {
            var frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
            if (!frameDoc) return;

            var $apiStatus = $(frameDoc).find('#api-status');
            if ($apiStatus.length > 0) {
                $apiStatus.text(message);

                // Update CSS classes based on status
                $apiStatus.removeClass('api-connected api-disconnected api-error api-checking');
                switch(status) {
                    case 'connected':
                        $apiStatus.addClass('api-connected');
                        break;
                    case 'error':
                        $apiStatus.addClass('api-error');
                        break;
                    case 'checking':
                        $apiStatus.addClass('api-checking');
                        break;
                    default:
                        $apiStatus.addClass('api-disconnected');
                }

                // Add timestamp for debugging
                $apiStatus.attr('data-last-update', new Date().toISOString());
            }
        } catch (error) {
            console.error('Error updating API status in frame:', error);
        }
    }

    /**
     * Aggiorna l'anteprima dell'add-in
     */
    function updatePreview() {
        console.log('Updating preview...');

        try {
            // Mostra un indicatore di caricamento
            showLoadingInPreview();

            // Check AI connection status first
            checkAIConnectionStatus();

            // Ottieni il contenuto HTML dell'add-in dal nuovo sistema
            var addinContent = getNewSystemAddinContent();

            if (!addinContent) {
                console.warn('Add-in content is empty, using fallback');
                addinContent = getNewSystemAddinContent(); // Il fallback è già incluso nella funzione
            }

            // Ottieni il CSS dell'add-in dal nuovo sistema
            var addinCSS = getDefaultCSS();
            console.log('Using default CSS from new system');

            // Crea il documento HTML completo per l'anteprima
            var previewHtml = createPreviewHtml(addinContent, addinCSS);

            // Aggiorna il contenuto dell'iframe
            updateIframeContent(previewHtml);

            console.log('Preview updated successfully');

            // We'll let the initializePreviewContent function handle the dropdown population
            // The initialization is now done via a setTimeout in updateIframeContent

            // Funzione per caricare le query predefinite nel dropdown
            function loadQueriesIntoDropdown(select, frameDoc) {
                // Pulisci il select e aggiungi l'opzione predefinita
                select.empty();
                select.append($('<option></option>').attr('value', '').text('-- Select a query --'));

                // Carica le query predefinite dal database
                $.ajax({
                    url: window.ajaxurl || '/wp-admin/admin-ajax.php',
                    type: 'POST',
                    data: {
                        action: 'office_addin_get_queries',
                        nonce: window.office_addin_preview_params ? window.office_addin_preview_params.nonce : ''
                    },
                    success: function(response) {
                        console.log('Predefined queries loaded successfully:', response);
                        if (response.success && response.data && response.data.length > 0) {
                            // Popola il select con le query predefinite dal database
                            $.each(response.data, function(i, query) {
                                select.append($('<option></option>').attr('value', query.id).text(query.title));
                            });
                            console.log('Dropdown populated with ' + response.data.length + ' queries from database');
                        } else {
                            console.warn('No predefined queries found in database or invalid response');
                            // Usa query predefinite di fallback
                            var fallbackQueries = [
                                { id: 1, title: "Analyze financial performance" },
                                { id: 2, title: "Identify key trends" },
                                { id: 3, title: "Summarize quarterly results" },
                                { id: 4, title: "Compare regional performance" }
                            ];

                            $.each(fallbackQueries, function(i, query) {
                                select.append($('<option></option>').attr('value', query.id).text(query.title));
                            });
                            console.log('Dropdown populated with fallback queries');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading predefined queries:', error);
                        // Usa query predefinite di fallback in caso di errore
                        var fallbackQueries = [
                            { id: 1, title: "Analyze financial performance" },
                            { id: 2, title: "Identify key trends" },
                            { id: 3, title: "Summarize quarterly results" },
                            { id: 4, title: "Compare regional performance" }
                        ];

                        $.each(fallbackQueries, function(i, query) {
                            select.append($('<option></option>').attr('value', query.id).text(query.title));
                        });
                        console.log('Dropdown populated with fallback queries due to error');
                    }
                });
            }
        } catch (error) {
            console.error('Error updating preview:', error);

            // Mostra un messaggio di errore nell'iframe
            try {
                if (previewFrame) {
                    var errorMessage = error.message || 'Unknown error';
                    var errorHtml = '<!DOCTYPE html><html><head><meta charset="utf-8"><style>body { font-family: \'Segoe UI\', Arial, sans-serif; padding: 20px; }.error { color: #e74c3c; }pre { background: #f8f8f8; padding: 10px; border-radius: 4px; overflow: auto; }</style></head><body><h2>Excel Add-in Preview Error</h2><p class="error">Si è verificato un errore durante l\'aggiornamento dell\'anteprima.</p><pre>' + errorMessage + '</pre><button onclick="location.reload()">Ricarica</button></body></html>';

                    var blob = new Blob([errorHtml], { type: 'text/html' });
                    var blobURL = URL.createObjectURL(blob);

                    previewFrame.onload = function() {
                        URL.revokeObjectURL(blobURL);
                    };

                    previewFrame.src = blobURL;
                }
            } catch (recoveryError) {
                console.error('Failed to show error message in iframe:', recoveryError);
            }
        }
    }

    /**
     * Restituisce il CSS predefinito per l'add-in
     *
     * @return {string} Il CSS predefinito
     */
    function getDefaultCSS() {
        return '/* CSS standard per Excel add-in */' +
        '.excel-addin-container { font-family: "Segoe UI", Arial, sans-serif; padding: 10px; width: 320px; margin: 0 auto; box-sizing: border-box; }' +
        '.section { margin-bottom: 15px; padding: 10px; border: 1px solid #e0e0e0; border-radius: 4px; background-color: #f9f9f9; }' +
        'h2 { color: #2c3e50; margin-top: 0; margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid #eee; font-size: 18px; }' +
        'h3 { color: #3498db; margin-top: 0; margin-bottom: 10px; font-size: 16px; }' +
        '.form-group { margin-bottom: 10px; }' +
        'label { display: block; margin-bottom: 5px; font-weight: bold; }' +
        '.primary-button { background-color: #3498db; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-weight: bold; }' +
        '.secondary-button { background-color: #95a5a6; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; }' +
        '.excel-grid-container { margin: 15px 0; border: 1px solid #d0d0d0; border-radius: 4px; background: #fff; }' +
        '.excel-grid-header { background: #f8f9fa; padding: 10px 15px; border-bottom: 1px solid #d0d0d0; font-weight: 600; color: #333; }' +
        '.excel-grid-controls { padding: 10px 15px; background: #f8f9fa; border-bottom: 1px solid #d0d0d0; display: flex; gap: 10px; flex-wrap: wrap; }' +
        '.grid-button { background: #0078d4; color: white; border: none; padding: 6px 12px; border-radius: 3px; cursor: pointer; font-size: 12px; transition: background-color 0.2s; }' +
        '.grid-button:hover { background: #106ebe; }' +
        '.grid-button:active { background: #005a9e; }' +
        '.excel-grid-wrapper { max-height: 400px; overflow: auto; border-bottom: 1px solid #d0d0d0; }' +
        '.excel-grid { width: 100%; border-collapse: collapse; font-family: "Segoe UI", Arial, sans-serif; font-size: 12px; }' +
        '.excel-grid th, .excel-grid td { border: 1px solid #d0d0d0; padding: 0; margin: 0; text-align: center; vertical-align: middle; }' +
        '.excel-grid th.row-header, .excel-grid td.row-header { background: #f8f9fa; color: #666; font-weight: 600; width: 40px; min-width: 40px; max-width: 40px; font-size: 11px; user-select: none; }' +
        '.excel-grid th.col-header { background: #f8f9fa; color: #666; font-weight: 600; height: 25px; font-size: 11px; user-select: none; width: 80px; min-width: 80px; }' +
        '.excel-grid .grid-cell { padding: 0; position: relative; width: 80px; min-width: 80px; height: 22px; }' +
        '.excel-grid .grid-cell.selected { background: #cce7ff !important; border: 2px solid #0078d4 !important; }' +
        '.excel-grid .cell-input { width: 100%; height: 100%; border: none; outline: none; padding: 2px 4px; font-family: inherit; font-size: inherit; background: transparent; box-sizing: border-box; }' +
        '.excel-grid .cell-input:focus { background: #fff; border: 2px solid #0078d4; z-index: 10; position: relative; }' +
        '.grid-selection-info { padding: 8px 15px; background: #f8f9fa; font-size: 11px; color: #666; border-top: 1px solid #e0e0e0; }' +
        '.excel-grid tbody tr:hover .grid-cell:not(.selected) { background: #f5f5f5; }' +
        '.excel-grid .grid-cell:hover:not(.selected) { background: #f0f0f0; }';
    }

    /**
     * Crea il documento HTML completo per l'anteprima
     *
     * @param {string} addinContent Il contenuto HTML dell'add-in
     * @param {string} addinCSS Il CSS dell'add-in
     * @return {string} Il documento HTML completo
     */
    function createPreviewHtml(addinContent, addinCSS) {
        var nonce = window.office_addin_preview_params ? window.office_addin_preview_params.nonce : '';
        var sampleData = 'Revenue,Expenses,Profit\\n1250000,780000,470000\\nProduct A,520000,Product B,350000\\nNorth America,650000,Europe,420000';

        var html = '<!DOCTYPE html><html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1"><title>Excel Add-in Preview</title>';
        html += '<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>';
        html += '<style>html, body { margin: 0; padding: 0; font-family: "Segoe UI", Arial, sans-serif; font-size: 14px; line-height: 1.4; color: #333; background-color: #fff; height: 100%; width: 100%; overflow: hidden; }';
        html += '.excel-addin-container { width: 320px; height: 100%; overflow-y: auto; box-sizing: border-box; margin: 0 auto; padding: 10px; background-color: #fff; border-right: 1px solid #e0e0e0; }';
        html += '.section { margin-bottom: 15px; padding: 10px; border: 1px solid #e0e0e0; border-radius: 4px; background-color: #f9f9f9; }';
        html += 'h2 { color: #2c3e50; margin-top: 0; margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid #eee; font-size: 18px; }';
        html += 'h3 { color: #3498db; margin-top: 0; margin-bottom: 10px; font-size: 16px; }';
        html += '.form-group { margin-bottom: 10px; } label { display: block; margin-bottom: 5px; font-weight: bold; }';
        html += '.primary-button { background-color: #3498db; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-weight: bold; }';
        html += '.secondary-button { background-color: #95a5a6; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; }';
        html += 'select { width: 100%; padding: 8px; border: 1px solid #d0d0d0; border-radius: 4px; background-color: #ffffff; color: #333333; font-size: 14px; line-height: 1.5; box-sizing: border-box; }';
        html += 'select option { padding: 4px 8px; background-color: #ffffff !important; color: #333333 !important; text-shadow: none !important; }';
        html += 'select:focus, select:hover { outline: 1px solid #007cba; box-shadow: 0 0 2px rgba(30, 140, 190, 0.8); }';
        html += 'textarea { width: 100%; padding: 8px; border: 1px solid #d0d0d0; border-radius: 4px; background-color: #ffffff; color: #333333; font-size: 14px; line-height: 1.5; min-height: 80px; box-sizing: border-box; }';
        html += '@keyframes highlight-animation { 0% { background-color: #ffffff; } 30% { background-color: #ffffd0; } 100% { background-color: #ffffff; } }';
        html += '.textarea-highlight { animation: highlight-animation 1.5s ease-in-out; border-color: #4a90e2 !important; }';
        html += '.api-connected { color: #28a745 !important; font-weight: bold; }';
        html += '.api-disconnected { color: #6c757d !important; font-weight: normal; }';
        html += '.api-error { color: #dc3545 !important; font-weight: bold; }';
        html += '.status-indicator { padding: 4px 8px; border-radius: 3px; font-size: 12px; }';
        html += '.status-indicator.connected { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }';
        html += '.status-indicator.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }';
        html += '.status-indicator.testing { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }';
        html += addinCSS + '</style>';

        // Add JavaScript section
        html += '<script>';
        html += 'function getBaseUrl() { try { if (window.location.origin) { return window.location.origin; } var url = window.location.protocol + "//" + window.location.hostname; if (window.location.port) { url += ":" + window.location.port; } return url; } catch(e) { return "http://" + (document.domain || window.location.hostname || "localhost"); } }';
        html += 'window.ajaxurl = getBaseUrl() + "/wp-admin/admin-ajax.php";';
        html += 'window.office_addin_preview_params = { nonce: "' + nonce + '", ajax_url: window.ajaxurl };';
        html += 'var Office = { initialize: function() {}, context: {}, HostType: { Excel: "Excel" }, onReady: function(callback) { $(document).ready(function() { callback({ host: "Excel" }); }); } };';
        html += 'var Excel = { run: function(callback) { return new Promise(function(resolve) { var context = { workbook: { getSelectedRange: function() { return { text: [["' + sampleData + '"]], load: function() {} }; } }, sync: function() { return Promise.resolve(); } }; resolve(callback(context)); }); } };';
        
        // Add API connection functions
        html += 'function checkAIConnectionStatus() {';
        html += '  console.log("Checking AI connection status...");';
        html += '  $.ajax({';
        html += '    url: window.ajaxurl,';
        html += '    type: "POST",';
        html += '    data: {';
        html += '      action: "check_ai_status",';
        html += '      nonce: window.office_addin_preview_params.nonce';
        html += '    },';
        html += '    success: function(response) {';
        html += '      if (response.success) {';
        html += '        updateApiStatusDisplay("connected", response.data.model || "AI Ready");';
        html += '        console.log("AI connection successful:", response.data);';
        html += '      } else {';
        html += '        updateApiStatusDisplay("error", "Not Connected");';
        html += '        console.warn("AI connection failed:", response.data);';
        html += '      }';
        html += '    },';
        html += '    error: function(xhr, status, error) {';
        html += '      updateApiStatusDisplay("error", "Connection Error");';
        html += '      console.error("AI connection check failed:", error);';
        html += '    }';
        html += '  });';
        html += '}';
        
        html += 'function loadRealApiSettings() {';
        html += '  console.log("Loading real API settings...");';
        html += '  $.ajax({';
        html += '    url: window.ajaxurl,';
        html += '    type: "POST",';
        html += '    data: {';
        html += '      action: "office_addin_get_settings",';
        html += '      nonce: window.office_addin_preview_params.nonce';
        html += '    },';
        html += '    success: function(response) {';
        html += '      if (response.success && response.data) {';
        html += '        console.log("API settings loaded:", response.data);';
        html += '        if (response.data.model) {';
        html += '          $("#selected-model").text(response.data.model);';
        html += '        }';
        html += '      } else {';
        html += '        console.warn("Failed to load API settings:", response);';
        html += '      }';
        html += '    },';
        html += '    error: function(xhr, status, error) {';
        html += '      console.error("Error loading API settings:", error);';
        html += '    }';
        html += '  });';
        html += '}';
        
        html += 'function updateApiStatusDisplay(status, modelText) {';
        html += '  var $apiStatus = $("#api-status");';
        html += '  var $selectedModel = $("#selected-model");';
        html += '  $apiStatus.removeClass("api-connected api-disconnected api-error");';
        html += '  if (status === "connected") {';
        html += '    $apiStatus.text("Connected").addClass("api-connected");';
        html += '    $selectedModel.text(modelText || "AI Ready");';
        html += '  } else if (status === "error") {';
        html += '    $apiStatus.text("Error").addClass("api-error");';
        html += '    $selectedModel.text(modelText || "Error");';
        html += '  } else {';
        html += '    $apiStatus.text("Disconnected").addClass("api-disconnected");';
        html += '    $selectedModel.text(modelText || "None");';
        html += '  }';
        html += '}';

        html += '$(document).ready(function() {';
        html += 'if (typeof window.parent.initializeGridHandlers === "function") { window.parent.initializeGridHandlers(document); }';
        html += '$("#extract-text").on("click", function() { var extractedText = ""; if (typeof window.parent.extractSelectedCellsText === "function") { extractedText = window.parent.extractSelectedCellsText(); } else { extractedText = "' + sampleData + '"; } $("#extracted-text").text(extractedText); $("#extracted-text-container").show(); });';
        html += 'var select = $("#predefined-query"); if (select.length > 0) { select.empty(); select.append($("<option></option>").attr("value", "").text("-- Select a query --")); $.ajax({ url: window.ajaxurl, type: "POST", data: { action: "office_addin_get_queries", nonce: window.office_addin_preview_params.nonce }, success: function(response) { if (response.success && response.data && response.data.length > 0) { $.each(response.data, function(i, query) { select.append($("<option></option>").attr("value", query.id).text(query.title)); }); } else { var fallbackQueries = [{ id: 1, title: "Analyze financial performance" }, { id: 2, title: "Identify key trends" }]; $.each(fallbackQueries, function(i, query) { select.append($("<option></option>").attr("value", query.id).text(query.title)); }); } }, error: function() { var fallbackQueries = [{ id: 1, title: "Analyze financial performance" }, { id: 2, title: "Identify key trends" }]; $.each(fallbackQueries, function(i, query) { select.append($("<option></option>").attr("value", query.id).text(query.title)); }); } }); }';
        html += '$("#analyze-button").on("click", function() { var extractedText = $("#extracted-text").text(); if (!extractedText) { alert("Please extract text from Excel first."); return; } var queryId = $("#predefined-query").val(); var customQuery = $("#custom-query").val(); if (!queryId && !customQuery) { alert("Please select a predefined query or enter a custom question."); return; } var $results = $("#analysis-results"); $results.show().html("<div style=\\"text-align: center; padding: 20px;\\"><div class=\\"spinner\\" style=\\"display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;\\"></div><p style=\\"margin-top: 10px;\\">Analyzing data with real API...</p></div>"); $.ajax({ url: window.ajaxurl, type: "POST", data: { action: "office_addin_analyze", text: extractedText, query_id: queryId, custom_query: customQuery, nonce: window.office_addin_preview_params.nonce }, timeout: 120000, success: function(response) { if (response.success && response.data && response.data.result) { $results.html("<div style=\\"background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 15px;\\"><h4 style=\\"color: #155724; margin-top: 0;\\">✅ Analysis Results</h4>" + response.data.result + "</div>"); } else { $results.html("<div style=\\"background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px;\\"><h4 style=\\"color: #721c24; margin-top: 0;\\">❌ Analysis Failed</h4><p>" + (response.data && response.data.message ? response.data.message : "The analysis request failed. Please check your API configuration.") + "</p></div>"); } }, error: function(xhr, status, error) { console.error("Analysis AJAX error:", { status: status, error: error, responseText: xhr.responseText }); if (status === "timeout") { $results.html("<div style=\\"background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px;\\"><h4 style=\\"color: #856404; margin-top: 0;\\">⏱️ Request Timeout</h4><p>The analysis request timed out. Please try again with a smaller dataset or check your connection.</p></div>"); } else { $results.html("<div style=\\"background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px;\\"><h4 style=\\"color: #721c24; margin-top: 0;\\">🔌 Connection Error</h4><p>Unable to connect to the analysis server. Please check your API settings and try again.</p></div>"); } } }); });';
        html += '$("#test-connection").on("click", function() { console.log("Testing API connection..."); $("#api-status").text("Testing..."); $.ajax({ url: window.ajaxurl, type: "POST", data: { action: "check_ai_status", nonce: window.office_addin_preview_params.nonce }, success: function(response) { if (response.success) { $("#api-status").text("Connected").removeClass("api-error api-disconnected").addClass("api-connected"); if (response.data.model) { $("#selected-model").text(response.data.model); } else { $("#selected-model").text("AI Model Ready"); } } else { $("#api-status").text("Not Connected").removeClass("api-connected").addClass("api-error"); $("#selected-model").text("None"); } }, error: function(xhr, status, error) { console.error("Connection test failed:", error); $("#api-status").text("Connection Error").removeClass("api-connected").addClass("api-error"); $("#selected-model").text("Error"); } }); });';
        html += '});';
        html += 'window.checkAIConnectionStatus = checkAIConnectionStatus;';
        html += 'window.loadRealApiSettings = loadRealApiSettings;';
        html += 'window.updateApiStatusDisplay = updateApiStatusDisplay;';
        html += '$(document).ready(function() { setTimeout(function() { checkAIConnectionStatus(); }, 1000); });';
        html += 'var spinnerCSS = "@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }"; var style = document.createElement("style"); style.textContent = spinnerCSS; document.head.appendChild(style);';
        html += '</script></head><body>' + addinContent + '</body></html>';

        return html;
    }

    /**
     * Aggiorna il contenuto dell'iframe
     *
     * @param {string} html Il contenuto HTML da inserire nell'iframe
     */
    function updateIframeContent(html) {
        try {
            // Check if iframe exists
            if (!previewFrame) {
                previewFrame = document.getElementById('addin-preview-frame');
                if (!previewFrame) {
                    console.error('Preview frame not found');
                    return;
                }
            }

            // Access the iframe document
            var frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;

            if (!frameDoc) {
                console.error('Could not access iframe document');
                return;
            }

            // Create a blob URL for the content instead of using document.write
            var blob = new Blob([html], { type: 'text/html' });
            var blobURL = URL.createObjectURL(blob);

            // Set the iframe src to the blob URL
            previewFrame.onload = function() {
                // Clean up the blob URL
                URL.revokeObjectURL(blobURL);

                try {
                    // Initialize event handlers after content is loaded
                    console.log('Iframe loaded via blob URL, initializing content...');
                    setTimeout(initializePreviewContent, 300);
                } catch (err) {
                    console.error('Error initializing content after blob load:', err);
                }
            };

            // Set the source to load the content
            previewFrame.src = blobURL;

            console.log('Iframe content update initiated');
        } catch (error) {
            console.error('Error updating iframe content:', error);

            // Tenta di recuperare mostrando un messaggio di errore nell'iframe
            try {
                var errorFrame = document.getElementById('addin-preview-frame');
                if (errorFrame) {
                    var errorDoc = errorFrame.contentDocument || errorFrame.contentWindow.document;
                    var errorWin = errorFrame.contentWindow;

                    // Create or get existing elements
                    var htmlElement = errorDoc.documentElement || errorDoc.createElement('html');
                    var headElement = errorDoc.head || errorDoc.getElementsByTagName('head')[0] || errorDoc.createElement('head');
                    var bodyElement = errorDoc.body || errorDoc.getElementsByTagName('body')[0] || errorDoc.createElement('body');

                    // Clear body content
                    bodyElement.innerHTML = '';

                    // Add meta and styles to head if not already present
                    if (!headElement.querySelector('meta[charset]')) {
                        var meta = errorDoc.createElement('meta');
                        meta.setAttribute('charset', 'utf-8');
                        headElement.appendChild(meta);
                    }

                    // Remove existing error styles and add new ones
                    var existingStyle = headElement.querySelector('#iframe-error-styles');
                    if (existingStyle) {
                        existingStyle.remove();
                    }

                    var style = errorDoc.createElement('style');
                    style.id = 'iframe-error-styles';
                    style.textContent = 'body { font-family: "Segoe UI", Arial, sans-serif; padding: 20px; margin: 0; } .error { color: #e74c3c; text-align: center; } pre { background: #f8f8f8; padding: 10px; border-radius: 4px; overflow: auto; text-align: left; } button { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 10px; } button:hover { background: #2980b9; }';
                    headElement.appendChild(style);

                    // Create error content
                    var title = errorDoc.createElement('h2');
                    title.textContent = 'Excel Add-in Preview Error';

                    var errorPara = errorDoc.createElement('p');
                    errorPara.className = 'error';
                    errorPara.textContent = 'Si è verificato un errore durante l\'aggiornamento dell\'anteprima.';

                    var errorPre = errorDoc.createElement('pre');
                    errorPre.textContent = error.message || 'Unknown error';

                    var reloadButton = errorDoc.createElement('button');
                    reloadButton.textContent = 'Ricarica';
                    reloadButton.addEventListener('click', function() {
                        errorWin.location.reload();
                    });

                    // Build error structure
                    bodyElement.appendChild(title);
                    bodyElement.appendChild(errorPara);
                    bodyElement.appendChild(errorPre);
                    bodyElement.appendChild(reloadButton);

                    // Ensure proper document structure only if elements don't exist
                    if (!errorDoc.head && headElement.parentNode !== htmlElement) {
                        htmlElement.appendChild(headElement);
                    }
                    if (!errorDoc.body && bodyElement.parentNode !== htmlElement) {
                        htmlElement.appendChild(bodyElement);
                    }
                    // Only append htmlElement if it's not already in the document
                    if (!errorDoc.documentElement && htmlElement.parentNode !== errorDoc) {
                        errorDoc.appendChild(htmlElement);
                    }
                }
            } catch (recoveryError) {
                console.error('Failed to show error message in iframe:', recoveryError);
            }
        }
    }

    /**
     * Initialize event handlers and content in the preview iframe after it's loaded
     */
    function initializePreviewContent() {
        if (!previewFrame) {
            console.error('Preview frame not available for initialization');
            return;
        }

        try {
            var frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
            var frameWin = previewFrame.contentWindow;

            console.log('Initializing preview content after frame load');

            // Wait for DOM to be fully ready in the iframe
            if (!frameDoc || frameDoc.readyState !== 'complete') {
                console.log('Document not yet ready, scheduling retry...');
                setTimeout(initializePreviewContent, 200);
                return;
            }

            console.log('Document ready state:', frameDoc.readyState);

            // Debug: Log iframe content to verify HTML is loaded correctly
            console.log('Iframe HTML content preview:');
            try {
                var bodyContent = frameDoc.body ? frameDoc.body.innerHTML : 'No body found';
                var contentPreview = bodyContent.substring(0, 500);
                console.log('Body content (first 500 chars):', contentPreview);

                // Check for specific elements in the raw HTML
                var hasExtractText = bodyContent.indexOf('id="extract-text"') !== -1;
                var hasAnalyzeButton = bodyContent.indexOf('id="analyze-button"') !== -1;
                var hasPredefinedQuery = bodyContent.indexOf('id="predefined-query"') !== -1;

                console.log('Element presence in HTML:');
                console.log('- extract-text:', hasExtractText ? 'FOUND' : 'NOT FOUND');
                console.log('- analyze-button:', hasAnalyzeButton ? 'FOUND' : 'NOT FOUND');
                console.log('- predefined-query:', hasPredefinedQuery ? 'FOUND' : 'NOT FOUND');
            } catch (debugError) {
                console.error('Error during debug content check:', debugError);
            }

            // Initialize query dropdown with retry mechanism
            var maxRetries = 5;
            var retryCount = 0;

            function tryInitializeElements() {
                var select = $(frameDoc).find('#predefined-query');
                var extractBtn = $(frameDoc).find('#extract-text');
                var analyzeBtn = $(frameDoc).find('#analyze-button');

                console.log('Element check attempt ' + (retryCount + 1) + ':');
                console.log('- Predefined query dropdown:', select.length > 0 ? 'FOUND' : 'NOT FOUND');
                console.log('- Extract text button:', extractBtn.length > 0 ? 'FOUND' : 'NOT FOUND');
                console.log('- Analyze button:', analyzeBtn.length > 0 ? 'FOUND' : 'NOT FOUND');

                // Check if all critical elements are present
                var allElementsFound = select.length > 0 && extractBtn.length > 0 &&
                                     analyzeBtn.length > 0;

                if (allElementsFound) {
                    console.log('All elements found, proceeding with initialization...');
                    // Load queries and THEN attach event handlers
                    loadQueriesIntoPreviewDropdown(select, frameDoc, function() {
                        // Callback executed after queries are loaded
                        console.log('Queries loaded, now attaching event handlers...');
                        attachIframeEventHandlers(frameDoc, frameWin, true);
                    });
                } else {
                    retryCount++;
                    if (retryCount < maxRetries) {
                        console.log('Some elements not found, retry ' + retryCount + '/' + maxRetries + ' in 300ms...');
                        setTimeout(tryInitializeElements, 300);
                    } else {
                        console.error('Could not find all required elements after ' + maxRetries + ' attempts');
                        console.log('Proceeding with partial initialization...');
                        // Try to load queries if dropdown exists
                        if (select.length > 0) {
                            loadQueriesIntoPreviewDropdown(select, frameDoc, function() {
                                attachIframeEventHandlers(frameDoc, frameWin, false);
                            });
                        } else {
                            // Attach event handlers anyway for elements that might exist, but don't show warnings
                            attachIframeEventHandlers(frameDoc, frameWin, false);
                        }
                    }
                }
            }

            // Start the element initialization process
            tryInitializeElements();

            // Hide loading indicator if present
            $('#addin-preview-loading').hide();

            console.log('Preview content initialized successfully');
        } catch (error) {
            console.error('Error initializing preview content:', error);
            showErrorInPreview('Error initializing preview: ' + error.message);
        }
    }

    /**
     * Attach event handlers to elements in the iframe
     * @param {Document} frameDoc The iframe document
     * @param {Window} frameWin The iframe window
     * @param {boolean} showWarnings Whether to show warnings for missing elements
     */
    function attachIframeEventHandlers(frameDoc, frameWin, showWarnings) {
        // Default to showing warnings if not specified
        if (typeof showWarnings === 'undefined') {
            showWarnings = true;
        }
        // Validate parameters
        if (!frameDoc || !frameWin) {
            console.error('Invalid parameters for attachIframeEventHandlers');
            return;
        }

        try {
            // Initialize grid handlers first
            initializeGridHandlers(frameDoc);

            // Extract Text button (Real Production Behavior)
            var extractTextBtn = $(frameDoc).find('#extract-text');
            if (extractTextBtn.length > 0) {
                extractTextBtn.off('click').on('click', function() {
                    console.log('Extract text button clicked in iframe - using real extraction logic');

                    // In a real Excel add-in, this would extract from Excel cells
                    // In our test environment, we extract from the external grid
                    var extractedText = extractSelectedCellsText();

                    // Show the extracted text in the iframe
                    $(frameDoc).find('#extracted-text').text(extractedText);
                    $(frameDoc).find('#extracted-text-container').show();

                    // Also update the external grid results for consistency
                    $('#extracted-text-display').text(extractedText);
                    $('#extraction-results').show();

                    console.log('Text extracted and displayed in both iframe and external grid:', extractedText.substring(0, 100) + '...');

                    // Scroll to show the extracted text
                    var $container = $(frameDoc).find('#extracted-text-container');
                    if ($container.length > 0) {
                        $container[0].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                });
            } else {
                if (showWarnings) {
                    console.warn('Extract text button not found in iframe');
                }
            }

            // Gestione delle richieste predefinite (predefined query dropdown)
            var predefinedQueryDropdown = $(frameDoc).find('#predefined-query');
            if (predefinedQueryDropdown.length > 0) {
                console.log('Found predefined query dropdown element, attaching event handlers...');

                // Verifica che loadedQueries sia disponibile
                if (!loadedQueries || loadedQueries.length === 0) {
                    console.warn('LoadedQueries not available, event handlers may not work properly');
                }

                // Aggiungi un event listener diretto usando JavaScript nativo (metodo primario)
                try {
                    var rawDropdown = frameDoc.getElementById('predefined-query');
                    if (rawDropdown) {
                        console.log('Attaching native event listener to dropdown');

                        rawDropdown.addEventListener('change', function(event) {
                            console.log('Native dropdown change event fired! Selected value:', this.value);
                            var selectedValue = this.value;

                            if (selectedValue && selectedValue !== '') {
                                // Find the selected query from loaded queries
                                var selectedQuery = loadedQueries.find(function(query) {
                                    return query.id == selectedValue;
                                });

                                if (selectedQuery && selectedQuery.query_text) {
                                    // Trova il campo di testo della query personalizzata
                                    var queryField = frameDoc.getElementById('custom-query');
                                    if (queryField) {
                                        console.log('Copying query_text to custom query field:', selectedQuery.title);

                                        // Imposta il valore con il query_text (domanda estesa)
                                        queryField.value = selectedQuery.query_text;

                                        // Aggiungi la classe per l'animazione di evidenziazione
                                        queryField.classList.add('textarea-highlight');

                                        // Rimuovi la classe dopo la fine dell'animazione
                                        setTimeout(function() {
                                            queryField.classList.remove('textarea-highlight');
                                        }, 1500);

                                        // Sposta il focus sul textarea
                                        queryField.focus();

                                        console.log('Query text copied successfully');
                                    } else {
                                        console.error('Custom query field not found by ID');
                                    }
                                } else {
                                    console.warn('Selected query not found in loaded queries or missing query_text');
                                }
                            }
                        });
                        console.log('Native event listener attached successfully');
                    } else {
                        console.warn('Could not find raw dropdown by ID');
                    }
                } catch (err) {
                    console.error('Error setting up native event handler:', err);
                }

                // Manteniamo anche il metodo jQuery come backup
                predefinedQueryDropdown.off('change').on('change', function() {
                    console.log('jQuery change event fired on dropdown! Selected value:', $(this).val());
                    var selectedValue = $(this).val();

                    if (selectedValue && selectedValue !== '') {
                        // Find the selected query from loaded queries
                        var selectedQuery = loadedQueries.find(function(query) {
                            return query.id == selectedValue;
                        });

                        if (selectedQuery && selectedQuery.query_text) {
                            var customQueryField = $(frameDoc).find('#custom-query');

                            if (customQueryField.length > 0) {
                                console.log('jQuery: Copying query_text to custom query field:', selectedQuery.title);

                                // Inserisci il query_text (domanda estesa) nel campo di testo della query personalizzata
                                customQueryField.val(selectedQuery.query_text);

                                // Aggiungi la classe per l'animazione di evidenziazione
                                customQueryField.addClass('textarea-highlight');

                                // Rimuovi la classe dopo la fine dell'animazione
                                setTimeout(function() {
                                    customQueryField.removeClass('textarea-highlight');
                                }, 1500);

                                // Sposta il focus sul textarea
                                customQueryField.focus();

                                console.log('jQuery: Query text copied successfully');
                            } else {
                                console.error('jQuery: Custom query field not found');
                            }
                        } else {
                            console.warn('jQuery: Selected query not found in loaded queries or missing query_text');
                        }
                    }
                });
                console.log('jQuery event handler attached as backup');
            } else {
                if (showWarnings) {
                    console.warn('Predefined query dropdown not found in iframe');
                }
            }

            // Analyze Button
            var analyzeBtn = $(frameDoc).find('#analyze-button');
            if (analyzeBtn.length > 0) {
                analyzeBtn.off('click').on('click', function() {
                    console.log('Analyze button clicked in iframe');
                    var extractedText = $(frameDoc).find('#extracted-text').text();

                    if (!extractedText) {
                        frameWin.alert("Please extract text from Excel first.");
                        return;
                    }
                     var queryId = $(frameDoc).find('#predefined-query').val();
                    var customQuery = $(frameDoc).find('#custom-query').val();

                    if (!queryId && !customQuery) {
                        frameWin.alert("Please select a predefined query or enter a custom question.");
                        return;
                    }

                    // Store the selected query for reporting
                    if (queryId) {
                        try {
                            var selectedQuery = $(frameDoc).find('#predefined-query option:selected').text();
                            console.log('Selected predefined query:', selectedQuery);
                        } catch (e) {
                            console.warn('Could not get selected query text:', e);
                        }
                    }

                    // Show loading indicator
                    $(frameDoc).find('#analysis-results').html('<p>Analyzing data...</p>');

                    // Perform live analysis via AJAX
                    $.ajax({
                        url: window.ajaxurl || '/wp-admin/admin-ajax.php',
                        type: 'POST',
                        timeout: 30000, // 30 seconds timeout for analysis
                        data: {
                            action: 'office_addin_analyze',
                            text: extractedText,
                            query_id: queryId,
                            custom_query: customQuery,
                            nonce: window.office_addin_preview_params ? 
                                (window.office_addin_preview_params.settings_nonce || window.office_addin_preview_params.nonce) : ''
                        },
                        success: function(response) {
                            console.log('Live analysis response:', response);
                            if (response.success && response.data && response.data.result) {
                                $(frameDoc).find('#analysis-results').html(
                                    '<div class="analysis-success" style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 15px; margin: 10px 0;">' +
                                    '<h4 style="color: #155724; margin-top: 0; margin-bottom: 10px;">✅ Live Analysis Results</h4>' +
                                    '<div style="color: #155724;">' + response.data.result + '</div>' +
                                    '</div>'
                                );
                                console.log('Live analysis results displayed successfully');
                            } else {
                                var errorMsg = response.data && response.data.message ? response.data.message : 'Analysis failed';
                                $(frameDoc).find('#analysis-results').html(
                                    '<div class="error" style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin: 10px 0;">' +
                                    '<h4 style="color: #721c24; margin-top: 0; margin-bottom: 10px;">❌ Analysis Error</h4>' +
                                    '<p style="color: #721c24; margin: 0;">Error: ' + errorMsg + '</p>' +
                                    '</div>'
                                );
                                console.error('Analysis failed:', response);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('AJAX analysis error:', {xhr: xhr, status: status, error: error});
                            var errorText = 'Analysis Failed';
                            var errorDetails = '';
                            
                            if (status === 'timeout') {
                                errorText = 'Request Timeout';
                                errorDetails = 'The analysis request failed. Please check your API configuration.';
                            } else if (xhr.status === 403) {
                                errorText = 'Security Error';
                                errorDetails = 'Security error. Please refresh the page and try again.';
                            } else if (xhr.status === 500) {
                                errorText = 'Server Error';
                                errorDetails = 'Server error. Please check your API configuration.';
                            } else if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message) {
                                errorText = 'API Error';
                                errorDetails = xhr.responseJSON.data.message;
                            } else {
                                errorDetails = 'Network connection error. Please try again.';
                            }
                            
                            $(frameDoc).find('#analysis-results').html(
                                '<div class="error" style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin: 10px 0;">' +
                                '<h4 style="color: #721c24; margin-top: 0; margin-bottom: 10px;">❌ ' + errorText + '</h4>' +
                                '<p style="color: #721c24; margin: 0;">' + errorDetails + '</p>' +
                                '</div>'
                            );
                        }
                    });
                });
            } else {
                if (showWarnings) {
                    console.warn('Analyze button not found in iframe');
                }
            }

            // Update API status automatically based on settings
            updateApiStatusInIframe(frameDoc);

            console.log('Event handlers attached to iframe elements successfully');
        } catch (error) {
            console.error('Error attaching iframe event handlers:', error);
        }
    }

    /**
     * Load predefined queries into the dropdown in the preview
     */
    function loadQueriesIntoPreviewDropdown(select, frameDoc, callback) {
        // Clear existing options
        select.empty();
        select.append($('<option></option>').attr('value', '').text('-- Select a query --'));

        console.log('Loading queries into dropdown, ajaxurl:', window.ajaxurl);

        try {
            // Get base URL for AJAX if not already set
            if (!window.ajaxurl) {
                window.ajaxurl = getBaseUrl() + '/wp-admin/admin-ajax.php';
                console.log('Set AJAX URL from base:', window.ajaxurl);
            }

            // Enhanced validation for AJAX URL
            var isValidAjaxUrl = window.ajaxurl &&
                                window.ajaxurl.indexOf('undefined') === -1 &&
                                window.ajaxurl.indexOf('/wp-admin/admin-ajax.php') !== -1;
        } catch (error) {
            console.error('Error setting AJAX URL:', error);
            var isValidAjaxUrl = false;
        }

        if (!isValidAjaxUrl) {
            console.warn('Invalid AJAX URL detected: ' + window.ajaxurl + ', using fallback queries');
            populateFallbackQueries(select, callback);
            return;
        }

        // Load predefined queries from the server with extended timeout and retry
        var ajaxRequest = $.ajax({
            url: window.ajaxurl,
            type: 'POST',
            timeout: 15000, // Extended to 15 second timeout for better reliability
            data: {
                action: 'office_addin_get_queries', // Updated action name
                nonce: window.office_addin_preview_params ?
                    (window.office_addin_preview_params.queries_nonce || window.office_addin_preview_params.nonce) : ''
            },
            success: function(response) {
                console.log('Server response received:', response);
                if (response && response.success && response.data && response.data.length > 0) {
                    // Store queries in global variable for access by event handlers
                    loadedQueries = response.data;

                    // Populate dropdown with queries from database
                    $.each(response.data, function(i, query) {
                        select.append($('<option></option>').attr('value', query.id).text(query.title));
                    });
                    console.log('Preview dropdown populated with ' + response.data.length + ' queries');

                    // Execute callback after successful loading
                    if (typeof callback === 'function') {
                        callback();
                    }
                } else {
                    console.warn('No predefined queries found or invalid response format');
                    // Use fallback queries
                    populateFallbackQueries(select, callback);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading predefined queries:', error, 'Status:', status, 'Response:', xhr.responseText);

                // Implement progressive retry with exponential backoff
                if (status === 'timeout' || status === 'error') {
                    console.log('🔄 Implementing progressive retry for predefined queries...');
                    retryPredefinedQueriesWithBackoff(select, callback, 1);
                } else {
                    // Use fallback queries for non-retryable errors
                    populateFallbackQueries(select, callback);
                }
            }
        });
    }

    /**
     * Retry predefined queries with exponential backoff
     */
    function retryPredefinedQueriesWithBackoff(select, callback, attempt) {
        var maxAttempts = 3;
        var baseDelay = 1000; // 1 second base delay

        if (attempt > maxAttempts) {
            console.warn('Max retry attempts reached for predefined queries, using fallback');
            populateFallbackQueries(select, callback);
            return;
        }

        var delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
        console.log(`Retry attempt ${attempt}/${maxAttempts} in ${delay}ms...`);

        setTimeout(function() {
            var altAjaxUrl = location.protocol + '//' + location.hostname + '/wp-admin/admin-ajax.php';
            console.log('Retrying with alternate URL (attempt ' + attempt + '):', altAjaxUrl);

            $.ajax({
                url: altAjaxUrl,
                type: 'POST',
                timeout: 15000 + (attempt * 5000), // Progressive timeout increase
                data: {
                    action: 'office_addin_get_queries',
                    nonce: window.office_addin_preview_params ?
                        (window.office_addin_preview_params.queries_nonce || window.office_addin_preview_params.nonce) : ''
                },
                success: function(retryResponse) {
                    if (retryResponse && retryResponse.success && retryResponse.data && retryResponse.data.length > 0) {
                        // Store queries in global variable for access by event handlers
                        loadedQueries = retryResponse.data;

                        // Clear and repopulate
                        select.empty();
                        select.append($('<option></option>').attr('value', '').text('-- Select a query --'));

                        // Populate dropdown with queries from database
                        $.each(retryResponse.data, function(i, query) {
                            select.append($('<option></option>').attr('value', query.id).text(query.title));
                        });
                        console.log('Preview dropdown populated with ' + retryResponse.data.length + ' queries after retry');

                        // Execute callback after successful retry
                        if (typeof callback === 'function') {
                            callback();
                        }
                    } else {
                        // Retry failed, try again or use fallback
                        retryPredefinedQueriesWithBackoff(select, callback, attempt + 1);
                    }
                },
                error: function(retryXhr, retryStatus, retryError) {
                    console.error('Retry attempt ' + attempt + ' failed:', retryError);
                    // Try again or use fallback
                    retryPredefinedQueriesWithBackoff(select, callback, attempt + 1);
                }
            });
        }, delay);
    }

    /**
     * Populate dropdown with fallback queries when server request fails
     */
    function populateFallbackQueries(select, callback) {
        if (!select || select.length === 0) {
            console.error('Invalid select element provided to populateFallbackQueries');
            return;
        }

        // More comprehensive set of fallback queries
        var fallbackQueries = [
            { id: 1, title: "Analyze financial performance", query_text: "Please analyze the financial performance data in this document, highlighting key metrics, trends, and areas of concern or opportunity." },
            { id: 2, title: "Identify key trends", query_text: "Identify and explain the key trends visible in this financial data, including patterns in revenue, expenses, and growth over time." },
            { id: 3, title: "Summarize quarterly results", query_text: "Provide a comprehensive summary of the quarterly financial results, including performance against targets and comparison with previous periods." },
            { id: 4, title: "Compare regional performance", query_text: "Compare the financial performance across different regions or business segments, identifying strengths and weaknesses in each area." },
            { id: 5, title: "Assess risk factors", query_text: "Analyze the financial data to identify potential risk factors, areas of vulnerability, and recommendations for risk mitigation." },
            { id: 6, title: "Evaluate growth opportunities", query_text: "Based on the financial data provided, identify and evaluate potential growth opportunities and areas for business expansion." },
            { id: 7, title: "Analyze profit margins", query_text: "Analyze the profit margins across different products, services, or business units, identifying areas for improvement and optimization." },
            { id: 8, title: "Identify cost reduction opportunities", query_text: "Examine the expense data to identify opportunities for cost reduction and operational efficiency improvements." },
            { id: 9, title: "Break down expenses by category", query_text: "Provide a detailed breakdown and analysis of expenses by category, highlighting the largest cost drivers and unusual expenditures." },
            { id: 10, title: "Project revenue for next quarter", query_text: "Based on current financial trends and data, provide a projection for revenue performance in the next quarter with supporting analysis." }
        ];

        try {
            // Clear existing options and add default first
            select.empty();
            select.append($('<option></option>').attr('value', '').text('-- Select a query --'));

            // Add each query to the dropdown
            $.each(fallbackQueries, function(i, query) {
                select.append($('<option></option>').attr('value', query.id).text(query.title));
            });

            // Store queries in global variable for access by event handlers
            loadedQueries = fallbackQueries;

            console.log('Dropdown populated with ' + fallbackQueries.length + ' fallback queries');

            // Execute callback after populating fallback queries
            if (typeof callback === 'function') {
                callback();
            }

            // Trigger a change event to ensure any listeners know the dropdown has been populated
            select.trigger('change');
        } catch (error) {
            console.error('Error populating fallback queries:', error);
        }
    }

    /**
     * Commuta tra le diverse dimensioni dell'anteprima
     * Cicla tra normale -> grande -> full-height -> normale
     * Mantiene la larghezza fissa che è standard per Excel add-in
     */
    function togglePreviewSize() {
        var container = $('.addin-preview-container');
        var icon = $('#toggle-preview-size .dashicons');

        // Remove all existing size classes
        container.removeClass('addin-preview-large addin-preview-fullheight');

        // Cycle through modes: normal -> large -> fullheight -> normal
        switch (previewMode) {
            case 'normal':
                // Switch to large mode
                container.addClass('addin-preview-large');
                icon.removeClass('dashicons-editor-expand dashicons-fullscreen-alt').addClass('dashicons-editor-contract');
                previewMode = 'large';
                console.log('Switched to large preview mode');
                break;

            case 'large':
                // Switch to full-height mode
                container.addClass('addin-preview-fullheight');
                icon.removeClass('dashicons-editor-expand dashicons-editor-contract').addClass('dashicons-fullscreen-alt');
                previewMode = 'fullheight';
                console.log('Switched to full-height preview mode');
                break;

            case 'fullheight':
                // Switch back to normal mode
                icon.removeClass('dashicons-editor-contract dashicons-fullscreen-alt').addClass('dashicons-editor-expand');
                previewMode = 'normal';
                console.log('Switched to normal preview mode');
                break;

            default:
                // Reset to normal mode as fallback
                icon.removeClass('dashicons-editor-contract dashicons-fullscreen-alt').addClass('dashicons-editor-expand');
                previewMode = 'normal';
                console.log('Reset to normal preview mode');
                break;
        }

        // Aggiorna l'anteprima dopo il ridimensionamento
        setTimeout(updatePreview, 100);
    }

    /**
     * Show error message in the preview iframe
     *
     * @param {string} message The error message to display
     */
    function showErrorInPreview(message) {
        if (previewFrame) {
            try {
                var frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
                var frameWin = previewFrame.contentWindow;

                // Create or get existing elements
                var htmlElement = frameDoc.documentElement || frameDoc.createElement('html');
                var headElement = frameDoc.head || frameDoc.getElementsByTagName('head')[0] || frameDoc.createElement('head');
                var bodyElement = frameDoc.body || frameDoc.getElementsByTagName('body')[0] || frameDoc.createElement('body');

                // Clear body content
                bodyElement.innerHTML = '';

                // Add meta and styles to head if not already present
                if (!headElement.querySelector('meta[charset]')) {
                    var meta = frameDoc.createElement('meta');
                    meta.setAttribute('charset', 'utf-8');
                    headElement.appendChild(meta);
                }

                // Remove existing error styles and add new ones
                var existingStyle = headElement.querySelector('#error-styles');
                if (existingStyle) {
                    existingStyle.remove();
                }

                var style = frameDoc.createElement('style');
                style.id = 'error-styles';
                style.textContent = `
                    body { font-family: "Segoe UI", Arial, sans-serif; padding: 20px; margin: 0; }
                    .error { color: #e74c3c; text-align: center; margin: 40px auto; }
                    .error button {
                        background: #3498db;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 4px;
                        cursor: pointer;
                        margin-top: 10px;
                    }
                    .error button:hover { background: #2980b9; }
                `;
                headElement.appendChild(style);

                // Create error content
                var errorDiv = frameDoc.createElement('div');
                errorDiv.className = 'error';

                var errorTitle = frameDoc.createElement('h3');
                errorTitle.textContent = 'Preview Error';

                var errorMessage = frameDoc.createElement('p');
                errorMessage.textContent = message || 'An error occurred while loading the preview.';

                var retryButton = frameDoc.createElement('button');
                retryButton.textContent = 'Try Again';
                retryButton.addEventListener('click', function() {
                    if (window.parent && window.parent.document) {
                        var refreshButton = window.parent.document.getElementById('refresh-preview');
                        if (refreshButton) refreshButton.click();
                    }
                });

                // Build error structure
                errorDiv.appendChild(errorTitle);
                errorDiv.appendChild(errorMessage);
                errorDiv.appendChild(retryButton);
                bodyElement.appendChild(errorDiv);

                // Ensure proper document structure only if elements don't exist
                if (!frameDoc.head && headElement.parentNode !== htmlElement) {
                    htmlElement.appendChild(headElement);
                }
                if (!frameDoc.body && bodyElement.parentNode !== htmlElement) {
                    htmlElement.appendChild(bodyElement);
                }
                // Only append htmlElement if it's not already in the document
                if (!frameDoc.documentElement && htmlElement.parentNode !== frameDoc) {
                    frameDoc.appendChild(htmlElement);
                }

            } catch(e) {
                console.error('Failed to show error in preview:', e);
            }
        }
    }

    /**
     * Show a loading indicator in the preview
     */
    function showLoadingInPreview() {
        if (previewFrame) {
            try {
                var frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
                var frameWin = previewFrame.contentWindow;

                // Create or get existing elements
                var htmlElement = frameDoc.documentElement || frameDoc.createElement('html');
                var headElement = frameDoc.head || frameDoc.getElementsByTagName('head')[0] || frameDoc.createElement('head');
                var bodyElement = frameDoc.body || frameDoc.getElementsByTagName('body')[0] || frameDoc.createElement('body');

                // Clear body content
                bodyElement.innerHTML = '';

                // Add meta and styles to head if not already present
                if (!headElement.querySelector('meta[charset]')) {
                    var meta = frameDoc.createElement('meta');
                    meta.setAttribute('charset', 'utf-8');
                    headElement.appendChild(meta);
                }

                // Remove existing loading styles and add new ones
                var existingStyle = headElement.querySelector('#loading-styles');
                if (existingStyle) {
                    existingStyle.remove();
                }

                var style = frameDoc.createElement('style');
                style.id = 'loading-styles';
                style.textContent = `
                    body { font-family: "Segoe UI", Arial, sans-serif; padding: 20px; text-align: center; margin: 0; }
                    .loading { margin: 40px auto; }
                    .loading-spinner {
                        border: 4px solid #f3f3f3;
                        border-top: 4px solid #3498db;
                        border-radius: 50%;
                        width: 30px;
                        height: 30px;
                        animation: spin 1s linear infinite;
                        margin: 0 auto 20px;
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `;
                headElement.appendChild(style);

                // Create loading content
                var loadingDiv = frameDoc.createElement('div');
                loadingDiv.className = 'loading';

                var spinnerDiv = frameDoc.createElement('div');
                spinnerDiv.className = 'loading-spinner';

                var loadingText = frameDoc.createElement('p');
                loadingText.textContent = 'Loading preview...';

                // Build loading structure
                loadingDiv.appendChild(spinnerDiv);
                loadingDiv.appendChild(loadingText);
                bodyElement.appendChild(loadingDiv);

                // Ensure proper document structure only if elements don't exist
                if (!frameDoc.head && headElement.parentNode !== htmlElement) {
                    htmlElement.appendChild(headElement);
                }
                if (!frameDoc.body && bodyElement.parentNode !== htmlElement) {
                    htmlElement.appendChild(bodyElement);
                }
                // Only append htmlElement if it's not already in the document
                if (!frameDoc.documentElement && htmlElement.parentNode !== frameDoc) {
                    frameDoc.appendChild(htmlElement);
                }

            } catch(e) {
                console.error('Failed to show loading in preview:', e);
            }
        }
    }

    /**
     * Ottieni il contenuto HTML dell'add-in dal nuovo sistema
     * This function provides the default HTML template for the Office Add-in
     *
     * @return {string} Il contenuto HTML dell'add-in
     */
    function getNewSystemAddinContent() {
        return `
<div class="excel-addin-container">
    <h2>Financial Advisor Excel Add-in</h2>

    <div class="section">
        <h3>1. Extract Text from Excel</h3>

        <p>Select cells in your Excel sheet (or use the external grid for testing) and click the button below to extract text for analysis.</p>
        <button id="extract-text" class="primary-button" title="Extract text from selected cells">
            <span class="button-icon">📋</span> Extract Selected Cells
        </button>
        <div id="extracted-text-container" style="display:none;">
            <h4>Extracted Text:</h4>
            <div id="extracted-text" class="text-display" style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 8px; margin-top: 10px; background: #f9f9f9;"></div>
        </div>
    </div>

    <div class="section">
        <h3>2. Choose Analysis Question</h3>
        <div class="form-group">
            <label for="predefined-query">Select a predefined query:</label>
            <select id="predefined-query" class="full-width" title="Select from predefined analysis questions" style="background-color: #ffffff; color: #333333; border: 1px solid #d0d0d0; padding: 8px; border-radius: 4px;">
                <option value="">-- Select a query --</option>
                <!-- Predefined queries will be populated dynamically -->
            </select>
        </div>

        <div class="form-group">
            <label for="custom-query">Or enter your own question:</label>
            <textarea id="custom-query" rows="3" class="full-width" placeholder="Enter your analysis question here..." title="Enter a custom analysis question"></textarea>
        </div>

        <button id="analyze-button" class="primary-button" title="Send data for analysis">
            <span class="button-icon">🔍</span> Analyze
        </button>
    </div>

    <div class="section">
        <h3>3. Analysis Results</h3>
        <div id="analysis-results" class="results-container" style="min-height: 100px; border: 1px solid #eee; padding: 10px; margin-top: 10px; display: none;">
            <!-- Results will appear here when analysis is complete -->
        </div>
    </div>



    <div id="addin-ai-status-indicator" class="chat-ai-status-indicator connecting" style="margin:20px auto 0 auto;max-width:300px;">
        <span class="chat-ai-status-icon">🔄</span>
        <span class="chat-ai-status-text">Verifica...</span>
    </div>
    <div class="footer" style="margin-top: 20px; padding-top: 10px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p>This add-in connects to your Financial Advisor WordPress plugin and uses the same API settings.</p>
        <p>Version 2.0 - ${new Date().toISOString().split('T')[0]}</p>
    </div>
</div>`;
    }

    /**
     * Update API status in iframe automatically based on actual settings with enhanced error handling
     */
    function updateApiStatusInIframe(frameDoc) {
        try {
            // Make AJAX call to get actual API settings with timeout
            $.ajax({
                url: window.office_addin_preview_params ? window.office_addin_preview_params.ajax_url : (window.ajaxurl || '/wp-admin/admin-ajax.php'),
                type: 'POST',
                timeout: 15000, // Extended timeout
                data: {
                    action: 'office_addin_get_settings',
                    nonce: window.office_addin_preview_params ?
                        (window.office_addin_preview_params.settings_nonce || window.office_addin_preview_params.nonce) : ''
                },
                success: function(response) {
                    if (response.success) {
                        var apiStatus = response.data.api_configured ? 'Connected' : 'Not Connected';
                        var selectedModel = response.data.model || 'None';

                        $(frameDoc).find('#api-status').text(apiStatus);
                        $(frameDoc).find('#selected-model').text(selectedModel);

                        console.log('API status updated in iframe:', apiStatus, 'Model:', selectedModel);
                    } else {
                        $(frameDoc).find('#api-status').text('Settings load failed');
                        console.error('Failed to load API settings for preview');
                    }
                },
                error: function(xhr, status, error) {
                    var errorMessage = 'Settings load error';
                    if (status === 'timeout') {
                        errorMessage = 'Connection timeout';
                    } else if (status === 'error') {
                        errorMessage = 'Network error';
                    }

                    $(frameDoc).find('#api-status').text(errorMessage);
                    console.error('Error loading API settings for preview:', error, 'Status:', status);

                    // Implement connection status monitoring
                    startConnectionMonitoring(frameDoc);
                }
            });
        } catch (error) {
            console.error('Error updating API status in iframe:', error);
            $(frameDoc).find('#api-status').text('Status error');
        }
    }

    /**
     * Start connection monitoring for automatic reconnection attempts
     */
    function startConnectionMonitoring(frameDoc) {
        // Avoid multiple monitoring instances
        if (window.connectionMonitorInterval) {
            clearInterval(window.connectionMonitorInterval);
        }

        console.log('🔍 Starting connection monitoring...');

        window.connectionMonitorInterval = setInterval(function() {
            // Check if iframe still exists
            if (!frameDoc || !$(frameDoc).find('#api-status').length) {
                clearInterval(window.connectionMonitorInterval);
                return;
            }

            // Attempt to reconnect
            $.ajax({
                url: window.ajaxurl || '/wp-admin/admin-ajax.php',
                type: 'POST',
                timeout: 10000,
                data: {
                    action: 'office_addin_get_settings',
                    nonce: window.office_addin_preview_params ? window.office_addin_preview_params.nonce : ''
                },
                success: function(response) {
                    if (response.success && response.data) {
                        console.log('🔄 Connection restored!');
                        var apiStatus = response.data.api_configured ? 'Connected' : 'Not Connected';
                        var selectedModel = response.data.model || 'None';

                        $(frameDoc).find('#api-status').text(apiStatus);
                        $(frameDoc).find('#selected-model').text(selectedModel);

                        // Stop monitoring once connection is restored
                        clearInterval(window.connectionMonitorInterval);
                    }
                },
                error: function() {
                    console.log('🔍 Still monitoring connection...');
                    $(frameDoc).find('#api-status').text('Reconnecting...');
                }
            });
        }, 10000); // Check every 10 seconds
    }

    /**
     * Start periodic connection checking
     */
    function startPeriodicConnectionCheck() {
        // Clear any existing interval
        if (window.connectionCheckInterval) {
            clearInterval(window.connectionCheckInterval);
        }

        console.log('🔄 Starting periodic connection monitoring...');

        // Check connection every 5 minutes (300 seconds) to avoid rate limiting
        window.connectionCheckInterval = setInterval(function() {
            if (window.connectionStatus === 'connected') {
                // Quick health check
                $.ajax({
                    url: window.ajaxurl || '/wp-admin/admin-ajax.php',
                    type: 'POST',
                    timeout: 5000,
                    data: {
                        action: 'office_addin_get_settings',
                        nonce: window.office_addin_preview_params ?
                            (window.office_addin_preview_params.settings_nonce || window.office_addin_preview_params.nonce) : ''
                    },
                    success: function(response) {
                        if (response.success) {
                            window.lastConnectionCheck = Date.now();
                            console.log('🔄 Connection health check passed');
                        }
                    },
                    error: function() {
                        console.warn('🔄 Connection health check failed, switching to error state');
                        window.connectionStatus = 'error';
                        updateApiStatusInFrame('error', 'Connection lost');
                        scheduleConnectionRetry();
                    }
                });
            }
        }, 300000); // 5 minutes (300 seconds) to avoid rate limiting
    }

    /**
     * Schedule connection retry with exponential backoff
     */
    function scheduleConnectionRetry() {
        // Clear periodic check while in error state
        if (window.connectionCheckInterval) {
            clearInterval(window.connectionCheckInterval);
        }

        if (!window.retryAttempt) {
            window.retryAttempt = 1;
        }

        var maxRetries = 5;
        var baseDelay = 5000; // 5 seconds

        if (window.retryAttempt <= maxRetries) {
            var delay = baseDelay * Math.pow(2, window.retryAttempt - 1);
            console.log(`🔄 Scheduling connection retry ${window.retryAttempt}/${maxRetries} in ${delay/1000}s...`);

            setTimeout(function() {
                updateApiStatusInFrame('checking', 'Reconnecting...');
                checkAIConnectionStatus();
                window.retryAttempt++;
            }, delay);
        } else {
            console.warn('🔄 Max retry attempts reached, stopping automatic reconnection');
            updateApiStatusInFrame('error', 'Connection failed - check network');
            window.retryAttempt = 1; // Reset for future attempts
        }
    }

    // Cleanup on page unload
    $(window).on('beforeunload', function() {
        if (window.connectionCheckInterval) {
            clearInterval(window.connectionCheckInterval);
        }
        if (window.connectionMonitorInterval) {
            clearInterval(window.connectionMonitorInterval);
        }
    });

})(jQuery);
