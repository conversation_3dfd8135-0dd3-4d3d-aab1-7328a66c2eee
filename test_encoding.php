<?php
// Test encoding fix
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Test message with Italian characters
$test_message = "Questo è un test con caratteri italiani: à<PERSON><PERSON><PERSON><PERSON>óòù. Rate limit exceeded: 60 times (limit: 60)";

// Test direct error_log with UTF-8 conversion
error_log(mb_convert_encoding('[TEST ENCODING] ' . $test_message, 'UTF-8', 'auto'));

// Use the dv_debug_log function
if (function_exists('dv_debug_log')) {
    dv_debug_log($test_message, 'office_addin');
}

echo "Test completato. Verifica il file debug.log\n";
?>
