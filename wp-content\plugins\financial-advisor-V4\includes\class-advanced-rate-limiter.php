<?php
/**
 * Advanced Rate Limiter Class
 * 
 * Provides enhanced rate limiting with sliding window algorithm
 * 
 * @package Financial_Advisor_V4
 * @subpackage Security
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Advanced_Rate_Limiter {
    
    /**
     * Default rate limits (requests per time window)
     * @var array
     */
    private $default_limits = [
        'office_addin_get_settings' => ['requests' => 200, 'window' => 3600], // 200 per hour (more generous)
        'office_addin_analyze' => ['requests' => 30, 'window' => 3600],      // 30 per hour
        'office_addin_get_queries' => ['requests' => 60, 'window' => 3600],  // 60 per hour
        'office_addin_preview' => ['requests' => 120, 'window' => 3600],     // 120 per hour
    ];
    
    /**
     * Database table name
     * @var string
     */
    private $table_name;
    
    /**
     * Cache group
     * @var string
     */
    private $cache_group = 'office_addin_rate_limit';
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'office_addin_rate_limits';
        
        // Initialize cache group
        wp_cache_add_global_groups([$this->cache_group]);
        
        // Create database table if needed
        $this->create_rate_limit_table();
        
        // Schedule cleanup of old entries
        if (!wp_next_scheduled('office_addin_cleanup_rate_limits')) {
            wp_schedule_event(time(), 'hourly', 'office_addin_cleanup_rate_limits');
        }
        
        add_action('office_addin_cleanup_rate_limits', [$this, 'cleanup_old_entries']);
    }
    
    /**
     * Check if request is allowed using sliding window algorithm
     */
    public function check_rate_limit($identifier, $action, $custom_limit = null) {
        try {
            // Get rate limit configuration
            $limit_config = $this->get_rate_limit_config($action, $custom_limit);
            
            if (!$limit_config) {
                return true; // No limit configured
            }
            
            $requests_limit = $limit_config['requests'];
            $time_window = $limit_config['window'];
            
            // Use sliding window algorithm
            $current_time = time();
            $window_start = $current_time - $time_window;
            
            // Clean old entries first
            $this->cleanup_old_entries_for_identifier($identifier, $action, $window_start);
            
            // Count current requests in the window
            $current_count = $this->count_requests($identifier, $action, $window_start);
            
            if ($current_count >= $requests_limit) {
                // Rate limit exceeded
                $this->log_rate_limit_violation($identifier, $action, $current_count, $requests_limit);
                return false;
            }
            
            // Record this request
            $this->record_request($identifier, $action, $current_time);
            
            return true;
            
        } catch (Exception $e) {
            // On error, allow the request but log it
            error_log('Rate limiter error: ' . $e->getMessage());
            return true;
        }
    }
    
    /**
     * Get remaining requests for an identifier and action
     */
    public function get_remaining_requests($identifier, $action) {
        $limit_config = $this->get_rate_limit_config($action);
        
        if (!$limit_config) {
            return null; // No limit configured
        }
        
        $requests_limit = $limit_config['requests'];
        $time_window = $limit_config['window'];
        $window_start = time() - $time_window;
        
        $current_count = $this->count_requests($identifier, $action, $window_start);
        
        return max(0, $requests_limit - $current_count);
    }
    
    /**
     * Get time until rate limit resets
     */
    public function get_reset_time($identifier, $action) {
        global $wpdb;
        
        $limit_config = $this->get_rate_limit_config($action);
        
        if (!$limit_config) {
            return null;
        }
        
        $time_window = $limit_config['window'];
        
        // Get the oldest request in the current window
        $oldest_request = $wpdb->get_var($wpdb->prepare(
            "SELECT MIN(timestamp) FROM {$this->table_name} 
             WHERE identifier = %s AND action = %s 
             AND timestamp > %s",
            $identifier,
            $action,
            date('Y-m-d H:i:s', time() - $time_window)
        ));
        
        if (!$oldest_request) {
            return 0; // No requests in current window
        }
        
        $oldest_timestamp = strtotime($oldest_request);
        $reset_time = $oldest_timestamp + $time_window;
        
        return max(0, $reset_time - time());
    }
    
    /**
     * Manually block an identifier (for security violations)
     */
    public function block_identifier($identifier, $duration = 3600) {
        $this->record_request($identifier, 'BLOCKED', time(), $duration);
    }
    
    /**
     * Check if an identifier is blocked
     */
    public function is_blocked($identifier) {
        global $wpdb;
        
        $blocked_until = $wpdb->get_var($wpdb->prepare(
            "SELECT MAX(timestamp + INTERVAL %d SECOND) FROM {$this->table_name} 
             WHERE identifier = %s AND action = 'BLOCKED'",
            3600, // Default block duration
            $identifier
        ));
        
        if (!$blocked_until) {
            return false;
        }
        
        return strtotime($blocked_until) > time();
    }
    
    /**
     * Get rate limit statistics
     */
    public function get_statistics($hours = 24) {
        global $wpdb;
        
        $since = date('Y-m-d H:i:s', time() - ($hours * 3600));
        
        $stats = $wpdb->get_results($wpdb->prepare(
            "SELECT action, COUNT(*) as request_count, COUNT(DISTINCT identifier) as unique_identifiers
             FROM {$this->table_name} 
             WHERE timestamp > %s 
             GROUP BY action 
             ORDER BY request_count DESC",
            $since
        ), ARRAY_A);
        
        return $stats;
    }
    
    /**
     * Create database table for rate limiting
     */
    private function create_rate_limit_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            identifier varchar(100) NOT NULL,
            action varchar(50) NOT NULL,
            timestamp timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY identifier_action (identifier, action),
            KEY timestamp (timestamp)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Get rate limit configuration for an action
     */
    private function get_rate_limit_config($action, $custom_limit = null) {
        if ($custom_limit) {
            return $custom_limit;
        }
        
        // Check for custom configuration
        $custom_limits = get_option('office_addin_custom_rate_limits', []);
        if (isset($custom_limits[$action])) {
            return $custom_limits[$action];
        }
        
        // Use default configuration
        return isset($this->default_limits[$action]) ? $this->default_limits[$action] : null;
    }
    
    /**
     * Count requests in the current window
     */
    private function count_requests($identifier, $action, $window_start) {
        global $wpdb;
        
        return (int) $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->table_name} 
             WHERE identifier = %s AND action = %s 
             AND timestamp > %s",
            $identifier,
            $action,
            date('Y-m-d H:i:s', $window_start)
        ));
    }
    
    /**
     * Record a request
     */
    private function record_request($identifier, $action, $timestamp, $custom_duration = null) {
        global $wpdb;
        
        $wpdb->insert(
            $this->table_name,
            [
                'identifier' => $identifier,
                'action' => $action,
                'timestamp' => date('Y-m-d H:i:s', $timestamp)
            ],
            ['%s', '%s', '%s']
        );
    }
    
    /**
     * Clean old entries for a specific identifier and action
     */
    private function cleanup_old_entries_for_identifier($identifier, $action, $window_start) {
        global $wpdb;
        
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$this->table_name} 
             WHERE identifier = %s AND action = %s 
             AND timestamp <= %s",
            $identifier,
            $action,
            date('Y-m-d H:i:s', $window_start)
        ));
    }
    
    /**
     * Clean up old entries (called by cron)
     */
    public function cleanup_old_entries() {
        global $wpdb;
        
        // Remove entries older than 24 hours
        $cutoff_time = date('Y-m-d H:i:s', time() - (24 * 3600));
        
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$this->table_name} WHERE timestamp < %s",
            $cutoff_time
        ));
    }
    
    /**
     * Log rate limit violation
     */
    private function log_rate_limit_violation($identifier, $action, $current_count, $limit) {
        // Only log once per minute per identifier/action to reduce spam
        $warning_key = "rate_limit_warning_{$identifier}_{$action}";
        $already_warned = wp_cache_get($warning_key, 'office_addin_warnings');
        
        if (!$already_warned) {
            // Log to WordPress debug log if enabled
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log(sprintf(
                    'Rate limit exceeded: %s attempted %s (limit: %d/min) - suppressing further warnings for 60s',
                    $identifier,
                    $action,
                    $limit
                ));
            }
            
            // Set warning flag for 60 seconds to prevent spam
            wp_cache_set($warning_key, true, 'office_addin_warnings', 60);
        }
        
        // Hook for custom logging (always fire for monitoring)
        do_action('office_addin_rate_limit_exceeded', $identifier, $action, $current_count, $limit);
    }
}
