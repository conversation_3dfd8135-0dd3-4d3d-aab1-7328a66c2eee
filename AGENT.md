# AGENT.md - WordPress ClarFin Project Guide

## Build/Test Commands
- **PHP Tests**: `composer test` (runs PHPUnit from financial-advisor-V4 plugin)
- **Single Test**: `vendor/bin/phpunit tests/Unit/SpecificTest.php`
- **Test Coverage**: `composer test-coverage`
- **Unit Tests**: `composer test-unit`  
- **Integration Tests**: `composer test-integration`
- **Theme Build**: `npm run build` (in wp-content/themes/blocksy/)
- **Theme Dev**: `npm run dev` (in wp-content/themes/blocksy/)

## Architecture & Structure
- **WordPress Site**: Local development at claryfin.local
- **Main Plugin**: financial-advisor-V4 (document viewer with AI analysis)
- **Active Theme**: Blocksy with child theme (astra-child available)
- **Database**: MySQL (glopvhmy_wp249) with wpcd_ prefix
- **Key Plugin Features**: PDF/Word parsing, OCR, mPDF generation
- **Dependencies**: Composer autoloading (PSR-4), WP-CLI integration

## Code Style & Conventions
- **PHP**: PSR-4 autoloading, namespace `FinancialAdvisor\DocumentViewer`
- **Testing**: PHPUnit 9.5+, Brain/Monkey for WP mocking
- **JS/CSS**: Modern build process via Gulp (Blocksy theme)
- **Security**: WP nonces, sanitization, escaping required
- **Debug**: WP_DEBUG enabled in wp-config.php
