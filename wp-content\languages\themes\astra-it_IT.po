# Translation of Themes - Astra in Italian
# This file is distributed under the same license as the Themes - Astra package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-07-23 06:59:17+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: it\n"
"Project-Id-Version: Themes - Astra\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "The Astra WordPress theme is lightning-fast and highly customizable. It has over 1 million downloads and the only theme in the world with 5,700+ five-star reviews! It’s ideal for professional web designers, solopreneurs, small businesses, eCommerce, membership sites and any type of website. It offers special features and templates so it works perfectly with all page builders like Spectra, Elementor, Beaver Builder, etc. Fast performance, clean code, mobile-first design and schema markup are all built-in, making the theme exceptionally SEO-friendly. It’s fully compatible with WooCommerce, SureCart and other eCommerce plugins and comes with lots of store-friendly features and templates. Astra also provides expert support for free users. A dedicated team of fully trained WordPress experts are on hand to help with every aspect of the theme. Try the live demo of Astra: https://zipwp.org/themes/astra/"
msgstr "Il tema WordPress Astra è velocissimo e altamente personalizzabile. Ha oltre 1 milione di download ed è l'unico tema al mondo con oltre 5.700 recensioni a cinque stelle! È ideale per web designer professionisti, liberi professionisti, piccole imprese, e-commerce, siti di membership e qualsiasi tipo di sito web. Offre funzionalità e template speciali che lo rendono perfettamente compatibile con tutti i builder di pagine come Spectra, Elementor, Beaver Builder, ecc. Prestazioni elevate, codice pulito, design mobile-first e markup schema sono tutti integrati, rendendo il tema eccezionalmente SEO-friendly. È pienamente compatibile con WooCommerce, SureCart e altri plugin di e-commerce e include numerose funzionalità e template pensati per i negozi. Astra offre anche un supporto di esperti per gli utenti gratuiti. Un team dedicato di esperti WordPress altamente qualificati è a disposizione per assisterti in ogni aspetto del tema. Prova la demo live di Astra: https://zipwp.org/themes/astra/"

#. Theme Name of the theme
#: style.css admin/includes/class-astra-menu.php:162
#: inc/lib/class-astra-nps-notice.php:108
#, gp-priority: high
msgid "Astra"
msgstr "Astra"

#: inc/metabox/class-astra-elementor-editor-settings.php:476
msgid "Sticky Header Rows"
msgstr "Righe dell'header fisso"

#: inc/metabox/class-astra-elementor-editor-settings.php:516
msgid "Preview Changes"
msgstr "Anteprima delle modifiche"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:243
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:298
msgid "Note: A subtle shadow is added in this preview so white logos remain visible."
msgstr "Nota: in questa anteprima è stata aggiunta un'ombra sottile per far sì che i loghi bianchi restino visibili."

#: admin/includes/class-astra-menu.php:850
msgid "Say goodbye to slow checkouts – boost sales with a smooth, hassle-free experience."
msgstr "Di' addio ai pagamenti lenti: aumenta le vendite con un'esperienza fluida e senza intoppi."

#: admin/includes/class-astra-menu.php:850
msgid "Modern Cart: A smarter way to sell"
msgstr "Carrello moderno: un modo più intelligente di vendere"

#: inc/customizer/class-astra-customizer.php:702
msgid "You are not allowed to access this resource."
msgstr "Non hai il permesso di usare questa risorsa."

#: inc/customizer/class-astra-customizer.php:707
msgid "This request is only allowed in the Customizer screen."
msgstr "Questa richiesta è consentita solo nella schermata \"Personalizza\"."

#: admin/includes/class-astra-menu.php:945
msgid "Sell products, services, subscriptions & more."
msgstr "Vendi prodotti, servizi, abbonamenti e altro ancora."

#: inc/lib/bsf-analytics/class-bsf-analytics.php:238
msgid "Help us improve %1$s and our other products!<br><br>With your permission, we'd like to collect <strong>non-sensitive information</strong> from your website — like your PHP version and which features you use — so we can fix bugs faster, make smarter decisions, and build features that actually matter to you. <em>No personal info. Ever.</em>"
msgstr "Aiutaci a migliorare %1$s e gli altri nostri prodotti!<br><br>Con la tua autorizzazione, vorremmo raccogliere <strong>informazioni non sensibili</strong> dal tuo sito web, come la tua versione PHP e le funzionalità che utilizzi, in modo da poter correggere bug più velocemente, prendere decisioni più intelligenti e sviluppare funzionalità che siano davvero importanti per te. <em>Nessuna informazione personale. Mai.</em>"

#: inc/markup-extras.php:875
msgid "Search button"
msgstr "Pulsante di ricerca"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:200
msgid "WooCommerce Product Pages"
msgstr "Pagine prodotto di WooCommerce"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:644
msgid "This page uses the Transparent Header. Click below to customize it."
msgstr "Questa pagina utilizza l'header trasparente. Fai clic qui sotto per personalizzarla."

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:262
msgid "Singular?"
msgstr "Singolo?"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:247
msgid "Single Post?"
msgstr "Articolo singolo?"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:233
msgid "Single Page?"
msgstr "Pagina singola?"

#: inc/builder/type/header/menu/class-astra-header-menu-component.php:49
msgid "Primary Site Navigation"
msgstr "Navigazione del sito principale"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:197
msgid "Post Types"
msgstr "Tipi di contenuto"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:276
msgid "404 Page?"
msgstr "Pagina 404?"

#: inc/customizer/configurations/typography/class-astra-archive-typo-configs.php:62
msgid "Archive pagination"
msgstr "Paginazione dell'archivio"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:128
msgid "Archive Pages"
msgstr "Pagine di archivio"

#: inc/customizer/configurations/typography/class-astra-archive-typo-configs.php:74
msgid "Archive excerpt"
msgstr "Riassunto dell'archivio"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:219
msgid "Archive?"
msgstr "Archivio?"

#. translators: %d: menu index number
#: inc/builder/type/header/menu/class-astra-header-menu-component.php:58
msgid "Menu %d Site Navigation"
msgstr "Menu %d di navigazione del sito"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:158
msgid "Latest Posts Page"
msgstr "Pagina degli ultimi articoli"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:641
msgid "Logo is set in the Transparent Header Section. Click below to customize it."
msgstr "Il logo è impostato nella sezione \"header trasparente\". Fai click qui sotto per personalizzarlo."

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:175
msgid "Home Page?"
msgstr "Homepage?"

#: inc/builder/type/header/menu/class-astra-header-menu-component.php:53
msgid "Secondary Site Navigation"
msgstr "Navigazione del sito secondario"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:113
msgid "Search Page"
msgstr "Pagina di ricerca"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:173
msgid "Pages"
msgstr "Pagine"

#: admin/class-astra-bsf-analytics.php:104
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:285
msgid "This is a temporary deactivation for testing."
msgstr "Si tratta di una disattivazione temporanea a scopo di test."

#: admin/class-astra-bsf-analytics.php:110
msgid "The theme isn't working properly."
msgstr "Il tema non funziona correttamente."

#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:291
msgid "The plugin isn't working properly."
msgstr "Il plugin non funziona correttamente."

#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:156
msgid "Submit & Deactivate"
msgstr "Invia e disattiva"

#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:157
msgid "Skip & Deactivate"
msgstr "Salta e disattiva"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:187
#: inc/blog/single-blog.php:286
msgid "Posts"
msgstr "Articoli"

#. translators: %s Product title
#: inc/lib/bsf-analytics/class-bsf-analytics.php:459
msgid "Allow %s products to track non-sensitive usage tracking data."
msgstr "Consenti ai prodotti %s di tenere traccia dei dati non sensibili di monitoraggio dell'utilizzo."

#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:80
msgid "Quick Feedback"
msgstr "Feedback veloce"

#: admin/class-astra-bsf-analytics.php:129
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:138
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:310
msgid "Please tell us more details."
msgstr "Forniscici maggiori dettagli."

#: admin/class-astra-bsf-analytics.php:111
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:292
msgid "Please tell us more about what went wrong?"
msgstr "Puoi spiegarci meglio cosa è andato storto?"

#: admin/class-astra-bsf-analytics.php:123
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:304
msgid "Please tell us more about the feature."
msgstr "Raccontaci di più su questa caratteristica."

#: admin/class-astra-bsf-analytics.php:128
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:309
msgid "Other"
msgstr "Altro"

#: inc/lib/bsf-analytics/classes/class-bsf-analytics-helper.php:28
msgid "Oops! Something went wrong. Please refresh the page and try again."
msgstr "Oops! Si è verificato un errore. Aggiorna la pagina e riprova."

#. translators: %1$s: link html start, %2$s: link html end
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:146
msgid "Need help from our experts? %1$sClick here to contact us.%2$s"
msgstr "Hai bisogno dell'aiuto dei nostri esperti? %1$sFai clic qui per contattarci.%2$s"

#: admin/class-astra-bsf-analytics.php:122
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:303
msgid "It's missing a specific feature."
msgstr "Manca una caratteristica specifica."

#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:83
msgid "If you have a moment, please share why you are deactivating the plugin."
msgstr "Se hai un momento, spiegaci perché stai disattivando il plugin."

#: admin/class-astra-bsf-analytics.php:116
msgid "I found a better alternative theme."
msgstr "Ho trovato un tema alternativo migliore."

#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:297
msgid "I found a better alternative plugin."
msgstr "Ho trovato un plugin alternativo migliore."

#: admin/class-astra-bsf-analytics.php:105
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:286
msgid "How can we assist you?"
msgstr "Come possiamo aiutarti?"

#: inc/markup-extras.php:1999
msgid "Untitled Post"
msgstr "Articolo senza titolo"

#: inc/markup-extras.php:2009
msgid "Read: %s"
msgstr "Leggi: %s"

#: inc/lib/astra-notices/class-astra-notices.php:153
msgid "Invalid notice ID."
msgstr "ID di avviso non valido."

#: inc/customizer/configurations/builder/header/configs/search.php:171
msgid "Visible Search Result"
msgstr "Risultato della ricerca visibile"

#: admin/includes/class-astra-admin-ajax.php:310
msgid "Plugin slug is missing."
msgstr "Manca lo slug del plugin."

#: admin/includes/class-astra-admin-ajax.php:333
msgid "Plugin installation function not found."
msgstr "Funzione di installazione del plugin non trovata."

#: inc/blog/blog-config.php:181
msgctxt "Blogs: Author Prefix Label"
msgid "%astra%"
msgstr "%astra%"

#: inc/class-astra-global-palette.php:302
#: inc/class-astra-global-palette.php:303
msgid "Subtle Background"
msgstr "Sfondo sottile"

#: inc/class-astra-global-palette.php:300
#: inc/class-astra-global-palette.php:301
msgid "Primary Background"
msgstr "Sfondo principale"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:97
msgid "Note: This is not applicable on Transparent and Sticky Headers!"
msgstr "Nota: questo non è applicabile agli header trasparenti e in evidenza!"

#: inc/class-astra-global-palette.php:300
#: inc/class-astra-global-palette.php:301
msgid "Secondary Background"
msgstr "Sfondo secondario"

#: inc/class-astra-global-palette.php:304
msgid "Other Supporting"
msgstr "Altro supporto"

#: inc/core/builder/class-astra-builder-helper.php:819
msgid "Widget 6"
msgstr "Widget 6"

#: inc/core/builder/class-astra-builder-helper.php:814
msgid "Widget 5"
msgstr "Widget 5"

#: inc/core/builder/class-astra-builder-helper.php:759
#: inc/core/builder/class-astra-builder-helper.php:930
msgid "Widget 4"
msgstr "Widget 4"

#: inc/core/builder/class-astra-builder-helper.php:753
#: inc/core/builder/class-astra-builder-helper.php:925
msgid "Widget 3"
msgstr "Widget 3"

#: inc/core/builder/class-astra-builder-helper.php:747
#: inc/core/builder/class-astra-builder-helper.php:920
msgid "HTML 3"
msgstr "HTML 3"

#: inc/core/builder/class-astra-builder-helper.php:741
#: inc/core/builder/class-astra-builder-helper.php:829
#: inc/core/builder/class-astra-builder-helper.php:915
msgid "Button 2"
msgstr "Pulsante 2"

#: inc/core/builder/class-astra-builder-helper.php:723
#: inc/core/builder/class-astra-builder-helper.php:910
#: inc/customizer/configurations/builder/header/configs/header-builder.php:706
msgid "Language Switcher"
msgstr "Selettore di lingua"

#: inc/core/builder/class-astra-builder-helper.php:717
#: inc/core/builder/class-astra-builder-helper.php:809
#: inc/core/builder/class-astra-builder-helper.php:905
msgid "Divider 3"
msgstr "Separatore 3"

#: inc/core/builder/class-astra-builder-helper.php:711
#: inc/core/builder/class-astra-builder-helper.php:804
#: inc/core/builder/class-astra-builder-helper.php:900
msgid "Divider 2"
msgstr "Separatore 2"

#: inc/core/builder/class-astra-builder-helper.php:824
msgid "Button 1"
msgstr "Pulsante 1"

#: inc/core/builder/class-astra-builder-helper.php:895
msgid "Divider 1"
msgstr "Separatore 1"

#: inc/core/builder/class-astra-builder-options.php:668
msgid "Copyright [copyright] [current_year] [site_title] | Powered by [theme_author]"
msgstr "Copyright [copyright] [current_year] [site_title] | Powered by [theme_author]"

#: inc/lib/class-astra-nps-notice.php:118
msgid "Thank you for your feedback"
msgstr "Grazie per il tuo feedback"

#: inc/lib/class-astra-nps-notice.php:119
msgid "We value your input. How can we improve your experience?"
msgstr "Apprezziamo il tuo input. Come possiamo migliorare la tua esperienza?"

#: inc/lib/class-astra-nps-notice.php:112
msgid "Thanks a lot for your feedback! 😍"
msgstr "Grazie mille per il tuo feedback! 😍"

#: inc/lib/class-astra-nps-notice.php:115
msgid "Rate the Theme"
msgstr "Valuta il tema"

#: inc/lib/nps-survey/classes/nps-survey-script.php:276
#: inc/lib/nps-survey/classes/nps-survey-script.php:382
msgid "Nonce verification failed."
msgstr "Verifica nonce fallita."

#: inc/lib/class-astra-nps-notice.php:109
msgid "How likely are you to recommend #pluginname to your friends or colleagues?"
msgstr "Quanto è probabile che consiglieresti #pluginname ai tuoi amici o colleghi?"

#: inc/lib/nps-survey/classes/nps-survey-script.php:254
msgid "Sorry, you are not allowed to do that."
msgstr "Non hai i permessi per farlo."

#: inc/core/common-functions.php:1152
msgctxt "Search Page Custom `When Results Found` Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/common-functions.php:1153
msgctxt "Search Page Custom `When Results Not Found` Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/customizer/class-astra-customizer-partials.php:79
#: inc/markup-extras.php:903
msgctxt "Primary Menu: Custom Menu Text / HTML for Last Item in Menu"
msgid "%astra%"
msgstr "%astra%"

#: inc/customizer/class-astra-customizer-partials.php:92
msgctxt "Primary Menu: Button Text for Last Item in Menu"
msgid "%astra%"
msgstr "%astra%"

#: inc/markup-extras.php:1061
msgctxt "Footer small section 1 credit"
msgid "%astra%"
msgstr "%astra%"

#: inc/markup-extras.php:1062
msgctxt "Footer small section 2 credit"
msgid "%astra%"
msgstr "%astra%"

#: inc/modules/related-posts/class-astra-related-posts-markup.php:71
msgctxt "Single Blog/Post Related Posts: Title"
msgid "%astra%"
msgstr "%astra%"

#: inc/modules/related-posts/class-astra-related-posts-markup.php:217
msgctxt "Blogs: Read More Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/template-parts.php:166
msgctxt "Primary Menu: Menu Label for Toggle Button"
msgid "%astra%"
msgstr "%astra%"

#: inc/blog/blog.php:450
msgctxt "Search Page Title: Subheading - When Results Found"
msgid "%astra%"
msgstr "%astra%"

#: inc/blog/blog.php:451
msgctxt "Search Page Title: Subheading - When Results Not Found"
msgid "%astra%"
msgstr "%astra%"

#: inc/builder/controllers/class-astra-builder-ui-controller.php:219
msgctxt "Primary Menu: Menu Label (Mobile Menu)"
msgid "%astra%"
msgstr "%astra%"

#: inc/builder/controllers/class-astra-builder-ui-controller.php:381
msgctxt "Header Builder: Account Widget - Logged In View Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/builder/controllers/class-astra-builder-ui-controller.php:460
msgctxt "Header Builder: Account Widget - Logged Out View Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/builder/markup/class-astra-builder-footer.php:226
msgctxt "Footer Builder: Copyright Editor Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/edd/edd-common-functions.php:323
msgctxt "EDD Product Archive: Cart Button Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/edd/edd-common-functions.php:324
msgctxt "EDD Product Archive: Variable Product Button Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:407
msgctxt "WooCommerce Single Product: Shipping Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:584
msgctxt "Header Builder: Cart Widget - Cart Label"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:3547
msgctxt "WooCommerce Cart: Cart Button Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:3652
msgctxt "WooCommerce Single Product: Payments - Payment Title."
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1376
msgctxt "Builder: Header Button 1 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1377
msgctxt "Builder: Header Button 2 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1378
msgctxt "Builder: Header Button 3 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1379
msgctxt "Builder: Header Button 4 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1380
msgctxt "Builder: Header Button 5 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1381
msgctxt "Builder: Header Button 6 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1382
msgctxt "Builder: Header Button 7 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1383
msgctxt "Builder: Header Button 8 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1384
msgctxt "Builder: Header Button 9 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1385
msgctxt "Builder: Header Button 10 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1387
msgctxt "Builder: Footer Button 1 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1388
msgctxt "Builder: Footer Button 2 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1389
msgctxt "Builder: Footer Button 3 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1390
msgctxt "Builder: Footer Button 4 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1391
msgctxt "Builder: Footer Button 5 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1392
msgctxt "Builder: Footer Button 6 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1393
msgctxt "Builder: Footer Button 7 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1394
msgctxt "Builder: Footer Button 8 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1395
msgctxt "Builder: Footer Button 9 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1396
msgctxt "Builder: Footer Button 10 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1404
msgctxt "Builder: Header HTML 7"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1403
msgctxt "Builder: Header HTML 6"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1402
msgctxt "Builder: Header HTML 5"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1401
msgctxt "Builder: Header HTML 4"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1400
msgctxt "Builder: Header HTML 3"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1399
msgctxt "Builder: Header HTML 2"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1398
msgctxt "Builder: Header HTML 1"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/common-functions.php:985
msgctxt "Search Page Title: Heading - Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1422
msgctxt "Button text for last item in Primary Menu"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1418
msgctxt "Builder: Footer HTML 10"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1413
msgctxt "Builder: Footer HTML 5"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1412
msgctxt "Builder: Footer HTML 4"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1411
msgctxt "Builder: Footer HTML 3"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1410
msgctxt "Builder: Footer HTML 2"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1409
msgctxt "Builder: Footer HTML 1"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1407
msgctxt "Builder: Header HTML 10"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1406
msgctxt "Builder: Header HTML 9"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1405
msgctxt "Builder: Header HTML 8"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:696
msgid "<span class=\"count\">%1$s</span>"
msgid_plural "<span class=\"count\">%1$s</span>"
msgstr[0] "<span class=\"count\">%1$s</span>"
msgstr[1] "<span class=\"count\">%1$s</span>"

#: inc/core/builder/class-astra-builder-helper.php:1414
msgctxt "Builder: Footer HTML 6"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1415
msgctxt "Builder: Footer HTML 7"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1416
msgctxt "Builder: Footer HTML 8"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1417
msgctxt "Builder: Footer HTML 9"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:353
msgid "Review Count"
msgstr "Conteggio delle revisioni"

#: admin/includes/class-astra-menu.php:356
msgid "Configuring"
msgstr "Configurazione in corso"

#: admin/includes/class-astra-menu.php:819
msgid "Connect your WordPress plugins, WooCommerce sites, apps, and websites for powerful automations."
msgstr "Connetti i tuoi plugin WordPress, siti WooCommerce, app e siti web per potenti automazioni."

#: inc/customizer/class-astra-customizer.php:1765
msgid "Unordered List"
msgstr "Elenco non ordinato"

#: inc/customizer/class-astra-customizer.php:1488
msgid "Style Guide"
msgstr "Guida di stile"

#: inc/customizer/class-astra-customizer.php:1625
#: inc/customizer/class-astra-customizer.php:1678
msgid "Secondary"
msgstr "Secondario"

#: inc/customizer/class-astra-customizer.php:1759
msgid "The future will belongs to those who believe in the beauty of their dreams."
msgstr "Il futuro appartiene a coloro che credono nella bellezza dei propri sogni."

#: inc/customizer/class-astra-customizer.php:1757
msgid "Quote"
msgstr "Virgolette"

#: inc/customizer/class-astra-customizer.php:1712
msgid "Here's how the body text will look like on your website. You can customize the typography to match your brand personality. Whether you aim for a modern and sleek appearance or a more traditional and elegant feel, the right typography sets the tone for your content."
msgstr "Ecco come apparirà il corpo del testo sul tuo sito web. Puoi personalizzare la tipografia per adattarla alla personalità del tuo brand. Sia che tu miri a un aspetto moderno ed elegante o a un aspetto più tradizionale ed elegante, la tipografia giusta dà il tono ai tuoi contenuti."

#: inc/customizer/class-astra-customizer.php:1637
msgid "Extra"
msgstr "Extra"

#: inc/class-astra-global-palette.php:296
#: inc/customizer/class-astra-customizer.php:1605
msgid "Brand"
msgstr "Brand"

#: inc/customizer/class-astra-customizer.php:1769
msgid "List Item 3"
msgstr "Elemento 3 dell'elenco"

#: inc/customizer/class-astra-customizer.php:1768
msgid "List Item 2"
msgstr "Elemento 2 dell'elenco"

#: inc/customizer/class-astra-customizer.php:1767
msgid "List Item 1"
msgstr "Elemento 1 dell'elenco"

#: inc/compatibility/surecart/class-astra-surecart.php:399
#: inc/compatibility/surecart/customizer/class-astra-customizer-register-surecart-section.php:59
msgid "Upsells"
msgstr "Upsell"

#: inc/compatibility/surecart/class-astra-surecart.php:406
msgid "Title Area"
msgstr "Area del titolo"

#: inc/compatibility/surecart/class-astra-surecart.php:390
msgid "Product"
msgstr "Prodotto"

#: admin/includes/class-astra-menu.php:835
msgid "Power-up block editor with advanced blocks for faster and effortlessly website creation."
msgstr "Potenzia l'editor a blocchi con blocchi avanzati per la creazione di siti web più rapida e semplice."

#: admin/includes/class-astra-menu.php:866
msgid "PayPal Payments For WooCommerce simplifies and secures PayPal transactions on your store."
msgstr "PayPal Payments For WooCommerce semplifica e protegge le transazioni PayPal sul tuo negozio."

#: admin/includes/class-astra-menu.php:882
msgid "Capture emails at checkout and send follow-up emails to recover lost revenue."
msgstr "Cattura le email al momento del pagamento e invia email di follow-up per recuperare le entrate perse."

#: inc/compatibility/surecart/class-astra-surecart.php:395
#: inc/compatibility/surecart/customizer/class-astra-customizer-register-surecart-section.php:52
msgid "Collections"
msgstr "Raccolte"

#: admin/includes/class-astra-menu.php:898
msgid "Convert WooCommerce variation dropdown attributes into attractive swatches instantly."
msgstr "Converti istantaneamente gli attributi del menu a discesa delle varianti di WooCommerce in campioni accattivanti."

#: admin/includes/class-astra-menu.php:802
msgid "Build high-converting E-Commerce stores with CartFlows, the ultimate checkout and funnel builder."
msgstr "Costruisci negozi di e-commerce ad alta conversione con CartFlows, il miglior builder di checkout e funnel."

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:224
msgid "This responsive cart click option will work for tablet and mobile in same way"
msgstr "Questa opzione di clic del carrello responsive funzionerà allo stesso modo per tablet e dispositivi mobile"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:215
msgid "Responsive Cart Click Action"
msgstr "Carrello responsive fai clic su azione"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5103
#: assets/svg/logo-svg-icons/icons-v6-3.php:5129
msgid "Vimeo"
msgstr "Vimeo"

#: inc/customizer/configurations/builder/header/configs/above-header.php:76
#: inc/customizer/configurations/builder/header/configs/below-header.php:76
#: inc/customizer/configurations/builder/header/configs/primary-header.php:93
msgid "It would not be effective if transparent header is enabled."
msgstr "Potrebbe non funzionare se è stato abilitato l'header trasparente."

#: inc/customizer/class-astra-customizer.php:1647
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Close"
msgstr "Chiudi"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:278
msgid "Logo SVG Gap"
msgstr "Distanza del logo SVG"

#: inc/customizer/configurations/builder/header/configs/header-builder.php:428
msgid "Sign up"
msgstr "Iscriviti"

#: inc/customizer/configurations/builder/header/configs/header-builder.php:427
msgid "Log in"
msgstr "Accedi"

#: inc/customizer/configurations/builder/header/configs/header-builder.php:286
msgid "Header Presets"
msgstr "Preset dell'header"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6426
msgid "Travel"
msgstr "Viaggi"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6418
msgid "Science"
msgstr "Scienza"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6410
msgid "Environment"
msgstr "Ambiente"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6406
msgid "Education"
msgstr "Istruzione"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6398
msgid "Communication"
msgstr "Comunicazione"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6390
msgid "Brands"
msgstr "Marchi"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6380
msgid "Zhihu"
msgstr "Zhihu"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6367
msgid "Z"
msgstr "Z"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6323
msgid "Yoast"
msgstr "Yoast"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6312
msgid "Yin Yang"
msgstr "Yin Yang"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6239
msgid "Yandex"
msgstr "Yandex"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6224
msgid "Yammer"
msgstr "Yammer"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6209
msgid "Yahoo Logo"
msgstr "Logo Yahoo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6111
msgid "Xbox"
msgstr "Xbox"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6085
msgid "X"
msgstr "X"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6056
msgid "wpressr"
msgstr "wpressr"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6026
msgid "WPExplorer"
msgstr "WPExplorer"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5968
msgid "WordPress Logo"
msgstr "Logo WordPress"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5894
msgid "Wix"
msgstr "Wix"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5983
msgid "Wordpress Simple"
msgstr "Wordpress Simple"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5940
msgid "Wolf Pack Battalion"
msgstr "Wolf Pack Battalion"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5925
msgid "Wodu"
msgstr "Wodu"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5909
msgid "Wizards of the Coast"
msgstr "Wizards of the Coast"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5879
msgid "wirsindhandwerk"
msgstr "wirsindhandwerk"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5323
msgid "Volume Off"
msgstr "Volume disattivato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5308
msgid "Volume Low"
msgstr "Volume basso"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5293
msgid "Volume High"
msgstr "Volume alto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5280
msgid "Volleyball"
msgstr "Pallavolo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5211
msgid "Viruses"
msgstr "Virus"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5159
msgid "Virus"
msgstr "Virus"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5144
msgid "Vine"
msgstr "Vine"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5090
msgid "Vihara"
msgstr "Vihara"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5048
msgid "Viber"
msgstr "Viber"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4351
msgid "Up Right And Down Left From Center"
msgstr "In alto a destra e in basso a sinistra dal centro"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4329
msgid "Up Down Left Right"
msgstr "Su giù sinistra destra"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4318
msgid "Up Down"
msgstr "Su giù"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4303
msgid "Untappd"
msgstr "Untappd"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4286
msgid "Unsplash"
msgstr "Unsplash"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4247
msgid "Universal Access"
msgstr "Accesso universale"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4232
msgid "Unity 3D"
msgstr "Unity 3D"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4789
msgid "United States Postal Service"
msgstr "United States Postal Service"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4217
msgid "Uniregistry"
msgstr "Uniregistry"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5823
msgid "Windows"
msgstr "Windows"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5810
msgid "Window Restore"
msgstr "Ripristina finestra"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5792
msgid "Window Minimize"
msgstr "Minimizza finestra"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5774
msgid "Window Maximize"
msgstr "Ingrandisci finestra"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5740
msgid "Wikipedia W"
msgstr "Wikipedia W"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5726
msgid "Wifi"
msgstr "Wifi"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5711
msgid "WHMCS"
msgstr "WHMCS"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5559
msgid "Weibo"
msgstr "Weibo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5544
msgid "Weebly"
msgstr "Weebly"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5529
msgid "Waze"
msgstr "Waze"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4389
msgid "UPS"
msgstr "UPS"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4376
msgid "Upload"
msgstr "Carica"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4365
msgid "Up Right From Square"
msgstr "In alto a destra dal quadrato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4191
msgid "Uncharted Software"
msgstr "Uncharted Software"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4178
msgid "Umbrella Beach"
msgstr "Ombrellone"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4164
msgid "Umbrella"
msgstr "Ombrello"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4149
msgid "Umbraco"
msgstr "Umbraco"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4134
msgid "UIkit"
msgstr "UIkit"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3649
msgid "Tree"
msgstr "Albero"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3592
msgid "Trash"
msgstr "Cestino"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3664
msgid "Tree City"
msgstr "Città con alberi"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3581
msgid "Transgender"
msgstr "Transgender"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6299
msgid "Yen Sign"
msgstr "Segno dello yen"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6071
msgid "Wrench"
msgstr "Chiave inglese"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6041
msgid "WPForms"
msgstr "WPForms"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5955
msgid "Won Sign"
msgstr "Simbolo del won"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6414
msgid "Lifestyle"
msgstr "Stile di vita"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6394
msgid "Business"
msgstr "Affari"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6098
msgid "X Ray"
msgstr "Raggi X"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:421
msgid "Logo SVG Icon Color"
msgstr "Colore dell'icona del logo SVG"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:242
msgid "Logo SVG Icon"
msgstr "Icona del logo SVG"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5851
msgid "Wine Glass"
msgstr "Bicchiere di vino"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5838
msgid "Wine Bottle"
msgstr "Bottiglia di vino"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5698
msgid "Whiskey Glass"
msgstr "Bicchiere di whiskey"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:240
msgid "When using Custom SVG code, do not include few attributes such as \"width\", \"height\", and \"fill\" in your custom svg code to utilize existing customizer controls."
msgstr "Quando utilizzi il codice SVG personalizzato, non includere alcuni attributi come \"width\", \"height\" e \"fill\" nel codice SVG personalizzato per utilizzare i controlli di personalizzazione esistenti."

#: assets/svg/logo-svg-icons/icons-v6-3.php:5865
msgid "Wine Glass Empty"
msgstr "Bicchiere di vino vuoto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5755
msgid "Wind"
msgstr "Vento"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5668
msgid "Wheelchair"
msgstr "Sedia a rotelle"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5641
msgid "Wheat Awn"
msgstr "Spiga di grano"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5615
msgid "What's App"
msgstr "Whatsapp"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5473
msgid "Watchman Monitoring"
msgstr "Watchman Monitoring"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5460
msgid "Warehouse"
msgstr "Magazzino"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5434
msgid "Wand Magic Sparkles"
msgstr "Bacchetta magica scintillante"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5447
msgid "Wand Sparkles"
msgstr "Bacchetta scintillante"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5421
msgid "Wand Magic"
msgstr "Bacchetta magica"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5408
msgid "Wallet"
msgstr "Portafoglio"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5394
msgid "Walkie Talkie"
msgstr "Walkie-talkie"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5381
msgid "W"
msgstr "W"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5338
msgid "Volume Xmark"
msgstr "Volume silenziato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5267
msgid "Volcano"
msgstr "Vulcano"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5254
msgid "Voicemail"
msgstr "Segreteria telefonica"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5239
msgid "VNV"
msgstr "VNV"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5172
msgid "Virus Covid"
msgstr "Virus Covid"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5035
msgid "Vials"
msgstr "Provette"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5022
msgid "Vial Virus"
msgstr "Provetta del virus"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4996
msgid "Vial"
msgstr "Provetta"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4970
msgid "Viadeo"
msgstr "Viadeo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4955
msgid "Viacoin"
msgstr "Viacoin"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4825
msgid "V"
msgstr "V"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4811
msgid "Utensils"
msgstr "Utensili"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4699
msgid "Users"
msgstr "Utenti"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4675
msgid "User Tie"
msgstr "Utente in cravatta"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4522
msgid "User Injured"
msgstr "Utente ferito"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4509
msgid "User Group"
msgstr "Gruppo di utenti"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4496
msgid "User Graduate"
msgstr "Utente laureato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4472
msgid "User Doctor"
msgstr "Utente dottore"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4437
msgid "User Astronaut"
msgstr "Utente astronauta"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4424
msgid "User"
msgstr "Utente"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4404
msgid "USB"
msgstr "USB"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4800
msgid "us-Sunnah Foundation"
msgstr "us-Sunnah Foundation"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3968
msgid "Turkish Lira Sign"
msgstr "Simbolo della lira turca"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3886
msgid "Truck Pickup"
msgstr "Camion pickup"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3844
msgid "Truck Medical"
msgstr "Ambulanza"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3830
msgid "Truck Front"
msgstr "Parte anteriore del camion"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3788
msgid "Truck Fast"
msgstr "Camion veloce"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3775
msgid "Truck Droplet"
msgstr "Camion con gocciolina"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3761
msgid "Truck Arrow Right"
msgstr "Camion con freccia rivolta verso destra"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3605
msgid "Trash Arrow Up"
msgstr "Camion con freccia rivolta verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3568
msgid "Train Tram"
msgstr "Tram"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4664
msgid "User Tag"
msgstr "Utente con tag"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4640
msgid "User Shield"
msgstr "Utente con scudo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4627
msgid "User Secret"
msgstr "Utente in incognito"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3859
msgid "Truck Monster"
msgstr "Monster truck"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5503
msgid "Water Ladder"
msgstr "Scaletta in acqua"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5488
msgid "Water"
msgstr "Acqua"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4941
msgid "Vest Patches"
msgstr "Toppe per gilet"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4927
msgid "Vest"
msgstr "Gilet"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4894
msgid "Venus"
msgstr "Femminile"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4881
msgid "Vector Square"
msgstr "Quadrato vettoriale"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4853
msgid "Van Shuttle"
msgstr "Navetta"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4838
msgid "Vaadin"
msgstr "Vaadin"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4119
msgid "Ubuntu"
msgstr "Ubuntu"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4104
msgid "Uber"
msgstr "Uber"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4091
msgid "U"
msgstr "U"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4076
msgid "Typo3"
msgstr "Typo3"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4019
msgid "Twitch"
msgstr "Twitch"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4003
msgid "Tv"
msgstr "Tv"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3873
msgid "Truck Moving"
msgstr "Camion da trasloco"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3746
msgid "Truck"
msgstr "Camion"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3733
msgid "Trowel Bricks"
msgstr "Mattoni e cazzuola"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3720
msgid "Trowel"
msgstr "Cazzuola"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3706
msgid "Trophy"
msgstr "Trofeo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3695
msgid "Triangle Exclamation"
msgstr "Triangolo con punto esclamativo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3678
msgid "Trello"
msgstr "Trello"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3636
msgid "Trash Can Arrow Up"
msgstr "Cestino della spazzatura con freccia verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3623
msgid "Trash Can"
msgstr "Cestino della spazzatura"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6011
msgid "WPBeginner"
msgstr "WPBeginner"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5366
msgid "Vue.js"
msgstr "Vue.js"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5998
msgid "Worm"
msgstr "Worm"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6153
msgid "Xmark"
msgstr "Xmark"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6269
msgid "Yarn"
msgstr "Yarn"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6254
msgid "Yandex International"
msgstr "Yandex International"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6194
msgid "Y Combinator"
msgstr "Y Combinator"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5600
msgid "Weixin (WeChat)"
msgstr "Weixin (WeChat)"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5587
msgid "Weight Scale"
msgstr "Bilancia"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5574
msgid "Weight Hanging"
msgstr "Peso sospeso"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5198
msgid "Virus Slash"
msgstr "Virus barrato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4756
msgid "Users Rectangle"
msgstr "Rettangolo con gli utenti"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4712
msgid "Users Between Lines"
msgstr "Utenti tra le linee"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4688
msgid "User Xmark"
msgstr "Utente con una x"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4653
msgid "User Slash"
msgstr "Utente barrato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4614
msgid "User Plus"
msgstr "Utente con più"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4603
msgid "User Pen"
msgstr "Utente con una penna"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4568
msgid "User Minus"
msgstr "Utente con meno"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4555
msgid "User Lock"
msgstr "Utente con un lucchetto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4723
msgid "Users Gear"
msgstr "Utenti con ingranaggio"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4485
msgid "User Gear"
msgstr "Utente con ingranaggio"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4461
msgid "User Clock"
msgstr "Utente con orologio"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4450
msgid "User Check"
msgstr "Utente con spunta"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4273
msgid "Unlock Keyhole"
msgstr "Serratura sbloccata"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3540
msgid "Train"
msgstr "Treno"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3525
msgid "Trailer"
msgstr "Trailer"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3512
msgid "Traffic Light"
msgstr "Semaforo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3481
msgid "Trade Federation"
msgstr "Trade Federation"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3468
msgid "Tractor"
msgstr "Trattore"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3319
msgid "Toilet Paper"
msgstr "Carta igienica"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3305
msgid "Toilet"
msgstr "Gabinetto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3294
msgid "Toggle On"
msgstr "Attiva"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3283
msgid "Toggle Off"
msgstr "Disattiva"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3241
msgid "Ticket Simple"
msgstr "Biglietto semplice"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3227
msgid "Ticket"
msgstr "Biglietto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3211
msgid "Thumbtack"
msgstr "Thumbtack"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3197
msgid "Thumbs Up"
msgstr "Pollice su"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3178
msgid "Thumbs Down"
msgstr "Pollice giù"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2970
msgid "Tent"
msgstr "Tenda"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2940
msgid "Tencent Weibo"
msgstr "Tencent Weibo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2900
msgid "Temperature Low"
msgstr "Temperatura bassa"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2886
msgid "Temperature High"
msgstr "Temperatura alta"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2873
msgid "Temperature Half"
msgstr "Temperatura media"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2860
msgid "Temperature Full"
msgstr "Temperatura massima"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2847
msgid "Temperature Empty"
msgstr "Temperatura vuota"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2833
msgid "Temperature Arrow Up"
msgstr "Freccia della temperatura verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2819
msgid "Temperature Arrow Down"
msgstr "Freccia della temperatura verso il basso"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2791
msgid "Teeth Open"
msgstr "Denti aperti"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2778
msgid "Teeth"
msgstr "Denti"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2763
msgid "TeamSpeak"
msgstr "TeamSpeak"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2749
msgid "Taxi"
msgstr "Taxi"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2708
msgid "Tape"
msgstr "Nastro"

#: assets/svg/logo-svg-icons/icons-v6-3.php:189
msgid "Scissors"
msgstr "Forbici"

#: assets/svg/logo-svg-icons/icons-v6-3.php:105
msgid "School"
msgstr "Scuola"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2652
msgid "Tablets"
msgstr "Tablet"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2639
msgid "Tablet Screen Button"
msgstr "Pulsante schermo tablet"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2613
msgid "Tablet"
msgstr "Tablet"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2589
msgid "Table List"
msgstr "Elenco tabelle"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2576
msgid "Table Columns"
msgstr "Colonne della tabella"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2565
msgid "Table Cells Large"
msgstr "Celle di tabella grandi"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2554
msgid "Table Cells"
msgstr "Celle di tabella"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2541
msgid "Table"
msgstr "Tabella"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2528
msgid "T"
msgstr "T"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2515
msgid "Syringe"
msgstr "Siringa"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2502
msgid "Synagogue"
msgstr "Sinagoga"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2487
msgid "Symfony"
msgstr "Symfony"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2472
msgid "Swift"
msgstr "Swift"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2218
msgid "Stripe"
msgstr "Stripe"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2207
msgid "Strikethrough"
msgstr "Barrato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2194
msgid "Street View"
msgstr "Street View"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2179
msgid "Strava"
msgstr "Strava"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2152
msgid "Store"
msgstr "Negozio"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2128
msgid "Stopwatch"
msgstr "Cronometro"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2114
msgid "Stop"
msgstr "Stop"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2238
msgid "Stripe S"
msgstr "Stripe S"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2258
msgid "Stroopwafel"
msgstr "Stroopwafel"

#: assets/svg/logo-svg-icons/icons-v6-3.php:862
msgid "Sitemap"
msgstr "Sitemap"

#: assets/svg/logo-svg-icons/icons-v6-3.php:847
msgid "SISTRIX"
msgstr "SISTRIX"

#: assets/svg/logo-svg-icons/icons-v6-3.php:963
msgid "Skype"
msgstr "Skype"

#: assets/svg/logo-svg-icons/icons-v6-3.php:978
msgid "Slack Logo"
msgstr "Logo Slack"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5272
msgid "Python"
msgstr "Python"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5202
msgid "Product Hunt"
msgstr "Product Hunt"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5187
msgid "Print"
msgstr "Stampa"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5161
msgid "Prescription Bottle"
msgstr "Boccettina prescrizione"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5174
msgid "Prescription Bottle Medical"
msgstr "Boccettina di prescrizione medica"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5148
msgid "Prescription"
msgstr "Prescrizione"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5135
msgid "Power Off"
msgstr "Spegnimento"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5243
msgid "Pushed"
msgstr "Pushed"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4943
msgid "PlayStation"
msgstr "PlayStation"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4928
msgid "Play"
msgstr "Riproduci"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4849
msgid "Plane Departure"
msgstr "Decollo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4794
msgid "Plane Arrival"
msgstr "Atterraggio"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4781
msgid "Plane"
msgstr "Aereo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4768
msgid "Place Of Worship"
msgstr "Luogo di culto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4579
msgid "PHP"
msgstr "PHP"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4564
msgid "Photo Film"
msgstr "Pellicola fotografica"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4547
msgid "Phone Volume"
msgstr "Volume telefono"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3690
msgid "Pencil"
msgstr "Matita"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3642
msgid "Pen Nib"
msgstr "Pennino"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3598
msgid "Pen"
msgstr "Penna"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3587
msgid "Peace"
msgstr "Pace"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3422
msgid "Panorama"
msgstr "Panorama"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3394
msgid "Palfed"
msgstr "Palfed"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3368
msgid "Paintbrush"
msgstr "Pennello"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3354
msgid "Paint Roller"
msgstr "Rullo per pittura"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3326
msgid "Pagelines"
msgstr "Pagelines"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3311
msgid "page4 Corporation"
msgstr "page4 Corporation"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3258
msgid "Otter"
msgstr "Lontra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3228
msgid "ORCID"
msgstr "ORCID"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3213
msgid "Optin Monster"
msgstr "Optin Monster"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3198
msgid "Opera"
msgstr "Opera"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3183
msgid "OpenID"
msgstr "OpenID"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3168
msgid "OpenCart"
msgstr "OpenCart"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2968
msgid "Notes Medical"
msgstr "Note mediche"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2954
msgid "Note Sticky"
msgstr "Nota in evidenza"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2936
msgid "Not Equal"
msgstr "Non uguale"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2921
msgid "Node.js JS"
msgstr "Node.js JS"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2906
msgid "Node.js"
msgstr "Node.js"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2891
msgid "Nimblr"
msgstr "Nimblr"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2875
msgid "Nfc Symbol"
msgstr "Simbolo NFC"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2845
msgid "Newspaper"
msgstr "Giornale"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2829
msgid "Neuter"
msgstr "Neutro"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2800
msgid "Neos"
msgstr "Neos"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2668
msgid "Mountain"
msgstr "Montagna"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2638
msgid "Motorcycle"
msgstr "Motocicletta"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2598
msgid "Mosque"
msgstr "Moschea"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2571
msgid "Moon"
msgstr "Luna"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2552
msgid "Monument"
msgstr "Monumento"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2524
msgid "Money Check"
msgstr "Assegno"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2511
msgid "Money Bills"
msgstr "Banconote"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2355
msgid "Mobile screen"
msgstr "Schermo mobile"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2341
msgid "Mobile Retro"
msgstr "Cellulare retrò"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2327
msgid "Mobile button"
msgstr "Pulsante cellulare"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2283
msgid "Mixer"
msgstr "Mixer"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2268
msgid "Mixcloud"
msgstr "Mixcloud"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2459
msgid "Swatchbook"
msgstr "Campionario"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2444
msgid "Suse"
msgstr "Suse"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2416
msgid "Superscript"
msgstr "Apice"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2401
msgid "Superpowers"
msgstr "Superpoteri"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2374
msgid "Sun"
msgstr "Sole"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2329
msgid "Suitcase"
msgstr "Valigia"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2316
msgid "Subscript"
msgstr "Pedice"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2286
msgid "StumbleUpon Logo"
msgstr "Logo StumbleUpon"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1991
msgid "Star Of David"
msgstr "Stella di David"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1964
msgid "Star Half"
msgstr "Mezza stella"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1933
msgid "Star"
msgstr "Stella"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1914
msgid "Stamp"
msgstr "Francobollo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1900
msgid "Stairs"
msgstr "Scale"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1874
msgid "Stackpath"
msgstr "Stackpath"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1859
msgid "Stack Overflow"
msgstr "Stack Overflow"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1844
msgid "Stack Exchange"
msgstr "Stack Exchange"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1829
msgid "Squarespace"
msgstr "Squarespace"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1370
msgid "Spotify"
msgstr "Spotify"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1356
msgid "Spoon"
msgstr "Cucchiaio"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1343
msgid "Splotch"
msgstr "Splotch"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1332
msgid "Spinner"
msgstr "Spinner"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1319
msgid "Spider"
msgstr "Ragno"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1308
msgid "Spell Check"
msgstr "Controllo ortografia"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1293
msgid "Speaker Deck"
msgstr "Speaker Deck"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1278
msgid "Speakap"
msgstr "Speakap"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1253
msgid "Spa"
msgstr "Spa"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1238
msgid "Sourcetree"
msgstr "Sourcetree"

#: assets/svg/logo-svg-icons/icons-v6-3.php:762
msgid "Signal"
msgstr "Segnale"

#: assets/svg/logo-svg-icons/icons-v6-3.php:692
msgid "Shower"
msgstr "Doccia"

#: assets/svg/logo-svg-icons/icons-v6-3.php:677
msgid "Shopware"
msgstr "Shopware"

#: assets/svg/logo-svg-icons/icons-v6-3.php:662
msgid "Shopify"
msgstr "Shopify"

#: assets/svg/logo-svg-icons/icons-v6-3.php:607
msgid "Shoe Prints"
msgstr "Impronte scarpe"

#: assets/svg/logo-svg-icons/icons-v6-3.php:579
msgid "Shirt"
msgstr "Camicia"

#: assets/svg/logo-svg-icons/icons-v6-3.php:566
msgid "Ship"
msgstr "Nave"

#: assets/svg/logo-svg-icons/icons-v6-3.php:410
msgid "Share"
msgstr "Condivisione"

#: assets/svg/logo-svg-icons/icons-v6-3.php:396
msgid "Shapes"
msgstr "Forme"

#: assets/svg/logo-svg-icons/icons-v6-3.php:381
msgid "Servicestack"
msgstr "Servicestack"

#: assets/svg/logo-svg-icons/icons-v6-3.php:368
msgid "Server"
msgstr "Server"

#: assets/svg/logo-svg-icons/icons-v6-3.php:353
msgid "Sellsy"
msgstr "Sellsy"

#: assets/svg/logo-svg-icons/icons-v6-3.php:338
msgid "Sellcast"
msgstr "Sellcast"

#: assets/svg/logo-svg-icons/icons-v6-3.php:322
msgid "Seedling"
msgstr "Piantina"

#: assets/svg/logo-svg-icons/icons-v6-3.php:311
msgid "Section"
msgstr "Sezione"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3496
msgid "Trademark"
msgstr "Marchio di fabbrica"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3270
msgid "Timeline"
msgstr "Timeline"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2665
msgid "Tachograph Digital"
msgstr "Tachigrafo digitale"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3089
msgid "Text Width"
msgstr "Larghezza del testo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3078
msgid "Text Slash"
msgstr "Cancella formattazione"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3067
msgid "Text Height"
msgstr "Altezza del testo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3054
msgid "Terminal"
msgstr "Terminale"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2626
msgid "Tablet Button"
msgstr "Pulsante del tablet"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4503
msgid "Phone"
msgstr "Telefono"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3409
msgid "Pallet"
msgstr "Pallet"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2429
msgid "Supple"
msgstr "Flessibile"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1948
msgid "Star And Crescent"
msgstr "Mezzaluna e stella"

#: assets/svg/logo-svg-icons/icons-v6-3.php:735
msgid "Shuttle Space"
msgstr "Navetta spaziale"

#: assets/svg/logo-svg-icons/icons-v6-3.php:721
msgid "Shuffle"
msgstr "Ordine casuale"

#: assets/svg/logo-svg-icons/icons-v6-3.php:296
msgid "Searchengin"
msgstr "Searchengin"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3454
msgid "Tower Observation"
msgstr "Torre di osservazione"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3424
msgid "Tower Broadcast"
msgstr "Trasmissione dalla torre"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3411
msgid "Tornado"
msgstr "Tornado"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3398
msgid "Torii Gate"
msgstr "Portale Torii"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3385
msgid "Tooth"
msgstr "Dente"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3372
msgid "Toolbox"
msgstr "Cassetta degli attrezzi"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3158
msgid "Think Peaks"
msgstr "Think Peaks"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3145
msgid "Thermometer"
msgstr "Termometro"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2914
msgid "Temperature Quarter"
msgstr "Temperatura a tre quarti"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2721
msgid "Tarp"
msgstr "Telone"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2600
msgid "Table Tennis Paddle Ball"
msgstr "Racchetta con pallina da ping pong"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2356
msgid "Suitcase Rolling"
msgstr "Valigia con le rotelle"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2342
msgid "Suitcase Medical"
msgstr "Valigetta medica"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2271
msgid "Studio Vinari"
msgstr "Studio Vinari"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2139
msgid "Stopwatch 20"
msgstr "Cronometro con il numero 20"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2086
msgid "Stethoscope"
msgstr "Stetoscopio"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1708
msgid "Square Plus"
msgstr "Quadrato con il simbolo più"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1688
msgid "Square Phone Flip"
msgstr "Quadrato con telefono capovolto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1673
msgid "Square Phone"
msgstr "Quadrato con un telefono"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1645
msgid "Square Pen"
msgstr "Quadrato con una penna"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1632
msgid "Square Parking"
msgstr "Quadrato con il simbolo del parecheggio"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1619
msgid "Square Nfi"
msgstr "Quadrato con la scritta Nfi"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1606
msgid "Square Minus"
msgstr "Quadrato con il segno meno"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1586
msgid "Square H"
msgstr "Quadrato con la lettera H"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1573
msgid "Square Full"
msgstr "Quadrato pieno"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1524
msgid "Square Envelope"
msgstr "Quadrato con una busta"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1222
msgid "SoundCloud"
msgstr "SoundCloud"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1189
msgid "Sort"
msgstr "Ordinamento"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1176
msgid "Solar Panel"
msgstr "Pannello solare"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1162
msgid "Socks"
msgstr "Calzini"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1149
msgid "Soap"
msgstr "Sapone"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1136
msgid "Snowplow"
msgstr "Spazzaneve"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1122
msgid "Snowman"
msgstr "Pupazzo di neve"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1107
msgid "Snowflake"
msgstr "Fiocco di neve"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1076
msgid "Snapchat"
msgstr "Snapchat"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1049
msgid "Smog"
msgstr "Smog"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1034
msgid "Slideshare"
msgstr "Slideshare"

#: assets/svg/logo-svg-icons/icons-v6-3.php:819
msgid "SimplyBuilt"
msgstr "SimplyBuilt"

#: assets/svg/logo-svg-icons/icons-v6-3.php:806
msgid "Sim Card"
msgstr "Scheda SIM"

#: assets/svg/logo-svg-icons/icons-v6-3.php:776
msgid "Signature"
msgstr "Firma"

#: assets/svg/logo-svg-icons/icons-v6-3.php:749
msgid "Sign Hanging"
msgstr "Cartello appeso"

#: assets/svg/logo-svg-icons/icons-v6-3.php:706
msgid "Shrimp"
msgstr "Gamberetto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:635
msgid "Shop Lock"
msgstr "Negozio con lucchetto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:526
msgid "Shield Halved"
msgstr "Scudo diviso a metà"

#: assets/svg/logo-svg-icons/icons-v6-3.php:512
msgid "Shield Dog"
msgstr "Scudo con un cane"

#: assets/svg/logo-svg-icons/icons-v6-3.php:498
msgid "Shield Cat"
msgstr "Scudo con un gatto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:553
msgid "Shield Virus"
msgstr "Scudo con un virus"

#: assets/svg/logo-svg-icons/icons-v6-3.php:540
msgid "Shield Heart"
msgstr "Scudo con un cuore"

#: assets/svg/logo-svg-icons/icons-v6-3.php:283
msgid "Sd Card"
msgstr "Scheda SD"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3439
msgid "Tower Cell"
msgstr "Cella a torre"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3359
msgid "Toilets Portable"
msgstr "Toilette portatili"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3346
msgid "Toilet Portable"
msgstr "Toilet portatile"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3333
msgid "Toilet Paper Slash"
msgstr "Carta igienica sbarrata"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3130
msgid "ThemeIsle"
msgstr "ThemeIsle"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3115
msgid "Themeco"
msgstr "Themeco"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3100
msgid "The Red Yeti"
msgstr "The Red Yeti"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3040
msgid "Tents"
msgstr "Tende"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3026
msgid "Tent Arrows Down"
msgstr "Tenda con le frecce in giù"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3012
msgid "Tent Arrow Turn Left"
msgstr "Tenda con la freccia gira a sinistra"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2998
msgid "Tent Arrow Left Right"
msgstr "Tenda con la freccia gira a destra"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2927
msgid "Temperature Three Quarters"
msgstr "Temperatura a tre quarti"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2099
msgid "Sticker Mule"
msgstr "Sticker Mule"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2073
msgid "Sterling Sign"
msgstr "Simbolo della sterlina"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2015
msgid "StayLinked"
msgstr "StayLinked"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2002
msgid "Star Of Life"
msgstr "Stella della vita"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1816
msgid "Square Xmark"
msgstr "Quadrato con segno x"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1803
msgid "Square Virus"
msgstr "Simbolo del virus in un quadrato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1792
msgid "Square Up Right"
msgstr "Quadrato con freccia in alto verso destra"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1776
msgid "Square Share Nodes"
msgstr "Quadrato con il simbolo di condivisione"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1750
msgid "Square Root Variable"
msgstr "Radice quadrata variabile"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1386
msgid "Spray Can"
msgstr "Bomboletta spray"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1211
msgid "Sort Up"
msgstr "Ordinamento verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1200
msgid "Sort Down"
msgstr "Ordinamento verso il basso"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1018
msgid "Sliders"
msgstr "Cursori"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1004
msgid "Sleigh"
msgstr "Slitta"

#: assets/svg/logo-svg-icons/icons-v6-3.php:993
msgid "Slash"
msgstr "Slash"

#: assets/svg/logo-svg-icons/icons-v6-3.php:948
msgid "skyatlas"
msgstr "skyatlas"

#: assets/svg/logo-svg-icons/icons-v6-3.php:934
msgid "Skull Crossbones"
msgstr "Teschio con ossa incrociate"

#: assets/svg/logo-svg-icons/icons-v6-3.php:921
msgid "Skull"
msgstr "Teschio"

#: assets/svg/logo-svg-icons/icons-v6-3.php:891
msgid "Sitrox"
msgstr "Sitrox"

#: assets/svg/logo-svg-icons/icons-v6-3.php:876
msgid "Sith"
msgstr "Sith"

#: assets/svg/logo-svg-icons/icons-v6-3.php:649
msgid "Shop Slash"
msgstr "Negozio con slash"

#: assets/svg/logo-svg-icons/icons-v6-3.php:592
msgid "Shirts in Bulk"
msgstr "Shirts in Bulk"

#: assets/svg/logo-svg-icons/icons-v6-3.php:484
msgid "Shield"
msgstr "Scudo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:457
msgid "Sheet Plastic"
msgstr "Foglio di plastica"

#: assets/svg/logo-svg-icons/icons-v6-3.php:161
msgid "School Flag"
msgstr "Bandiera della scuola"

#: assets/svg/logo-svg-icons/icons-v6-3.php:147
msgid "School Circle Xmark"
msgstr "Scuola con un cerchio con la x"

#: assets/svg/logo-svg-icons/icons-v6-3.php:133
msgid "School Circle Exclamation"
msgstr "Scuola con un cerchio con il punto esclamativo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:90
msgid "SCHLIX"
msgstr "SCHLIX"

#: assets/svg/logo-svg-icons/icons-v6-3.php:34
msgid "Satellite Dish"
msgstr "Antenna satellitare"

#: assets/svg/logo-svg-icons/icons-v6-3.php:21
msgid "Satellite"
msgstr "Satellite"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6402
msgid "Sass"
msgstr "Sass"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6387
msgid "Salesforce"
msgstr "Salesforce"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6373
msgid "Sailboat"
msgstr "Barca a vela"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6358
msgid "Safari"
msgstr "Safari"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6197
msgid "Ruble Sign"
msgstr "Simbolo del rublo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6182
msgid "Rss"
msgstr "Rss"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6168
msgid "Route"
msgstr "Itinerario"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6154
msgid "Rotate Right"
msgstr "Ruota a destra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6140
msgid "Rotate Left"
msgstr "Ruota a sinistra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6125
msgid "Rotate"
msgstr "Ruota"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6110
msgid "Rockrms"
msgstr "Rockrms"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6095
msgid "Rocket.Chat"
msgstr "Rocket.Chat"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6081
msgid "Rocket"
msgstr "Razzo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6067
msgid "Robot"
msgstr "Robot"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6040
msgid "Road Lock"
msgstr "Strada con lucchetto chiuso"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6027
msgid "Road Circle Xmark"
msgstr "Strada con cerchio con la x"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6014
msgid "Road Circle Exclamation"
msgstr "Strada con cerchio con punto esclamativo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6001
msgid "Road Circle Check"
msgstr "Strada con cerchio con segno di verifica"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5988
msgid "Road Bridge"
msgstr "Strada con ponte"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5974
msgid "Road Barrier"
msgstr "Barriera stradale"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5960
msgid "Road"
msgstr "Strada"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5947
msgid "Ring"
msgstr "Anello"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5890
msgid "Ribbon"
msgstr "Nastro"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5729
msgid "Registered"
msgstr "Registrato"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5709
msgid "Redhat"
msgstr "Redhat"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5666
msgid "reddit Logo"
msgstr "Logo reddit"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5681
msgid "reddit Alien"
msgstr "Alien di reddit"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5638
msgid "Recycle"
msgstr "Simbolo di riciclaggio"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5625
msgid "Rectangle Xmark"
msgstr "Rettangolo con la x"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5591
msgid "Rectangle Ad"
msgstr "Rettangolo con scritta Ad"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5563
msgid "Receipt"
msgstr "Ricevuta"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5533
msgid "ReadMe"
msgstr "ReadMe"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1511
msgid "Square Check"
msgstr "Quadrato con il segno di spunta"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1495
msgid "Square Caret Up"
msgstr "Quadrato con cursore verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1479
msgid "Square Caret Right"
msgstr "Quadrato con cursore verso destra"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1463
msgid "Square Caret Left"
msgstr "Quadrato con cursore verso sinistra"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1447
msgid "Square Caret Down"
msgstr "Quadrato con cursore verso giù"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1431
msgid "Square Arrow Up Right"
msgstr "Quadrato con freccia in alto verso destra"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1418
msgid "Square"
msgstr "Quadrato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:244
msgid "Scribd"
msgstr "Scribd"

#: assets/svg/logo-svg-icons/icons-v6-3.php:231
msgid "Screwdriver Wrench"
msgstr "Cacciavite e chiave inglese"

#: assets/svg/logo-svg-icons/icons-v6-3.php:218
msgid "Screwdriver"
msgstr "Cacciavite"

#: assets/svg/logo-svg-icons/icons-v6-3.php:203
msgid "Screenpal"
msgstr "Screenpal"

#: assets/svg/logo-svg-icons/icons-v6-3.php:175
msgid "School Lock"
msgstr "Scuola con un lucchetto chiuso"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6332
msgid "Sack Dollar"
msgstr "Sacco con il simbolo del dollaro"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6319
msgid "S"
msgstr "S"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6210
msgid "Rug"
msgstr "Tappeto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5914
msgid "Right Left"
msgstr "Destra e sinistra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5875
msgid "Rev.io"
msgstr "Rev.io"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5862
msgid "Retweet"
msgstr "Ritwitta"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5849
msgid "Restroom"
msgstr "Bagno"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5819
msgid "Researchgate"
msgstr "Researchgate"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5782
msgid "Reply All"
msgstr "Rispondi a tutti"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6304
msgid "Rust"
msgstr "Rust"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6291
msgid "Rupiah Sign"
msgstr "Segno della rupia"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6264
msgid "Ruler Vertical"
msgstr "Righello verticale"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6250
msgid "Ruler Horizontal"
msgstr "Righello orizzontale"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6223
msgid "Ruler"
msgstr "Righello"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5742
msgid "Renren"
msgstr "Renren"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5651
msgid "red river"
msgstr "red river"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5548
msgid "Rebel Alliance"
msgstr "Rebel Alliance"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5516
msgid "ReactEurope"
msgstr "ReactEurope"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5490
msgid "Ravelry"
msgstr "Ravelry"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5479
msgid "Raspberry Pi"
msgstr "Raspberry Pi"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5452
msgid "Rainbow"
msgstr "Arcobaleno"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5439
msgid "Radio"
msgstr "Radio"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5425
msgid "Radiation"
msgstr "Radiazione"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5287
msgid "Q"
msgstr "Q"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5793
msgid "replyd"
msgstr "replyd"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5505
msgid "React"
msgstr "Reazione"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5410
msgid "R Project"
msgstr "R Project"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5397
msgid "R"
msgstr "R"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5384
msgid "Quote Right"
msgstr "Virgolette verso destra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5371
msgid "Quote Left"
msgstr "Virgolette verso sinistra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5356
msgid "Quora"
msgstr "Quora"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5341
msgid "QuinScape"
msgstr "QuinScape"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5328
msgid "Question"
msgstr "Punto di domanda"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5315
msgid "Qrcode"
msgstr "Qrcode"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5300
msgid "QQ"
msgstr "QQ"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5258
msgid "Puzzle Piece"
msgstr "Pezzo del puzzle"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5122
msgid "Poop"
msgstr "Pupù"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5083
msgid "Podcast"
msgstr "Podcast"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5068
msgid "Plus Minus"
msgstr "Più e meno"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5052
msgid "Plus"
msgstr "Più"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4915
msgid "Plate Wheat"
msgstr "Piatto con spiga di grano"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4902
msgid "Plant Wilt"
msgstr "Pianta appassita"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4889
msgid "Plane Up"
msgstr "Aereo verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4876
msgid "Plane Slash"
msgstr "Aereo sbarrato"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4862
msgid "Plane Lock"
msgstr "Aereo con un lucchetto chiuso"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4835
msgid "Plane Circle Xmark"
msgstr "Aereo con un cerchio con il segno x"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4821
msgid "Plane Circle Exclamation"
msgstr "Aereo con un cerchio con il punto esclamativo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4807
msgid "Plane Circle Check"
msgstr "Aereo con un cerchio con il segno di verifica"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4755
msgid "Pizza Slice"
msgstr "Pezzo di pizza"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4740
msgid "Pix"
msgstr "Pix"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4714
msgid "Pinterest P"
msgstr "P di Pinterest"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4686
msgid "Pills"
msgstr "Pillole"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4673
msgid "Piggy Bank"
msgstr "Salvadanaio a forma di maialino"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4645
msgid "Pied Piper PP Logo (Old)"
msgstr "Pied Piper PP Logo (Old)"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4594
msgid "Pied Piper Logo"
msgstr "Pied Piper Logo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4533
msgid "Phone Slash"
msgstr "Telefono con una sbarra sopra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4488
msgid "Phoenix Squadron"
msgstr "Phoenix Squadron"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4473
msgid "Phoenix Framework"
msgstr "Phoenix Framework"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4458
msgid "Phabricator"
msgstr "Phabricator"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4432
msgid "Peseta Sign"
msgstr "Segno della peseta"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4418
msgid "Person Walking With Cane"
msgstr "Persona che cammina con un bastone"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4405
msgid "Person Walking Luggage"
msgstr "Persona che cammina con un bagaglio"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4392
msgid "Person Walking Dashed Line Arrow Right"
msgstr "Persona che cammina con una linea tratteggiata e una freccia verso destra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4379
msgid "Person Walking Arrow Right"
msgstr "Persona che cammina con una freccia verso destra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4353
msgid "Person Walking"
msgstr "Persona che cammina"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4340
msgid "Person Through Window"
msgstr "Persona attraverso una finestra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4326
msgid "Person Swimming"
msgstr "Persona che nuota"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4313
msgid "Person Snowboarding"
msgstr "Persona che fa snowboard"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4287
msgid "Person Skiing"
msgstr "Persona che scia"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4274
msgid "Person Skating"
msgstr "Persona che pattina"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4132
msgid "Person Harassing"
msgstr "Persona che molesta un'altra persona"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4095
msgid "Person Falling"
msgstr "Persona che cade"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4082
msgid "Person Drowning"
msgstr "Persona che annega"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4058
msgid "Person Dress"
msgstr "Persona con un vestito"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4032
msgid "Person Digging"
msgstr "Persona che scava"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4021
msgid "Person Circle Xmark"
msgstr "Persona con un cerco con una x"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4010
msgid "Person Circle Question"
msgstr "Persona con un cerchio con il punto interrogativo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3988
msgid "Person Circle Minus"
msgstr "Persona con un cerchio con il segno meno"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3999
msgid "Person Circle Plus"
msgstr "Persona con un cerco con il segno più"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3977
msgid "Person Circle Exclamation"
msgstr "Persona con un cerchio con il punto esclamativo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3966
msgid "Person Circle Check"
msgstr "Persona con un cerchio con il segno di verifica"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3952
msgid "Person Chalkboard"
msgstr "Persona che indica alla lavagna"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3938
msgid "Person Cane"
msgstr "Persona con un bastone"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3911
msgid "Person Breastfeeding"
msgstr "Persona che allatta al seno"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3884
msgid "Person Biking"
msgstr "Persona che va in bici"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3849
msgid "Person"
msgstr "Persona"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4247
msgid "Person Running"
msgstr "Persona che corre"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4233
msgid "Person Rifle"
msgstr "Persona con un fucile"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4209
msgid "Person Pregnant"
msgstr "Persona incinta"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4198
msgid "Person Praying"
msgstr "Persona che prega"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4172
msgid "Person Military Rifle"
msgstr "Persona militare con un fucile"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4159
msgid "Person Military Pointing"
msgstr "Persona militare che indica"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4145
msgid "Person Hiking"
msgstr "Persona che fa un'escursione"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3834
msgid "Periscope"
msgstr "Periscope"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3820
msgid "Percent"
msgstr "Percento"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3805
msgid "PerByte"
msgstr "PerByte"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3792
msgid "Pepper Hot"
msgstr "Peperoncino piccante"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3779
msgid "People Roof"
msgstr "Persone sotto un tetto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3766
msgid "People Robbery"
msgstr "Rapina alla gente"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3753
msgid "People Pulling"
msgstr "Gente che tira"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3729
msgid "People Group"
msgstr "Gruppo di persone"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3716
msgid "People Carry Box"
msgstr "Persone che portano uno scatolo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3656
msgid "Pen Ruler"
msgstr "Penna e righello"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3557
msgid "Paw"
msgstr "Zampa"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3543
msgid "Pause"
msgstr "Pausa"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3514
msgid "Paste"
msgstr "Incolla"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3144
msgid "Old Republic"
msgstr "Old Republic"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3130
msgid "Oil Well"
msgstr "Pozzo di petrolio"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3075
msgid "Octopus Deploy"
msgstr "Octopus Deploy"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3062
msgid "Object Ungroup"
msgstr "Separa oggetto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3026
msgid "O"
msgstr "O"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3011
msgid "Nutritionix"
msgstr "Nutritionix"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2996
msgid "NS8"
msgstr "NS8"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2981
msgid "npm"
msgstr "npm"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2859
msgid "NFC Directional"
msgstr "NFC Directional"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2815
msgid "Network Wired"
msgstr "Rete cablata"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2758
msgid "N"
msgstr "N"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2741
msgid "Music"
msgstr "Musica"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2725
msgid "Mug Saucer"
msgstr "Piattino della tazza"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2712
msgid "Mug Hot"
msgstr "Tazza calda"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1958
msgid "MRT"
msgstr "MRT"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2697
msgid "Mountain Sun"
msgstr "Montagna con il sole"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2652
msgid "Mound"
msgstr "Collinetta"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2625
msgid "Mosquito Net"
msgstr "Zanzariera"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2784
msgid "Napster"
msgstr "Napster"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2398
msgid "Monero"
msgstr "Monero"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2383
msgid "MODX"
msgstr "MODX"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2369
msgid "Mobile Screen Button"
msgstr "Pulsante dello schermo del cellulare"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2298
msgid "Mizuni"
msgstr "Mizuni"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2239
msgid "Mitten"
msgstr "Guanto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2225
msgid "Minus"
msgstr "Meno"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2210
msgid "Minimize"
msgstr "Minimizza"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2182
msgid "Microsoft"
msgstr "Microsoft"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2168
msgid "Microscope"
msgstr "Microscopio"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2154
msgid "Microphone Slash"
msgstr "Microfono sbarrato"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3495
msgid "Passport"
msgstr "Passaporto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3482
msgid "Paragraph"
msgstr "Paragrafo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3469
msgid "Parachute Box"
msgstr "Scatola del paracadute"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3455
msgid "Paperclip"
msgstr "Graffetta per fogli"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3441
msgid "Paper Plane"
msgstr "Aereo di carta"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3296
msgid "Padlet"
msgstr "Padlet"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3283
msgid "P"
msgstr "P"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3243
msgid "Open Source Initiative"
msgstr "Open Source Initiative"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3157
msgid "Om"
msgstr "Om"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2112
msgid "Microphone"
msgstr "Microfono"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2099
msgid "Microchip"
msgstr "Microchip"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2084
msgid "Micro.blog"
msgstr "Micro.blog"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2070
msgid "Meteor"
msgstr "Meteora"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2056
msgid "Message"
msgstr "Messaggio"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2040
msgid "Mercury"
msgstr "Mercurio"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1932
msgid "MedApps"
msgstr "MedApps"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1919
msgid "Medal"
msgstr "Medaglia"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1890
msgid "Maximize"
msgstr "Massimizza"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1875
msgid "MaxCDN"
msgstr "MaxCDN"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1862
msgid "Mattress Pillow"
msgstr "Cuscino per materasso"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1847
msgid "Mastodon"
msgstr "Mastodon"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1834
msgid "Masks Theater"
msgstr "Maschere teatrali"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1808
msgid "Mask Face"
msgstr "Mascherina"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1795
msgid "Mask"
msgstr "Maschera"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1781
msgid "Martini Glass Empty"
msgstr "Bicchiere da Martini vuoto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1767
msgid "Martini Glass Citrus"
msgstr "Bicchiere da Martini con agrumi"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1753
msgid "Martini Glass"
msgstr "Bicchiere da Martini"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1570
msgid "Mandalorian"
msgstr "Mandalorian"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1542
msgid "Mailchimp"
msgstr "Mailchimp"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1516
msgid "Magnifying Glass Minus"
msgstr "Lente di ingrandimento con il segno meno"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1529
msgid "Magnifying Glass Plus"
msgstr "Lente di ingrandimento con il segno più"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1503
msgid "Magnifying Glass Location"
msgstr "Lente di ingrandimento con il simbolo di posizione"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1490
msgid "Magnifying Glass Dollar"
msgstr "Lente di ingrandimento con il simbolo del dollaro"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1477
msgid "Magnifying Glass Chart"
msgstr "Lente di ingrandimento con il simbolo del grafico"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1464
msgid "Magnifying Glass Arrow Right"
msgstr "Lente di ingrandimento con la freccia verso destra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1451
msgid "Magnifying Glass"
msgstr "Lente di ingrandimento"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1437
msgid "Magnet"
msgstr "Magnete"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1422
msgid "Magento"
msgstr "Magento"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1409
msgid "M"
msgstr "M"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1381
msgid "Lungs Virus"
msgstr "Virus ai polmoni"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1368
msgid "Lungs"
msgstr "Polmoni"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1355
msgid "Locust"
msgstr "Locusta"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1342
msgid "Lock Open"
msgstr "Lucchetto aperto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1329
msgid "Lock"
msgstr "Lucchetto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1261
msgid "Location Arrow"
msgstr "Freccia di posizione"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1226
msgid "List Ol"
msgstr "Elenco numerato"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1144
msgid "LinkedIn In"
msgstr "In di LinkedIn"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1063
msgid "Lightbulb"
msgstr "Lampadina"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1045
msgid "Life Ring"
msgstr "Salvagente"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1660
msgid "Marker"
msgstr "Pennarello"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1618
msgid "Map Location Dot"
msgstr "Mappa con il punto di posizione"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1590
msgid "Map"
msgstr "Mappa"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1174
msgid "Linux"
msgstr "Linux"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1159
msgid "Linode"
msgstr "Linode"

#: assets/svg/logo-svg-icons/icons-v6-2.php:985
msgid "Lemon"
msgstr "Limone"

#: assets/svg/logo-svg-icons/icons-v6-2.php:969
msgid "Left Right"
msgstr "Sinistra/Destra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:943
msgid "Leanpub"
msgstr "Leanpub"

#: assets/svg/logo-svg-icons/icons-v6-2.php:926
msgid "Leaf"
msgstr "Foglia"

#: assets/svg/logo-svg-icons/icons-v6-2.php:912
msgid "Layer Group"
msgstr "Gruppo di livelli"

#: assets/svg/logo-svg-icons/icons-v6-2.php:862
msgid "Laravel"
msgstr "Laravel"

#: assets/svg/logo-svg-icons/icons-v6-2.php:834
msgid "Laptop File"
msgstr "File del laptop"

#: assets/svg/logo-svg-icons/icons-v6-2.php:820
msgid "Laptop Code"
msgstr "Codice del laptop"

#: assets/svg/logo-svg-icons/icons-v6-2.php:807
msgid "Laptop"
msgstr "Laptop"

#: assets/svg/logo-svg-icons/icons-v6-2.php:794
msgid "Language"
msgstr "Lingua"

#: assets/svg/logo-svg-icons/icons-v6-2.php:753
msgid "Landmark"
msgstr "Monumento"

#: assets/svg/logo-svg-icons/icons-v6-2.php:727
msgid "L"
msgstr "L"

#: assets/svg/logo-svg-icons/icons-v6-2.php:628
msgid "Kickstarter"
msgstr "Kickstarter"

#: assets/svg/logo-svg-icons/icons-v6-2.php:617
msgid "Khanda"
msgstr "Khanda"

#: assets/svg/logo-svg-icons/icons-v6-2.php:602
msgid "KeyCDN"
msgstr "KeyCDN"

#: assets/svg/logo-svg-icons/icons-v6-2.php:588
msgid "Keyboard"
msgstr "Tastiera"

#: assets/svg/logo-svg-icons/icons-v6-2.php:568
msgid "Keybase"
msgstr "Keybase"

#: assets/svg/logo-svg-icons/icons-v6-2.php:553
msgid "Key"
msgstr "Chiave"

#: assets/svg/logo-svg-icons/icons-v6-2.php:538
msgid "Kaggle"
msgstr "Kaggle"

#: assets/svg/logo-svg-icons/icons-v6-2.php:525
msgid "Kaaba"
msgstr "Kaaba"

#: assets/svg/logo-svg-icons/icons-v6-2.php:512
msgid "K"
msgstr "K"

#: assets/svg/logo-svg-icons/icons-v6-2.php:499
msgid "Jug Detergent"
msgstr "Bidone di detersivo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:484
msgid "jsFiddle"
msgstr "jsFiddle"

#: assets/svg/logo-svg-icons/icons-v6-2.php:443
msgid "Joomla Logo"
msgstr "Logo di Joomla"

#: assets/svg/logo-svg-icons/icons-v6-2.php:415
msgid "Joget"
msgstr "Joget"

#: assets/svg/logo-svg-icons/icons-v6-2.php:400
msgid "Jira"
msgstr "Jira"

#: assets/svg/logo-svg-icons/icons-v6-2.php:386
msgid "Jet Fighter Up"
msgstr "Jet da combattimento verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:373
msgid "Jet Fighter"
msgstr "Jet da combattimento"

#: assets/svg/logo-svg-icons/icons-v6-2.php:358
msgid "Jenkis"
msgstr "Jenkis"

#: assets/svg/logo-svg-icons/icons-v6-2.php:326
msgid "Jedi"
msgstr "Jedi"

#: assets/svg/logo-svg-icons/icons-v6-2.php:458
msgid "JavaScript (JS)"
msgstr "JavaScript (JS)"

#: assets/svg/logo-svg-icons/icons-v6-2.php:311
msgid "Java"
msgstr "Java"

#: assets/svg/logo-svg-icons/icons-v6-2.php:298
msgid "Jar Wheat"
msgstr "Grano in un vasetto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:285
msgid "Jar"
msgstr "Vasetto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:272
msgid "J"
msgstr "J"

#: assets/svg/logo-svg-icons/icons-v6-2.php:257
msgid "Itunes Note"
msgstr "Nota di iTunes"

#: assets/svg/logo-svg-icons/icons-v6-2.php:242
msgid "iTunes"
msgstr "iTunes"

#: assets/svg/logo-svg-icons/icons-v6-2.php:227
msgid "itch.io"
msgstr "itch.io"

#: assets/svg/logo-svg-icons/icons-v6-2.php:216
msgid "Italic"
msgstr "Corsivo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:201
msgid "ioxhost"
msgstr "ioxhost"

#: assets/svg/logo-svg-icons/icons-v6-2.php:186
msgid "InVision"
msgstr "InVision"

#: assets/svg/logo-svg-icons/icons-v6-2.php:171
msgid "Internet-explorer"
msgstr "Internet Explorer"

#: assets/svg/logo-svg-icons/icons-v6-2.php:156
msgid "Intercom"
msgstr "Intercom"

#: assets/svg/logo-svg-icons/icons-v6-2.php:141
msgid "InstaLOD"
msgstr "InstaLOD"

#: assets/svg/logo-svg-icons/icons-v6-2.php:102
msgid "Info"
msgstr "Informazioni"

#: assets/svg/logo-svg-icons/icons-v6-2.php:73
msgid "Industry"
msgstr "Industria"

#: assets/svg/logo-svg-icons/icons-v6-2.php:49
msgid "Indent"
msgstr "Rientro"

#: assets/svg/logo-svg-icons/icons-v6-2.php:36
msgid "Inbox"
msgstr "Posta in arrivo"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6674
msgid "Images"
msgstr "Immagini"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6621
msgid "Igloo"
msgstr "Iglù"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6606
msgid "iDeal"
msgstr "iDeal"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6578
msgid "Id Card"
msgstr "Carta d'identità"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6558
msgid "Id Badge"
msgstr "Badge d'identità"

#: assets/svg/logo-svg-icons/icons-v6-2.php:712
msgid "KORVUE"
msgstr "KORVUE"

#: assets/svg/logo-svg-icons/icons-v6-2.php:699
msgid "Kiwi Bird"
msgstr "Uccello Kiwi"

#: assets/svg/logo-svg-icons/icons-v6-2.php:685
msgid "Kitchen Set"
msgstr "Set da cucina"

#: assets/svg/logo-svg-icons/icons-v6-2.php:671
msgid "Kit Medical"
msgstr "Kit medico"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6525
msgid "Icicles"
msgstr "Ghiaccioli"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6511
msgid "Ice Cream"
msgstr "Gelato"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6474
msgid "Hurricane"
msgstr "Uragano"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6444
msgid "HTML 5 Logo"
msgstr "Logo HTML 5"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6416
msgid "Houzz"
msgstr "Houzz"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6375
msgid "House Signal"
msgstr "Segnale della casa"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6236
msgid "House Flag"
msgstr "Bandiera della casa"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6098
msgid "House Chimney"
msgstr "Casa con camino"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6084
msgid "House"
msgstr "Casa"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6073
msgid "Hourglass Start"
msgstr "Inizio della clessidra"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6062
msgid "Hourglass End"
msgstr "Fine della clessidra"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6040
msgid "Hourglass"
msgstr "Clessidra"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5933
msgid "Horse Head"
msgstr "Testa di cavallo"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5919
msgid "Horse"
msgstr "Cavallo"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5904
msgid "Hornbill"
msgstr "Hornbill"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5889
msgid "Hooli"
msgstr "Hooli"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5876
msgid "Holly Berry"
msgstr "Bacche dell'agrifoglio"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5863
msgid "Hockey Puck"
msgstr "Disco da hockey"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5848
msgid "Hive Blockchain Network"
msgstr "Hive Blockchain Network"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5833
msgid "HireAHelper"
msgstr "HireAHelper"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5818
msgid "Hips"
msgstr "Hips"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5805
msgid "Hippo"
msgstr "Ippopotamo"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5792
msgid "Hill Rockslide"
msgstr "Collina che frana"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5765
msgid "Highlighter"
msgstr "Evidenziatore"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5739
msgid "Helmet Safety"
msgstr "Elmetto di sicurezza"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5725
msgid "Helicopter Symbol"
msgstr "Simbolo dell'elicottero"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5711
msgid "Helicopter"
msgstr "Elicottero"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5696
msgid "Heart Pulse"
msgstr "Cuore con battito"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5683
msgid "Heart Crack"
msgstr "Cuore spezzato"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5670
msgid "Heart Circle Xmark"
msgstr "Cuore con un cerchio con la x"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5657
msgid "Heart Circle Plus"
msgstr "Cuore con un cerchio con il segno più"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5644
msgid "Heart Circle Minus"
msgstr "Cuore con un cerchio con il segno meno"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5631
msgid "Heart Circle Exclamation"
msgstr "Cuore con un cerchio con il punto esclamativo"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5618
msgid "Heart Circle Check"
msgstr "Cuore con un cerchio con il segno di spunta"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5587
msgid "Heart"
msgstr "Cuore"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5569
msgid "Headset"
msgstr "Cuffia"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5556
msgid "Headphones Simple"
msgstr "Cuffie semplici"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5541
msgid "Headphones"
msgstr "Cuffie"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5465
msgid "Hat Wizard"
msgstr "Cappello da mago"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5452
msgid "Hat Cowboy Side"
msgstr "Cappello da cowboy di lato"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5439
msgid "Hat Cowboy"
msgstr "Cappello da cowboy"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5423
msgid "Hashtag"
msgstr "Hashtag"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5300
msgid "Hands Praying"
msgstr "Mani che pregano"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5271
msgid "Hands Holding Child"
msgstr "Mani che sostengono un bambino"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4611
msgid "Alternate Pied Piper Logo (Old)"
msgstr "Logo pifferaio magico alternativo (vecchio)"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4538
msgid "Google Wallet"
msgstr "Google Wallet"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4497
msgid "Google Plus"
msgstr "Google Plus"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4482
msgid "Google Play"
msgstr "Google Play"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4466
msgid "Google Pay"
msgstr "Google Pay"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2166
msgid "Store Slash"
msgstr "Negozio barrato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2030
msgid "Steam"
msgstr "Steam"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2957
msgid "Tenge Sign"
msgstr "Simbolo del Tenge"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2388
msgid "Sun Plant Wilt"
msgstr "Pianta appassita con il sole"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2301
msgid "StumbleUpon Circle"
msgstr "StumbleUpon cerchiato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2057
msgid "Steam Symbol"
msgstr "Simbolo Steam"

#: assets/svg/logo-svg-icons/icons-v6-3.php:1736
msgid "Square Poll Vertical"
msgstr "Sondaggio quadrato verticale"

#: assets/svg/logo-svg-icons/icons-v6-3.php:471
msgid "Shekel Sign"
msgstr "Simbolo del siclo"

#: assets/svg/logo-svg-icons/icons-v6-3.php:119
msgid "School Circle Check"
msgstr "Scuola con un cerchio con il segno di verifica"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6345
msgid "Sack Xmark"
msgstr "Sacco con una x"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6278
msgid "Rupee Sign"
msgstr "Simbolo della rupia"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6236
msgid "Ruler Combined"
msgstr "Righello unito"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6053
msgid "Road Spikes"
msgstr "Spuntoni stradali"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5936
msgid "Right To Bracket"
msgstr "A destra della parentesi"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5925
msgid "Right Long"
msgstr "Lungo a destra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5903
msgid "Right From Bracket"
msgstr "A destra dalla parentesi"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5808
msgid "Republican"
msgstr "Republicani"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5096
msgid "Poo"
msgstr "Cacca"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5039
msgid "Plug Circle Xmark"
msgstr "Presa con un cerchio con una x"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5230
msgid "Pump Soap"
msgstr "Erogatore di sapone"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4959
msgid "Plug"
msgstr "Spina"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4628
msgid "Pied Piper Hat (Old)"
msgstr "Cappello da pifferaio magico (vecchio)"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4518
msgid "Phone Flip"
msgstr "Telefono capovolto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4445
msgid "Peso Sign"
msgstr "Simbolo del peso"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4366
msgid "Person Walking Arrow Loop Left"
msgstr "Persona che cammina con la freccia che gira a sinistra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4300
msgid "Person Skiing Nordic"
msgstr "Persona che fa sci nordico"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4260
msgid "Person Shelter"
msgstr "Rifugio per persone"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4106
msgid "Person Falling Burst"
msgstr "Persona che cade a terra"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3898
msgid "Person Booth"
msgstr "Cabina per persone"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3116
msgid "Oil Can"
msgstr "Lattina di olio"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2771
msgid "Naira Sign"
msgstr "Simbolo del naira"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2682
msgid "Mountain City"
msgstr "Montagna e città"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2611
msgid "Mosquito"
msgstr "Zanzara"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2585
msgid "Mortar Pestle"
msgstr "Mortaio e pestello"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2485
msgid "Money Bill Wave"
msgstr "Banconota ondulata"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2498
msgid "Money Bill Wheat"
msgstr "Banconota con spiga di grano"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2027
msgid "Menorah"
msgstr "Menorah"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2012
msgid "Mendeley"
msgstr "Mendeley"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1999
msgid "Memory"
msgstr "Memoria"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1984
msgid "Megaport"
msgstr "Megaport"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1969
msgid "Meetup"
msgstr "Meetup"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1904
msgid "Material Design for Bootstrap"
msgstr "Material Design per Bootstrap"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1821
msgid "Mask Ventilator"
msgstr "Mascherina di ventilazione"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1248
msgid "Litecoin Sign"
msgstr "Simbolo del litecoin"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1237
msgid "List Ul"
msgstr "Elenco UI"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1213
msgid "List Check"
msgstr "Elenco di controllo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1189
msgid "Lira Sign"
msgstr "Simbolo della lira"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1116
msgid "Link Slash"
msgstr "Link barrato"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1090
msgid "Lines Leaning"
msgstr "Linee inclinate"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1079
msgid "Line"
msgstr "Linea"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1027
msgid "Less Than Equal"
msgstr "Minore o uguale a"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1014
msgid "Less Than"
msgstr "Minore di"

#: assets/svg/logo-svg-icons/icons-v6-2.php:999
msgid "Less"
msgstr "Less"

#: assets/svg/logo-svg-icons/icons-v6-2.php:877
msgid "Lari Sign"
msgstr "Simbolo del lari"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2413
msgid "Money Bill"
msgstr "Banconota"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2253
msgid "Mix"
msgstr "Mix"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2197
msgid "Mill Sign"
msgstr "Simbolo del mill"

#: assets/svg/logo-svg-icons/icons-v6-2.php:658
msgid "Kip Sign"
msgstr "Simbolo del kip"

#: assets/svg/logo-svg-icons/icons-v6-2.php:60
msgid "Indian Rupee Sign"
msgstr "Simbolo della rupia indiana"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6655
msgid "Image Portrait"
msgstr "Ritratto d'immagine"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6020
msgid "Hotjar"
msgstr "Hotjar"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6006
msgid "Hotel"
msgstr "Hotel"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5993
msgid "Hotdog"
msgstr "Hotdog"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5951
msgid "Hospital"
msgstr "Ospedale"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5779
msgid "Hill Avalanche"
msgstr "Collina con valanga"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5316
msgid "Handshake"
msgstr "Stretta di mano"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5249
msgid "Hands Clapping"
msgstr "Mani che applaudono"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5238
msgid "Hands Bubbles"
msgstr "Mani con bolle"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5225
msgid "Hands Bound"
msgstr "Mani legate"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6459
msgid "HubSpot"
msgstr "HubSpot"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6431
msgid "Hryvnia Sign"
msgstr "SImbolo della grivna"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6403
msgid "House User"
msgstr "Casa con un utente"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6220
msgid "House Fire"
msgstr "Casa con il fuoco"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6193
msgid "House Circle Xmark"
msgstr "Casa con un cerchio con il segno x"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6180
msgid "House Circle Exclamation"
msgstr "Casa con un cerchio con il punto esclamativo"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6167
msgid "House Circle Check"
msgstr "Casa con un cerchio con il segno di spunta"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6153
msgid "House Chimney Window"
msgstr "Casa con un comignolo e una finestra"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5408
msgid "Hashnode"
msgstr "Hashnode"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5395
msgid "Hard Drive"
msgstr "Disco rigido"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5379
msgid "Hanukiah"
msgstr "Hanukiah"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5368
msgid "Handshake Slash"
msgstr "Stretta di mano con una barra"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5344
msgid "Handshake Simple"
msgstr "Stretta di mano semplice"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4885
msgid "Hand"
msgstr "Mano"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4869
msgid "Hamsa"
msgstr "Hamsa"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4856
msgid "Hammer"
msgstr "Martello"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4802
msgid "H"
msgstr "H"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4789
msgid "Gun"
msgstr "Pistola"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4761
msgid "Guitar"
msgstr "Chitarra"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4746
msgid "Guilded"
msgstr "Guilded"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4733
msgid "Guarani Sign"
msgstr "Simbolo del guaranì"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4718
msgid "Grunt"
msgstr "Grunt"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4690
msgid "Gripfire, Inc."
msgstr "Gripfire, Inc."

#: assets/svg/logo-svg-icons/icons-v6-2.php:3090
msgid "Odnoklassniki"
msgstr "Odnoklassniki"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2446
msgid "Money Bill 1 Wave"
msgstr "Banconota ondulata con il numero 1"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2432
msgid "Money Bill 1"
msgstr "Banconota con il numero 1"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1645
msgid "Markdown"
msgstr "Markdown"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1632
msgid "Map Pin"
msgstr "Puntina della mappa"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4625
msgid "Greater Than Equal"
msgstr "Maggiore uguale di"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4612
msgid "Greater Than"
msgstr "Maggiore di"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4582
msgid "Gratipay (Gittip)"
msgstr "Gratipay (Gittip)"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4567
msgid "Graduation Cap"
msgstr "Cappello di laurea"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4554
msgid "Gopuram"
msgstr "Gopuram"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4113
msgid "Stripe Credit Card"
msgstr "Carta di credito Stripe"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4097
msgid "Paypal Credit Card"
msgstr "Carta di credito Paypal"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1928
msgid "Battery Half"
msgstr "Metà della batteria"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1915
msgid "Battery Full"
msgstr "Batteria carica"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1902
msgid "Battery Empty"
msgstr "Batteria scarica"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1873
msgid "Basketball"
msgstr "Pallacanestro"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1832
msgid "Baseball"
msgstr "Baseball"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2268
msgid "BlackBerry"
msgstr "BlackBerry"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2238
msgid "Bity"
msgstr "Bity"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3519
msgid "Calendar Day"
msgstr "Giorno del calendario"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3508
msgid "Calendar Check"
msgstr "Calendario con segno di spunta"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3489
msgid "Calendar"
msgstr "Calendario"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3470
msgid "Calculator"
msgstr "Calcolatrice"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3425
msgid "BuySellAds"
msgstr "BuySellAds"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3410
msgid "Buy n Large"
msgstr "Buy n Large"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3397
msgid "Business Time"
msgstr "Orario di lavoro"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3369
msgid "Bus"
msgstr "Bus"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3330
msgid "Burger"
msgstr "Burger"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3317
msgid "Bullseye"
msgstr "Bersaglio"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3723
msgid "Car"
msgstr "Auto"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3710
msgid "Capsules"
msgstr "Capsule"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3696
msgid "Cannabis"
msgstr "Cannabis"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3683
msgid "Candy Cane"
msgstr "Bastoncino di zucchero"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3668
msgid "Canadian Maple Leaf"
msgstr "Foglia d'acero canadese"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3654
msgid "Campground"
msgstr "Campeggio"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3607
msgid "Camera"
msgstr "Fotocamera"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3882
msgid "Carrot"
msgstr "Carota"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4274
msgid "Charging Station"
msgstr "Stazione di ricarica"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4261
msgid "Champagne Glasses"
msgstr "Bicchieri di champagne"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4233
msgid "Chalkboard"
msgstr "Lavagna"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4220
msgid "Chair"
msgstr "Sedia"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4205
msgid "Certificate"
msgstr "Certificato"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4190
msgid "Centos"
msgstr "Centos"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4175
msgid "Centercode"
msgstr "Centercode"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3975
msgid "Cat"
msgstr "Gatto"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3961
msgid "Cash Register"
msgstr "Registratore di cassa"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3947
msgid "Cart Shopping"
msgstr "Carrello della spesa"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4477
msgid "Chess King"
msgstr "Re degli scacchi"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4459
msgid "Chess Board"
msgstr "Scacchiera"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4446
msgid "Chess Bishop"
msgstr "Alfiere degli scacchi"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4428
msgid "Chess"
msgstr "Scacchi"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4415
msgid "Cheese"
msgstr "Formaggio"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4364
msgid "Chart Simple"
msgstr "Grafico semplice"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4351
msgid "Chart Pie"
msgstr "Grafico a torta"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4327
msgid "Chart Gantt"
msgstr "Grafico Gantt"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4305
msgid "Chart Bar"
msgstr "Grafico a barre"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4248
msgid "Chalkboard User"
msgstr "Utente alla lavagna"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4669
msgid "Chrome"
msgstr "Chrome"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4656
msgid "Children"
msgstr "Bambini"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4619
msgid "Child Dress"
msgstr "Abito da bambino"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4606
msgid "Child"
msgstr "Bambino"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4549
msgid "Chess Rook"
msgstr "Torre degli scacchi"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4531
msgid "Chess Queen"
msgstr "Regina degli scacchi"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4513
msgid "Chess Pawn"
msgstr "Pedone degli scacchi"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4495
msgid "Chess Knight"
msgstr "Cavallo degli scacchi"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3823
msgid "Caravan"
msgstr "Caravan"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3809
msgid "Car Tunnel"
msgstr "Tunnel automobilistico"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3795
msgid "Car Side"
msgstr "Fiancata dell'auto"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4717
msgid "Circle"
msgstr "Cerchio"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4699
msgid "Church"
msgstr "Chiesa"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4684
msgid "Chromecast"
msgstr "Chromecast"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5324
msgid "Cloud"
msgstr "Cloud"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5262
msgid "Clock"
msgstr "Orologio"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5191
msgid "Clipboard"
msgstr "Appunti"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5160
msgid "City"
msgstr "Città"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5486
msgid "Cloudflare"
msgstr "Cloudflare"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5696
msgid "Coins"
msgstr "Monete"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5681
msgid "Codie Pie"
msgstr "Codie Pie"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5666
msgid "Codepen"
msgstr "Codepen"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5575
msgid "Code"
msgstr "Codice"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5531
msgid "cloudversify"
msgstr "cloudversify"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5516
msgid "Cloudsmith"
msgstr "Cloudsmith"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5920
msgid "Computer Mouse"
msgstr "Mouse del computer"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5906
msgid "Computer"
msgstr "Computer"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5892
msgid "Compress"
msgstr "Comprimi"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5864
msgid "Compass"
msgstr "Bussola"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5844
msgid "Compact Disc"
msgstr "Compact Disc"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5727
msgid "Comment"
msgstr "Commento"

#: assets/svg/logo-svg-icons/icons-v6-0.php:6085
msgid "cPanel"
msgstr "cPanel"

#: assets/svg/logo-svg-icons/icons-v6-0.php:6072
msgid "Cow"
msgstr "Mucca"

#: assets/svg/logo-svg-icons/icons-v6-0.php:6058
msgid "Couch"
msgstr "Divano"

#: assets/svg/logo-svg-icons/icons-v6-0.php:6043
msgid "Cotton Bureau"
msgstr "Cotton Bureau"

#: assets/svg/logo-svg-icons/icons-v6-0.php:6011
msgid "Copy"
msgstr "Copia"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5978
msgid "Cookie"
msgstr "Cookie"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5963
msgid "Contao"
msgstr "Contao"

#: assets/svg/logo-svg-icons/icons-v6-1.php:59
msgid "Crown"
msgstr "Corona"

#: assets/svg/logo-svg-icons/icons-v6-1.php:46
msgid "Crow"
msgstr "Corvo"

#: assets/svg/logo-svg-icons/icons-v6-1.php:32
msgid "Crosshairs"
msgstr "Mirino"

#: assets/svg/logo-svg-icons/icons-v6-1.php:21
msgid "Cross"
msgstr "Croce"

#: assets/svg/logo-svg-icons/icons-v6-0.php:6345
msgid "Crop"
msgstr "Ritaglia"

#: assets/svg/logo-svg-icons/icons-v6-0.php:6315
msgid "Credit Card"
msgstr "Carta di credito"

#: assets/svg/logo-svg-icons/icons-v6-1.php:267
msgid "Deezer"
msgstr "Deezer"

#: assets/svg/logo-svg-icons/icons-v6-1.php:254
msgid "Database"
msgstr "Database"

#: assets/svg/logo-svg-icons/icons-v6-1.php:239
msgid "DashCube"
msgstr "DashCube"

#: assets/svg/logo-svg-icons/icons-v6-1.php:224
msgid "dailymotion"
msgstr "dailymotion"

#: assets/svg/logo-svg-icons/icons-v6-1.php:208
msgid "D&D Beyond"
msgstr "D&D Beyond"

#: assets/svg/logo-svg-icons/icons-v6-1.php:182
msgid "D"
msgstr "D"

#: assets/svg/logo-svg-icons/icons-v6-1.php:138
msgid "Cubes"
msgstr "Cubi"

#: assets/svg/logo-svg-icons/icons-v6-1.php:124
msgid "Cube"
msgstr "Cubo"

#: assets/svg/logo-svg-icons/icons-v6-1.php:98
msgid "CSS 3 Logo"
msgstr "Logo CSS 3"

#: assets/svg/logo-svg-icons/icons-v6-1.php:72
msgid "Crutch"
msgstr "Stampella"

#: assets/svg/logo-svg-icons/icons-v6-1.php:508
msgid "Dice"
msgstr "Dado"

#: assets/svg/logo-svg-icons/icons-v6-1.php:493
msgid "Diaspora"
msgstr "Diaspora"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4338
msgid "Chart Line"
msgstr "Grafico a linee"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4316
msgid "Chart Column"
msgstr "Grafico a colonne"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4289
msgid "Chart Area"
msgstr "Grafico ad area"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4378
msgid "Check"
msgstr "Segno di spunta"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5291
msgid "Clone"
msgstr "Clona"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5310
msgid "Closed Captioning"
msgstr "Sottotitoli"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4645
msgid "Child Rifle"
msgstr "Bambino e fucile"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1954
msgid "Battery Three Quarters"
msgstr "Tre quarti di batteria"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1941
msgid "Battery Quarter"
msgstr "Un quarto di batteria"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1886
msgid "Bath"
msgstr "Vasca da bagno"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3871
msgid "Caret Up"
msgstr "Triangolo rivolto verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-1.php:295
msgid "Delicious"
msgstr "Delicious"

#: assets/svg/logo-svg-icons/icons-v6-1.php:167
msgid "Cuttlefish"
msgstr "Cuttlefish"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5231
msgid "Clipboard Question"
msgstr "Appunti con punto di domanda"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5218
msgid "Clipboard List"
msgstr "Appunti con elenco"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5204
msgid "Clipboard Check"
msgstr "Appunti con segno di spunta"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4595
msgid "Chevron Up"
msgstr "Freccetta rivolta verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4584
msgid "Chevron Right"
msgstr "Freccetta rivolta verso destra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4573
msgid "Chevron Left"
msgstr "Freccetta rivolta verso sinistra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4562
msgid "Chevron Down"
msgstr "Freccetta rivolta verso il basso"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3934
msgid "Cart Plus"
msgstr "Carrello con segno più"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3860
msgid "Caret Right"
msgstr "Triangolo rivolto verso destra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3849
msgid "Caret Left"
msgstr "Triangolo rivolto verso sinistra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4133
msgid "Visa Credit Card"
msgstr "Carta di credito Visa"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4081
msgid "MasterCard Credit Card"
msgstr "Carta di credito MasterCard"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4065
msgid "JCB Credit Card"
msgstr "Carta di credito JCB"

#: assets/svg/logo-svg-icons/icons-v6-0.php:666
msgid "iOS App Store"
msgstr "iOS App Store"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4004
msgid "American Express Credit Card"
msgstr "Carta di credito American Express"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1547
msgid "Amazon Web Services (AWS)"
msgstr "Amazon Web Services (AWS)"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3988
msgid "Amazon Pay Credit Card"
msgstr "Carta di credito Amazon Pay"

#: assets/svg/logo-svg-icons/icons-v6-0.php:391
msgid "Amazon Pay"
msgstr "Amazon Pay"

#: assets/svg/logo-svg-icons/icons-v6-0.php:376
msgid "Amazon"
msgstr "Amazon"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4207
msgid "Alternate GitHub"
msgstr "Alternate GitHub"

#: assets/svg/logo-svg-icons/icons-v6-0.php:360
msgid "Alipay"
msgstr "Alipay"

#: assets/svg/logo-svg-icons/icons-v6-0.php:349
msgid "Align Right"
msgstr "Allinea a destra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:338
msgid "Align Left"
msgstr "Allinea a sinistra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1697
msgid "Bahai"
msgstr "Bahai"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1683
msgid "Bag Shopping"
msgstr "Borsa da shopping"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1670
msgid "Bacterium"
msgstr "Batterio"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1657
msgid "Bacteria"
msgstr "Batteri"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1644
msgid "Bacon"
msgstr "Bacon"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1588
msgid "Baby Carriage"
msgstr "Carrozzina"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1575
msgid "Baby"
msgstr "Neonato"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1562
msgid "B"
msgstr "B"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1534
msgid "Award"
msgstr "Premio"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1519
msgid "Aviato"
msgstr "Aviato"

#: assets/svg/logo-svg-icons/icons-v6-0.php:901
msgid "Arrow Right"
msgstr "Freccia verso destra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:890
msgid "Arrow Pointer"
msgstr "Puntatore a freccia"

#: assets/svg/logo-svg-icons/icons-v6-0.php:758
msgid "Arrow Down"
msgstr "Freccia verso il basso"

#: assets/svg/logo-svg-icons/icons-v6-0.php:744
msgid "Archway"
msgstr "Arco"

#: assets/svg/logo-svg-icons/icons-v6-0.php:727
msgid "Apple Whole"
msgstr "Mela intera"

#: assets/svg/logo-svg-icons/icons-v6-0.php:4017
msgid "Apple Pay Credit Card"
msgstr "Carta di credito Apple Pay"

#: assets/svg/logo-svg-icons/icons-v6-0.php:696
msgid "Apple"
msgstr "Apple"

#: assets/svg/logo-svg-icons/icons-v6-0.php:681
msgid "Apper Systems AB"
msgstr "Apper Systems AB"

#: assets/svg/logo-svg-icons/icons-v6-0.php:245
msgid "App.net"
msgstr "App.net"

#: assets/svg/logo-svg-icons/icons-v6-0.php:651
msgid "App Store"
msgstr "App Store"

#: assets/svg/logo-svg-icons/icons-v6-0.php:507
msgid "AngelList"
msgstr "AngelList"

#: assets/svg/logo-svg-icons/icons-v6-0.php:492
msgid "Android"
msgstr "Android"

#: assets/svg/logo-svg-icons/icons-v6-0.php:478
msgid "Anchor Lock"
msgstr "Ancora con il lucchetto chiuso"

#: assets/svg/logo-svg-icons/icons-v6-0.php:464
msgid "Anchor Circle Xmark"
msgstr "Ancora con un cerchio con il segno x"

#: assets/svg/logo-svg-icons/icons-v6-0.php:450
msgid "Anchor Circle Exclamation"
msgstr "Ancora con un cerchio con il punto esclamativo"

#: assets/svg/logo-svg-icons/icons-v6-0.php:436
msgid "Anchor Circle Check"
msgstr "Ancora con un cerchio con il segno di spunta"

#: assets/svg/logo-svg-icons/icons-v6-0.php:422
msgid "Anchor"
msgstr "Ancora"

#: assets/svg/logo-svg-icons/icons-v6-0.php:407
msgid "Amilia"
msgstr "Amilia"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5992
msgid "Cookie Bite"
msgstr "Biscotto con un morso"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5948
msgid "Connect Develop"
msgstr "Connect Develop"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5933
msgid "Confluence"
msgstr "Confluence"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5546
msgid "Clover"
msgstr "Trifoglio"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5501
msgid "cloudscale.ch"
msgstr "cloudscale.ch"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5473
msgid "Cloud Sun Rain"
msgstr "Nuvola con il sole e con la pioggia"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5421
msgid "Cloud Rain"
msgstr "Nuvola con la pioggia"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5460
msgid "Cloud Sun"
msgstr "Nuvola con il sole"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5408
msgid "Cloud Moon Rain"
msgstr "Nuvola con la luna e la pioggia"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5395
msgid "Cloud Moon"
msgstr "Nuvola con la luna"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5368
msgid "Cloud Bolt"
msgstr "Nuvola con il fulmine"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5354
msgid "Cloud Arrow Up"
msgstr "Nuvola con la freccia verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5340
msgid "Cloud Arrow Down"
msgstr "Nuvola con la freccia verso il basso"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5273
msgid "Clock Rotate Left"
msgstr "Orologio con freccia che ruota a sinistra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5173
msgid "Clapperboard"
msgstr "Ciak"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5147
msgid "Circle Xmark"
msgstr "Cerchio con il segno x"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5129
msgid "Circle User"
msgstr "Cerchio con all'interno un utente"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5113
msgid "Circle Up"
msgstr "Cerchio con la freccia verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3596
msgid "Calendar Xmark"
msgstr "Calendario con il segno x"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3569
msgid "Calendar Plus"
msgstr "Calendario con il segno più"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3553
msgid "Calendar Minus"
msgstr "Calendario con il segno meno"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3535
msgid "Calendar Days"
msgstr "Calendario con i giorni"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3453
msgid "Cake Candles"
msgstr "Torta con le candeline"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3440
msgid "C"
msgstr "C"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3383
msgid "Bus Simple"
msgstr "Autobus semplice"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3354
msgid "Burst"
msgstr "Esplosione"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3302
msgid "Bullhorn"
msgstr "Altoparlante"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3289
msgid "Building Wheat"
msgstr "Edificio con una spiga di grano"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3129
msgid "Building"
msgstr "Edificio"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3084
msgid "Bug"
msgstr "Bug"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3069
msgid "Buffer"
msgstr "Buffer"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3054
msgid "Bucket"
msgstr "Secchio"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3037
msgid "BTC"
msgstr "BTC"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3023
msgid "Brush"
msgstr "Pennello"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2999
msgid "Broom"
msgstr "Scopa"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2986
msgid "Briefcase Medical"
msgstr "Valigetta medica"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2972
msgid "Briefcase"
msgstr "Valigetta"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2787
msgid "Box Open"
msgstr "Scatola aperta"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2758
msgid "Box"
msgstr "Scatola"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2745
msgid "Bowling Ball"
msgstr "Palla da bowling"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2732
msgid "Bowl Rice"
msgstr "Ciotola di riso"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2719
msgid "Bowl Food"
msgstr "Ciotola di cibo"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2706
msgid "Bottle Water"
msgstr "Bottiglia d'acqua"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2693
msgid "Bottle Droplet"
msgstr "Bottiglia con goccia"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2678
msgid "Bots"
msgstr "Bot"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2617
msgid "Bootstrap"
msgstr "Bootstrap"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4451
msgid "Google Drive"
msgstr "Google Drive"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4436
msgid "Google Logo"
msgstr "Logo Google"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3805
msgid "FreeBSD"
msgstr "FreeBSD"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3623
msgid "Font Awesome"
msgstr "Font Awesome"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3264
msgid "Firefox Browser"
msgstr "Browser Firefox"

#: assets/svg/logo-svg-icons/icons-v6-1.php:2638
msgid "Figma"
msgstr "Figma"

#: assets/svg/logo-svg-icons/icons-v6-1.php:2609
msgid "Fedora"
msgstr "Fedora"

#: assets/svg/logo-svg-icons/icons-v6-1.php:2471
msgid "Facebook Messenger"
msgstr "Facebook Messenger"

#: assets/svg/logo-svg-icons/icons-v6-1.php:1608
msgid "Etsy"
msgstr "Etsy"

#: assets/svg/logo-svg-icons/icons-v6-1.php:1594
msgid "Ethernet"
msgstr "Ethernet"

#: assets/svg/logo-svg-icons/icons-v6-1.php:1339
msgid "Elementor"
msgstr "Elementor"

#: assets/svg/logo-svg-icons/icons-v6-1.php:980
msgid "Dropbox"
msgstr "Dropbox"

#: assets/svg/logo-svg-icons/icons-v6-1.php:642
msgid "Digital Ocean"
msgstr "Digital Ocean"

#: assets/svg/logo-svg-icons/icons-v6-1.php:405
msgid "DHL"
msgstr "DHL"

#: assets/svg/logo-svg-icons/icons-v6-0.php:6100
msgid "Creative Commons"
msgstr "Creative Commons"

#: assets/svg/logo-svg-icons/icons-v6-0.php:286
msgid "Airbnb"
msgstr "Airbnb"

#: assets/svg/logo-svg-icons/icons-v6-0.php:146
msgid "500px"
msgstr "500px"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3424
msgid "Flickr"
msgstr "Flickr"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4250
msgid "GitLab"
msgstr "GitLab"

#: assets/svg/logo-svg-icons/icons-v6-1.php:758
msgid "Docker"
msgstr "Docker"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1390
msgid "Asymmetrik, Ltd."
msgstr "Asymmetrik, Ltd."

#: assets/svg/logo-svg-icons/icons-v6-0.php:1418
msgid "Atlassian"
msgstr "Atlassian"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1433
msgid "Atom"
msgstr "Atom"

#: assets/svg/logo-svg-icons/icons-v6-0.php:327
msgid "Align Justify"
msgstr "Allineamento giustificato"

#: assets/svg/logo-svg-icons/icons-v6-0.php:316
msgid "Align Center"
msgstr "Allinea al centro"

#: assets/svg/logo-svg-icons/icons-v6-0.php:301
msgid "Algolia"
msgstr "Algolia"

#: assets/svg/logo-svg-icons/icons-v6-0.php:271
msgid "affiliatetheme"
msgstr "affiliatetheme"

#: assets/svg/logo-svg-icons/icons-v6-0.php:256
msgid "Adversal"
msgstr "Adversal"

#: assets/svg/logo-svg-icons/icons-v6-0.php:231
msgid "Address Card"
msgstr "Scheda dell'indirizzo"

#: assets/svg/logo-svg-icons/icons-v6-0.php:212
msgid "Address Book"
msgstr "Rubrica"

#: assets/svg/logo-svg-icons/icons-v6-0.php:640
msgid "Ankh"
msgstr "Ankh"

#: assets/svg/logo-svg-icons/icons-v6-0.php:625
msgid "Angular"
msgstr "Angolare"

#: assets/svg/logo-svg-icons/icons-v6-0.php:610
msgid "Angry Creative"
msgstr "Angry Creative"

#: assets/svg/logo-svg-icons/icons-v6-0.php:599
msgid "Angles Up"
msgstr "Angoli verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-0.php:577
msgid "Angles Left"
msgstr "Angoli verso sinistra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:588
msgid "Angles Right"
msgstr "Angoli verso destra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:566
msgid "Angles Down"
msgstr "Angoli verso giù"

#: assets/svg/logo-svg-icons/icons-v6-0.php:555
msgid "Angle Up"
msgstr "Angolo verso l'alto"

#: assets/svg/logo-svg-icons/icons-v6-0.php:544
msgid "Angle Right"
msgstr "Angolo verso destra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:533
msgid "Angle Left"
msgstr "Angolo verso sinistra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:522
msgid "Angle Down"
msgstr "Angolo verso giù"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3690
msgid "Alternate Fort Awesome"
msgstr "Fort Awesome alternativo"

#: assets/svg/logo-svg-icons/icons-v6-0.php:923
msgid "Arrow Right From Bracket"
msgstr "Freccia a destra dalla parentesi"

#: assets/svg/logo-svg-icons/icons-v6-0.php:912
msgid "Arrow Right Arrow Left"
msgstr "Freccia a destra e freccia a sinistra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:868
msgid "Arrow Left"
msgstr "Freccia a sinistra"

#: assets/svg/logo-svg-icons/icons-v6-0.php:857
msgid "Arrow Down Z A"
msgstr "Freccia verso giù Z A"

#: assets/svg/logo-svg-icons/icons-v6-0.php:791
msgid "Arrow Down A Z"
msgstr "Freccia verso giù A  Z"

#: assets/svg/logo-svg-icons/icons-v6-0.php:780
msgid "Arrow Down 9 1"
msgstr "Freccia verso giù 9 1"

#: assets/svg/logo-svg-icons/icons-v6-0.php:769
msgid "Arrow Down 1 9"
msgstr "Freccia verso giù 1 9"

#: assets/svg/logo-svg-icons/icons-v6-0.php:192
msgid "Accusoft"
msgstr "Accusoft"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4406
msgid "Goodreads"
msgstr "Goodreads"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4393
msgid "Golf Ball Tee"
msgstr "Punta con pallina da golf"

#: admin/includes/class-astra-menu.php:982
msgid "A versatile form builder plugin."
msgstr "Un plugin versatile per la creazione di moduli."

#: assets/svg/logo-svg-icons/icons-v6-0.php:161
msgid "A"
msgstr "A"

#: assets/svg/logo-svg-icons/icons-v6-0.php:131
msgid "42.group"
msgstr "42.group"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4421
msgid "Goodreads G"
msgstr "Goodreads G"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4367
msgid "Gofore"
msgstr "Gofore"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4352
msgid "Globe"
msgstr "Globo"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4337
msgid "Glide G"
msgstr "Glide G"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4322
msgid "Glide"
msgstr "Glide"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4095
msgid "GG Currency Circle"
msgstr "Valuta GG cerchiata"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4082
msgid "GG Currency"
msgstr "Valuta GG"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4056
msgid "Genderless"
msgstr "Senza genere"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4043
msgid "Gem"
msgstr "Gemma"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4024
msgid "Gears"
msgstr "Ingranaggi"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4013
msgid "Gear"
msgstr "Ingranaggio"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4000
msgid "Gavel"
msgstr "Martelletto"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3972
msgid "Gauge Simple"
msgstr "Indicatore semplice"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3944
msgid "Gauge"
msgstr "Indicatore"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3897
msgid "Galactic Senate"
msgstr "Senato galattico"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3881
msgid "Galactic Republic"
msgstr "Repubblica galattica"

#: assets/svg/logo-svg-icons/icons-v6-1.php:1423
msgid "Galactic Empire"
msgstr "Impero galattico"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3868
msgid "G"
msgstr "G"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3855
msgid "Futbol"
msgstr "Calcio"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3835
msgid "Fulcrum"
msgstr "Fulcrum"

#: inc/customizer/configurations/builder/header/configs/account.php:167
msgid "Choose if you want to display Icon or Avatar with the Text selected Profile Type."
msgstr "Scegli se desideri visualizzare l'icona o l'avatar con il profilo di tipo Testo selezionato."

#: inc/customizer/configurations/builder/header/configs/account.php:290
msgid "Choose if you want to display Icon with the Text selected Profile Type for logged out users."
msgstr "Scegli se desideri visualizzare l'icona con il profilo di tipo Testo selezionato per gli utenti disconnessi."

#: inc/customizer/configurations/builder/header/configs/account.php:168
#: inc/customizer/configurations/builder/header/configs/account.php:291
msgid "Show Text with"
msgstr "Mostra testo con"

#: inc/core/class-theme-strings.php:56
msgid "Name"
msgstr "Nome"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:114
msgid "This setting is generally not recommended on search page. If you would like to enable it, uncheck this option"
msgstr "Questa impostazione generalmente non è consigliata nella pagina di ricerca. Se desideri abilitarla, deseleziona questa opzione"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:129
msgid "This setting is generally not recommended on archives pages, etc. If you would like to enable it, uncheck this option"
msgstr "Questa impostazione generalmente non è consigliata nelle pagine di archivio, ecc. Se desideri abilitarla, deseleziona questa opzione"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:99
msgid "This setting is generally not recommended on 404 page. If you would like to enable it, uncheck this option"
msgstr "Questa impostazione generalmente non è consigliata sulla pagina 404. Se desideri abilitarla, deseleziona questa opzione"

#: inc/markup-extras.php:1074
msgid "WordPress Theme"
msgstr "Tema WordPress"

#: inc/compatibility/surecart/class-astra-surecart.php:398
msgid "Upsell"
msgstr "Upsell"

#: inc/builder/type/footer/menu/class-astra-footer-menu-component.php:82
#: inc/builder/type/header/mobile-menu/class-astra-mobile-menu-component.php:78
msgid "Site Navigation: "
msgstr "Navigazione sito: "

#: inc/compatibility/surecart/class-astra-surecart.php:391
#: inc/compatibility/surecart/customizer/class-astra-customizer-register-surecart-section.php:45
msgid "Products"
msgstr "Prodotti"

#: inc/compatibility/surecart/class-astra-surecart.php:394
msgid "Collection"
msgstr "Raccolta"

#: inc/modules/related-posts/class-astra-related-posts-markup.php:353
msgid "Read more about %s"
msgstr "Leggi di più su %s"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:468
msgid "This is a demo store for testing purposes &mdash; no orders shall be fulfilled."
msgstr "Questo è un negozio demo per finalità di test &mdash; nessun ordine verrà evaso."

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:372
msgid "'None' respects hierarchy; 'Behind' position is not applicable for single product page."
msgstr "L'opzione 'Nessuno' rispetta la gerarchia; la posizione 'Dietro' non è applicabile alla pagina prodotto singolo."

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:375
msgid "'None' respects hierarchy; 'Below' position is not applicable for single product page."
msgstr "L'opzione 'Nessuno' rispetta la gerarchia; la posizione 'Sotto' non è applicabile alla pagina prodotto singolo."

#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:89
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:104
msgid " Page Title"
msgstr " Titolo della pagina"

#: inc/core/class-astra-admin-settings.php:238
msgid "I want to build this website from scratch"
msgstr "Voglio costruire questo sito web da zero"

#: inc/core/class-astra-admin-settings.php:228
msgid "Say goodbye to the days of spending weeks designing and building your website. With Astra and our Starter Templates plugin, you can now create professional-grade websites in minutes."
msgstr "Di' addio ai giorni in cui dovevi passare settimane a progettare e realizzare il tuo sito web. Con Astra e il nostro plugin Starter Templates, potrai realizzare siti web di livello professionale in pochi minuti."

#: inc/core/class-astra-admin-settings.php:227
msgid "Build Your Dream Site in Minutes With AI 🚀"
msgstr "Realizza il tuo sito da sogno in pochi minuti con l'IA 🚀"

#: inc/core/class-astra-admin-settings.php:226
msgid "Thank you for choosing the Astra theme!"
msgstr "Grazie per aver scelto il tema Astra!"

#: inc/core/class-astra-admin-settings.php:191
msgid "Let’s Get Started with Starter Templates"
msgstr "Comincia con i Template iniziali"

#: inc/core/class-astra-admin-settings.php:239
msgid "300+ Templates"
msgstr "300+ template"

#: inc/customizer/configurations/typography/class-astra-archive-typo-configs.php:172
msgid "Taxonomy Font"
msgstr "Font della tassonomia"

#: inc/customizer/configurations/typography/class-astra-archive-typo-configs.php:99
msgid "Post Title Size"
msgstr "Dimensione del titolo dell'articolo"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:50
msgid "Layout 3"
msgstr "Layout 3"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:562
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:585
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:798
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1601
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:474
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:493
msgid "Badge"
msgstr "Badge"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1202
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:69
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:45
msgid "List"
msgstr "Elenco"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:65
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:41
msgid "Grid"
msgstr "Griglia"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:57
msgid "Classic Layout"
msgstr "Layout classico"

#. translators: %s: Name of special page type
#: inc/customizer/class-astra-customizer-register-sections-panels.php:440
msgid "%s Page"
msgstr "%s pagina"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:230
msgid "Single Page"
msgstr "Pagina singola"

#: inc/core/class-theme-strings.php:71 inc/core/class-theme-strings.php:73
#: inc/core/class-theme-strings.php:90
msgid "Previous"
msgstr "Precedente"

#: inc/core/class-theme-strings.php:70 inc/core/class-theme-strings.php:72
#: inc/core/class-theme-strings.php:89
msgid "Next"
msgstr "Successivo"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:738
msgid "To sync this option with comments, use the same positioning for both sections: Contained or Separated."
msgstr "Per sincronizzare questa opzione con i commenti, utilizza lo stesso posizionamento per entrambe le sezioni: Contenuto o Separato."

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:732
msgid "Location"
msgstr "Posizione"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:377
msgid "Note: Image Size & Ratio won't work if Image Position set as Background."
msgstr "Nota: dimensioni e rapporto dell'immagine non funzionano se la posizione dell'immagine è impostata come sfondo."

#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:770
msgid "This option activates Live Search support for the search box on the no results page."
msgstr "Questa opzione attiva il supporto di Live Search per la casella di ricerca nella pagina dei risultati."

#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:742
msgid "Results Layout"
msgstr "Layout risultati"

#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:248
msgid "When Results Not Found"
msgstr "Quando i risultati non vengono trovati"

#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:233
msgid "When Results Found"
msgstr "Quando i risultati vengono trovati"

#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:71
msgid "Subheading"
msgstr "Sottotitolo"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5530
#: inc/customizer/class-astra-customizer.php:1613
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:64
msgid "Heading"
msgstr "Titolo"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:861
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:401
msgid "Author Avatar"
msgstr "Avatar dell'autore"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:844
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:388
msgid "Prefix Label"
msgstr "Prefisso Etichetta"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:722
msgid "Remove the padding around featured image when position is \"None\"."
msgstr "Rimuove la spaziatura interna intorno all'immagine in evidenza quando la posizione è \"Nessuna\"."

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:721
msgid "Remove Image Padding"
msgstr "Rimuovi la spaziatura interna dell'immagine"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:303
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:311
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:319
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:365
msgid "Taxonomies"
msgstr "Tassonomie"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:1005
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1632
msgid " Title Area"
msgstr " Area del titolo"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:94
msgid "Reveal Effect"
msgstr "Effetto rivela"

#: inc/markup-extras.php:526
msgid "Post pagination"
msgstr "Paginazione articoli"

#: inc/customizer/configurations/typography/class-astra-single-typo-configs.php:49
msgid "Author Box with Social Share"
msgstr "Box autore con condivisione social"

#: inc/customizer/configurations/typography/class-astra-archive-typo-configs.php:135
msgid "Meta Font Size"
msgstr "Dimensione del font dei meta"

#: inc/customizer/configurations/typography/class-astra-archive-typo-configs.php:50
msgid "Posts Reveal Effect"
msgstr "Effetto rivela per gli articoli"

#: inc/customizer/configurations/typography/class-astra-archive-typo-configs.php:47
msgid "Posts Filter"
msgstr "Filtro articoli"

#: inc/customizer/configurations/builder/header/configs/search.php:128
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:771
msgid "Live Search"
msgstr "Live Search"

#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:93
msgid "Content Images Box Shadow"
msgstr "Ombreggiatura del box contenuto immagini"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:599
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:947
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:225
msgid "Divider Type"
msgstr "Tipo di separatore"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:486
msgid "Post Cards"
msgstr "Schede articolo"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:458
msgid "Zoom Out"
msgstr "Zoom indietro"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:450
msgid "Hover Effect"
msgstr "Effetto al passaggio del mouse"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:284
msgid "Read More"
msgstr "Leggi tutto"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:270
msgid "Post Meta"
msgstr "Meta dell'articolo"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:240
msgid "Post Elements"
msgstr "Elementi dell'articolo"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:207
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:753
msgid "Post Per Page"
msgstr "Articoli per pagina"

#: inc/customizer/configurations/comments/class-astra-comments-configs.php:118
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:736
msgid "Above Comments"
msgstr "Sopra ai commenti"

#: inc/customizer/configurations/comments/class-astra-comments-configs.php:117
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:735
msgid "Below Comments"
msgstr "Sotto ai commenti"

#: inc/customizer/configurations/comments/class-astra-comments-configs.php:114
msgid "Form Position"
msgstr "Posizione del modulo"

#: inc/customizer/configurations/comments/class-astra-comments-configs.php:89
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:763
msgid "Container Structure"
msgstr "Struttura del contenitore"

#: inc/customizer/configurations/comments/class-astra-comments-configs.php:76
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:713
msgid "Separated"
msgstr "Separato"

#: inc/customizer/configurations/comments/class-astra-comments-configs.php:75
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:712
msgid "Contained"
msgstr "Contenuto"

#: inc/customizer/configurations/comments/class-astra-comments-configs.php:72
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:708
msgid "Decide whether to isolate or integrate the module with the entry content area."
msgstr "Decidi se isolare o integrare il modulo con l'area di inserimento del contenuto."

#: inc/customizer/configurations/comments/class-astra-comments-configs.php:70
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:706
msgid "Section Placement"
msgstr "Posizionamento della sezione"

#: inc/core/class-astra-theme-options.php:189
msgid "Sorry, but we could not find anything related to your search terms. Please try again."
msgstr "Impossibile trovare termini corrispondenti ai termini cercati. Riprova."

#: inc/core/class-astra-theme-options.php:188
msgid "Here are the search results for your search."
msgstr "Ecco i risultati della ricerca."

#: inc/blog/blog-config.php:497
msgid "Read More »"
msgstr "Leggi tutto »"

#: inc/blog/blog-config.php:497
msgid "Read Post »"
msgstr "Leggi l'articolo »"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:218
msgid "Different Transparent Logo"
msgstr "Logo trasparente diverso"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:90
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:100
msgid "Note:"
msgstr "Nota:"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:100
msgid " Certain Checkout page options may not work smoothly on the block editor based Checkout page. For best results with these features, prefer using a shortcode-based Checkout page."
msgstr " Alcune opzioni della pagina di pagamento potrebbero non funzionare correttamente con l'editor di blocchi. Per ottenere i migliori risultati con queste funzionalità, prova a utilizzare una pagina di pagamento basata su shortcode."

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:90
msgid " Certain Cart page options may not work smoothly on the block editor based Cart page. For best results with these features, prefer using a shortcode based Cart page."
msgstr " Alcune opzioni della pagina Carrello potrebbero non funzionare correttamente sulla pagina Carrello con l'editor di blocchi. Per ottenere i migliori risultati con queste funzionalità, prova a utilizzare una pagina Carrello basata su shortcode."

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:98
#: admin/assets/theme-builder/build/index.js:61093
#: admin/assets/theme-builder/build/index.js:61223
msgid "404 Page"
msgstr "Pagina 404"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1629
msgid "Single Banner"
msgstr "Banner singolo"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:474
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:704
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:878
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:373
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:414
msgid "Image Size"
msgstr "Dimensione dell'immagine"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:385
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:638
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:308
msgid "2:1"
msgstr "2:1"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:352
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:608
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:279
msgid "Original"
msgstr "Originale"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:571
msgid "Below"
msgstr "Sotto"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:384
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:637
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:307
msgid "16:9"
msgstr "16:9"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:383
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:636
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:306
msgid "4:3"
msgstr "4:3"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:382
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:635
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:305
msgid "1:1"
msgstr "1:1"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:353
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:609
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:280
msgid "Predefined"
msgstr "Predefinito"

#: inc/extras.php:1200
msgid "Thumbnail"
msgstr "Miniatura"

#: inc/customizer/extend-customizer/class-astra-wp-customize-section.php:57
msgid "Customizing &#9656; %s"
msgstr "Stai personalizzando &#9656; %s"

#: inc/customizer/class-astra-customizer.php:748
msgid "Customizing ▸ %s"
msgstr "Stai personalizzando ▸ %s"

#: inc/core/common-functions.php:1417
msgctxt "placeholder"
msgid "Search..."
msgstr "Cerca..."

#: inc/core/class-theme-strings.php:44 inc/core/class-theme-strings.php:45
#: inc/core/class-theme-strings.php:46
msgid "Search..."
msgstr "Cerca..."

#: inc/core/class-astra-enqueue-scripts.php:441
msgid "No results found"
msgstr "Nessun risultato trovato"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:122
msgid "Enable Sticky Sidebar"
msgstr "Abilita la barra laterale fissa"

#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:104
msgid "Input Highlight"
msgstr "Evidenziazione dell'input"

#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:53
msgid "Global Highlight"
msgstr "Evidenziazione globale"

#: inc/customizer/configurations/builder/header/configs/search.php:98
msgid "Search Width"
msgstr "Larghezza della ricerca"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:374
msgid "'None' respects hierarchy; 'Below' positions image on top of the article."
msgstr "'Nessuno' rispetta la gerarchia; 'Sotto' posiziona l'immagine sopra l'articolo."

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:371
msgid "'None' respects hierarchy; 'Behind' positions the image under the article."
msgstr "'Nessuno' rispetta la gerarchia; 'Dietro' posiziona l'immagine sotto l'articolo."

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:552
msgid "Behind"
msgstr "Dietro"

#: inc/metabox/class-astra-meta-boxes.php:760
msgid "Surface Colors"
msgstr "Colori della superficie"

#: inc/metabox/class-astra-meta-boxes.php:630
msgid "Page Background"
msgstr "Sfondo della pagina"

#: inc/extras.php:1203
msgid "Large"
msgstr "Grande"

#: inc/extras.php:1202
msgid "Medium Large"
msgstr "Medio grande"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:708
msgid "You can specify Custom image sizes from the Single Post's 'Featured Image Size' option."
msgstr "Puoi specificare le dimensioni personalizzate dell'immagine dall'opzione \"Dimensione immagine in evidenza\" dell'articolo singolo."

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:589
msgid "Wide"
msgstr "Ampio"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:350
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:606
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:277
msgid "Image Ratio"
msgstr "Proporzioni dell'immagine"

#: inc/customizer/configurations/builder/header/configs/search.php:143
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:784
msgid "Search Within Post Types"
msgstr "Cerca all'interno del tipo di contenuto"

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:290
msgid "Heading Color"
msgstr "Colore del titolo"

#: inc/customizer/class-astra-customizer.php:1819
msgid "Other System Fonts"
msgstr "Altri font di sistema"

#. translators: 1: link open markup, 2: link close markup
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:435
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:693
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:362
msgid "Calculate a personalized image ratio using this %1$s online tool %2$s for your image dimensions."
msgstr "Calcola una proporzione personalizzata dell'ìimmagine utilizzando questo %1$s strumento online %2$s per le dimensioni della tua immagine."

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:1002
msgid "Archive Banner"
msgstr "Banner dell'archivio"

#: inc/extras.php:1204 inc/extras.php:1211
msgid "Full Size"
msgstr "Dimensione piena"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:587
msgid "Behind Positioned Image Width"
msgstr "Larghezza dell'immagine posizionata dietro"

#: inc/metabox/class-astra-meta-boxes.php:762
msgid "Enabling this option will override global > colors > surface color options"
msgstr "L'abilitazione di questa opzione sovrascriverà le opzioni Globale > Colori > Colore della superficie"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3942
msgid "Tumblr"
msgstr "Tumblr"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3255
msgid "TikTok"
msgstr "TikTok"

#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:156
msgid "Enable Brand Color On Hover"
msgstr "Abilita il colore del brand al passaggio del mouse"

#: admin/includes/class-astra-menu.php:686
#: admin/includes/class-astra-theme-builder-free.php:82
#: admin/includes/class-astra-theme-builder-free.php:151
#: admin/includes/class-astra-theme-builder-free.php:152
#: admin/assets/build/dashboard-app.js:15
#: admin/assets/theme-builder/build/index.js:60268
msgid "Site Builder"
msgstr "Builder del sito"

#: inc/core/class-theme-strings.php:29
msgid "Post Comment"
msgstr "Pubblica il commento"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:57
#: inc/customizer/configurations/builder/header/configs/site-identity.php:77
msgid "Use it with transparent images for optimal results."
msgstr "Per ottenere risultati ottimali, usalo con immagini trasparenti."

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:53
#: inc/customizer/configurations/builder/header/configs/site-identity.php:76
msgid "Logo Color"
msgstr "Colore del logo"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:63
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:63
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:84
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:139
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:62
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:43
#: inc/metabox/class-astra-elementor-editor-settings.php:360
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:152
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:130
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:691
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Sidebar will only apply when container layout is set to normal."
msgstr "La barra laterale si applica solo quando il layout del contenitore è impostato su normale."

#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:75
#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:80
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:75
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:80
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:83
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:101
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:156
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:75
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:79
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:73
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:81
#: inc/metabox/class-astra-meta-boxes.php:309
#: inc/metabox/class-astra-meta-boxes.php:340
#: inc/metabox/class-astra-meta-boxes.php:835
#: inc/metabox/class-astra-meta-boxes.php:847
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:131
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:187
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:112
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:167
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:676
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:725
msgid "Unboxed"
msgstr "Unboxed"

#: inc/customizer/configurations/comments/class-astra-comments-configs.php:92
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:57
#: inc/metabox/class-astra-meta-boxes.php:293
#: inc/metabox/class-astra-meta-boxes.php:823
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:45
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:45
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:654
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:766
msgid "Narrow"
msgstr "Stretto"

#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:49
msgid "Course/Lesson Sidebar Style"
msgstr "Stile della barra laterale per corsi/lezioni"

#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:48
msgid "Global Sidebar Style"
msgstr "Layout globale della barra laterale"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:77
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:77
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:31
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:76
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:71
#: inc/metabox/class-astra-elementor-editor-settings.php:369
#: inc/metabox/class-astra-meta-boxes.php:336
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:184
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:164
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:722
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Sidebar Style"
msgstr "Stile della barra laterale"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:72
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:71
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:79
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:71
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:79
#: inc/metabox/class-astra-elementor-editor-settings.php:322
#: inc/metabox/class-astra-meta-boxes.php:305
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:128
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:108
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:672
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Container Style"
msgstr "Stile del contenitore"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:70
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:72
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:80
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:72
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:78
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:136
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:109
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:673
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Container style will apply only when layout is set to either normal or narrow."
msgstr "Lo stile del contenitore si applica solo quando il layout è impostato su normale o stretto."

#. translators: 1: Astra, 2: Theme rating link
#: admin/includes/class-astra-menu.php:1091
msgid "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"
msgstr "Ti è piaciuto %1$s? Lasciaci una valutazione di %2$s. Apprezziamo davvero il tuo supporto!"

#: admin/includes/class-astra-menu.php:1087
msgid "Thank you for using"
msgstr "Grazie per l'utilizzo"

#: inc/metabox/class-astra-meta-boxes.php:438
#: inc/metabox/class-astra-meta-boxes.php:919
msgid "Disable Banner Area"
msgstr "Disattiva l'area del banner"

#: assets/svg/logo-svg-icons/icons-v6-0.php:711
#: inc/theme-update/astra-update-functions.php:426
msgid "Apple Pay"
msgstr "Apple Pay"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3571
#: inc/theme-update/astra-update-functions.php:414
msgid "Paypal"
msgstr "Paypal"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:980
#: inc/theme-update/astra-update-functions.php:366
msgid "Visa"
msgstr "Visa"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:988
#: inc/theme-update/astra-update-functions.php:378
msgid "Mastercard"
msgstr "Mastercard"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:996
#: inc/theme-update/astra-update-functions.php:390
msgid "Amex"
msgstr "Amex"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:327
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:924
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:452
msgid "Format"
msgstr "Formato"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:305
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:903
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:435
msgid "Last Updated"
msgstr "Ultimo aggiornamento"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:304
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:902
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:434
msgid "Published"
msgstr "Pubblicato"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:302
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:900
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:432
msgid "Type"
msgstr "Tipo"

#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:38
msgid "Site Accessibility"
msgstr "Accessibilità del sito"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:265
msgid "Accessibility"
msgstr "Accessibilità"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:1004
#: inc/theme-update/astra-update-functions.php:402
msgid "Discover"
msgstr "Scopri"

#: inc/customizer/configurations/typography/class-astra-single-typo-configs.php:70
msgid "Social sharing options"
msgstr "Opzioni di condivisione social"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:41
msgid "Redirect To Checkout Page"
msgstr "Reindirizza alla pagina del pagamento"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:40
msgid "Redirect To Cart Page"
msgstr "Reindirizza alla pagina del carrello"

#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:61
#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:116
msgid "Solid"
msgstr "Tinta unita"

#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:57
#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:112
msgid "Dotted"
msgstr "Punteggiato"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:375
msgid "Please publish the changes and see result on the frontend.<br />[Slide in cart requires Cart added inside Header Builder]"
msgstr "Pubblica le modifiche per vedere il risultato nel frontend.<br />[Il carrello scorrevole richiede l'aggiunta del carrello all'interno del builder dell'header]."

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:324
msgid "Add To Cart Action"
msgstr "Aggiungi al carrello azione"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:340
msgid "Read Time"
msgstr "Tempo di lettura"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:46
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:324
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:439
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:125
msgid "Layout 2"
msgstr "Layout 2"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:42
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:320
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:435
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:121
msgid "Layout 1"
msgstr "Layout 1"

#: inc/compatibility/starter-content/home.php:236
msgctxt "Theme starter content"
msgid "Home"
msgstr "Home"

#: inc/compatibility/class-astra-starter-content.php:251
msgctxt "Theme starter content"
msgid "Logo"
msgstr "Logo"

#: inc/compatibility/surecart/customizer/class-astra-customizer-register-surecart-section.php:36
msgid "SureCart"
msgstr "SureCart"

#: admin/includes/class-astra-menu.php:226
#: admin/assets/theme-builder/build/index.js:60264
msgid "Dashboard"
msgstr "Bacheca"

#: inc/modules/posts-structures/customizer/class-astra-posts-structures-configs.php:113
#: admin/assets/theme-builder/build/index.js:61030
#: admin/assets/theme-builder/build/index.js:61216
msgid "Archive"
msgstr "Archivio"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:243
msgid "Select"
msgstr "Seleziona"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:971
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:748
msgid "Overlay Color"
msgstr "Colore Overlay"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:595
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:348
msgid "Container Background"
msgstr "Sfondo del contenitore"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:574
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1054
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:331
msgid "Inner Elements Spacing"
msgstr "Spaziatura degli elementi interni"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:552
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1032
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:313
msgid "Banner Min Height"
msgstr "Altezza minima del banner"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:489
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:969
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:258
msgid "Horizontal Alignment"
msgstr "Allineamento orizzontale"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:781
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1585
msgid "Taxonomy"
msgstr "Tassonomia"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:316
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:431
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:117
msgid "Banner Layout"
msgstr "Layout del banner"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:428
msgid "Blog Title"
msgstr "Titolo del blog"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:262
msgid "Description"
msgstr "Descrizione"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:229
msgid "Featured"
msgstr "In evidenza"

#: inc/compatibility/class-astra-starter-content.php:242
msgid "Contact"
msgstr "Contatti"

#: inc/compatibility/class-astra-starter-content.php:237
msgid "Why Us"
msgstr "Perché noi"

#: inc/compatibility/class-astra-starter-content.php:232
msgid "Reviews"
msgstr "Recensioni"

#: inc/compatibility/class-astra-starter-content.php:227
msgid "About"
msgstr "Chi siamo"

#: inc/compatibility/class-astra-starter-content.php:222
msgid "Services"
msgstr "Servizi"

#: admin/includes/class-astra-menu.php:398
msgid "Blog Options"
msgstr "Opzioni del blog"

#: admin/includes/class-astra-menu.php:378
msgid "Header Settings"
msgstr "Impostazioni dell'header"

#: admin/includes/class-astra-menu.php:337
#: admin/includes/class-astra-theme-builder-free.php:104
msgid "Activated"
msgstr "Attivato"

#: admin/includes/class-astra-menu.php:335
#: admin/includes/class-astra-theme-builder-free.php:102
msgid "Installed"
msgstr "Installato"

#: admin/includes/class-astra-menu.php:334
#: admin/includes/class-astra-theme-builder-free.php:101
msgid "Installing"
msgstr "Installazione in corso"

#: admin/includes/class-astra-menu.php:563
#: admin/includes/class-astra-menu.php:576
#: admin/includes/class-astra-menu.php:589
#: admin/includes/class-astra-menu.php:602
#: admin/includes/class-astra-menu.php:615
#: admin/includes/class-astra-menu.php:628
#: admin/includes/class-astra-menu.php:641
#: admin/includes/class-astra-menu.php:654
#: admin/includes/class-astra-menu.php:667
#: admin/includes/class-astra-menu.php:680
#: admin/includes/class-astra-menu.php:695
#: admin/includes/class-astra-menu.php:710
#: admin/includes/class-astra-menu.php:724
#: admin/includes/class-astra-menu.php:738
#: admin/includes/class-astra-menu.php:753
#: admin/includes/class-astra-menu.php:767
#: admin/includes/class-astra-menu.php:780
#: admin/assets/build/dashboard-app.js:6
msgid "Documentation"
msgstr "Documentazione"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:461
msgid "Blog Description"
msgstr "Descrizione del blog"

#: template-parts/scroll-to-top.php:24 admin/assets/build/dashboard-app.js:10
msgid "Scroll to Top"
msgstr "Torna in alto"

#: inc/modules/posts-structures/customizer/class-astra-posts-structures-configs.php:122
#: admin/assets/theme-builder/build/index.js:60977
#: admin/assets/theme-builder/build/index.js:61209
msgid "Single"
msgstr "Singolo"

#: inc/modules/posts-structures/customizer/class-astra-posts-structures-configs.php:73
msgid "Custom Post Types"
msgstr "Tipi di contenuto personalizzato"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:736
msgid "Use as Background"
msgstr "Imposta come sfondo"

#. translators: 1: post type
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:476
msgid "Note: This description appear on %1$s archive for banner Layout 2."
msgstr "Nota: questa descrizione appare nell'archivio %1$s per il banner Layout 2."

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:461
msgid "Archive Description"
msgstr "Descrizione dell'archivio"

#. translators: 1: post type
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:443
msgid "Note: This title appear on %1$s archive for banner Layout 2."
msgstr "Nota: questo titolo compare nell'archivio %1$s per il banner Layout 2."

#: inc/customizer/configurations/layout/class-astra-site-layout-configs.php:63
msgid "Narrow Container Width"
msgstr "Larghezza del contenitore stretto"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:351
msgid "Site Tagline Visibility"
msgstr "Visibilità del motto del sito"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:330
msgid "Site Title Visibility"
msgstr "Visibilità del titolo del sito"

#: inc/customizer/configurations/global-misc/class-astra-global-misc-configs.php:38
msgid "Enable Smooth Scroll to ID"
msgstr "Abilita lo scorrimento fluido fino all'ID"

#: inc/customizer/configurations/builder/header/configs/menu.php:338
msgid "Stack on Responsive"
msgstr "Disponi in colonne nel Responsive"

#: inc/customizer/configurations/builder/header/configs/account.php:525
msgid "Text Options"
msgstr "Opzioni del testo"

#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:435
#: inc/customizer/configurations/builder/header/configs/menu.php:607
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:396
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:175
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:278
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:802
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:887
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1268
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1353
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1458
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:521
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:589
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1140
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1225
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1310
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1397
msgid "Font Extras"
msgstr "Font aggiuntivi"

#: inc/customizer/class-astra-customizer.php:277
msgid "Local font files not present."
msgstr "File dei font locali non presenti."

#: inc/customizer/class-astra-customizer.php:271
msgid "Failed to Flush, try again later."
msgstr "Non è stato possibile eseguire la pulizia, riprova più tardi."

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:289
msgid "Note: To get design settings in action make sure to enable Scroll to Top."
msgstr "Nota: per visualizzare le impostazioni del design in azione, assicurati di attivare Torna in alto."

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:60
msgid "Display On"
msgstr "Visualizza su"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:44
msgid "Enable Scroll to Top"
msgstr "Abilita pulsante Torna in alto"

#: admin/includes/class-astra-menu.php:835
#: admin/includes/class-astra-menu.php:957
msgid "Free WordPress Page Builder."
msgstr "Page builder gratuito per WordPress."

#: admin/includes/class-astra-menu.php:994
msgid "Ultimate Video Player For WordPress."
msgstr "Ultimate Video Player per WordPress."

#: admin/includes/class-astra-menu.php:969
msgid "Automate your WordPress setup."
msgstr "Automatizza la configurazione di WordPress."

#: admin/includes/class-astra-menu.php:882
msgid "Recover lost revenue automatically."
msgstr "Recupera automaticamente le entrate perse."

#: admin/includes/class-astra-menu.php:802
msgid "#1 Sales Funnel WordPress Builder."
msgstr "Il Sales Funnel Builder #1 per WordPress."

#: admin/includes/class-astra-api-init.php:142
msgid "Sorry, you cannot list resources."
msgstr "Impossibile elencare le risorse."

#: admin/includes/class-astra-admin-ajax.php:236
#: admin/includes/class-astra-admin-ajax.php:270
msgid "Successfully saved data!"
msgstr "Dati salvati con successo!"

#: admin/includes/class-astra-admin-ajax.php:70
msgid "Sorry, something went wrong."
msgstr "Qualcosa è andato storto."

#: admin/includes/class-astra-admin-ajax.php:69
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:238
msgid "Nonce validation failed"
msgstr "Convalida nonce non riuscita"

#: admin/includes/class-astra-admin-ajax.php:68
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:225
msgid "Sorry, you are not allowed to do this operation."
msgstr "Non hai i permessi per effettuare questa operazione."

#: admin/includes/class-astra-admin-ajax.php:71
msgid "No post data found!"
msgstr "Non sono stati trovati dati per l'articolo!"

#: inc/compatibility/class-astra-starter-content.php:261
#: inc/compatibility/class-astra-starter-content.php:265
#: inc/customizer/class-astra-customizer.php:1621
#: inc/customizer/class-astra-customizer.php:1674
msgid "Primary"
msgstr "Principale"

#: inc/metabox/class-astra-elementor-editor-settings.php:534
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Unlock your full design potential and build a website to be proud of with Astra Pro."
msgstr "Libera il tuo potenziale di progettazione e realizza un sito web di cui andare fiero con Astra Pro."

#: inc/customizer/configurations/typography/class-astra-single-typo-configs.php:61
msgid "Single post read time"
msgstr "Tempo di lettura dell'articolo singolo"

#: inc/customizer/configurations/typography/class-astra-single-typo-configs.php:55
msgid "Single post navigation control"
msgstr "Controllo della navigazione per l'articolo singolo"

#: inc/customizer/configurations/typography/class-astra-single-typo-configs.php:52
msgid "Auto load previous posts"
msgstr "Carica automaticamente gli articoli precedenti"

#: inc/customizer/configurations/typography/class-astra-archive-typo-configs.php:81
msgid "Take your blog to the next level with powerful design features."
msgstr "Porta il tuo blog a un livello superiore con potenti funzionalità di progettazione"

#: inc/customizer/configurations/typography/class-astra-archive-typo-configs.php:71
msgid "Archive read time"
msgstr "Tempo di lettura dell'archivio"

#: inc/customizer/configurations/typography/class-astra-single-typo-configs.php:64
msgid "Extended typography options"
msgstr "Opzioni di tipografia avanzate"

#: inc/customizer/configurations/typography/class-astra-single-typo-configs.php:67
msgid "Extended spacing options"
msgstr "Opzioni di spaziatura avanzate"

#: inc/customizer/configurations/typography/class-astra-single-typo-configs.php:58
msgid "Custom featured images size"
msgstr "Dimensioni personalizzate delle immagini in evidenza"

#: inc/customizer/configurations/typography/class-astra-archive-typo-configs.php:53
msgid "Grid, Masonry layout"
msgstr "Griglia, layout masonry"

#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:172
msgid "Use containers to their maximum potential with Astra Pro"
msgstr "Usa i contenitori al massimo delle loro potenzialità con Astra Pro"

#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:166
msgid "Container spacings"
msgstr "Spaziatura del contenitore"

#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:163
msgid "Fluid layout"
msgstr "Layout fluido"

#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:157
msgid "Full Width layout"
msgstr "Layout a larghezza piena"

#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:160
msgid "Padded layout"
msgstr "Layout rientrato"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:156
msgid "Make sidebars work harder to engage with Astra Pro"
msgstr "Usa le barre laterali Astra Pro per favorire l'interazione"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:150
msgid "Widget content typography"
msgstr "Tipografia del contenuto del widget"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:147
msgid "Widget title typography"
msgstr "Tipografia del titolo del widget"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:144
msgid "Widget color options"
msgstr "Opzioni del colore del widget"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:141
msgid "Sidebar color options"
msgstr "Opzioni del colore della barra laterale"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:138
msgid "Sidebar spacing"
msgstr "Spaziatura della barra laterale"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:198
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:219
msgid "Slide-In"
msgstr "Scorrimento interno"

#: inc/customizer/configurations/builder/header/configs/header-builder.php:722
msgid "Make an instant connection with amazing site headers"
msgstr "Crea una connessione immediata con header straordinari"

#: inc/customizer/configurations/builder/header/configs/header-builder.php:709
msgid "Toggle Button element"
msgstr "Pulsante attiva/disattiva elemento"

#: inc/customizer/configurations/builder/header/configs/header-builder.php:700
msgid "Sticky header"
msgstr "Header fisso"

#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:318
msgid "Finish your page on a high with amazing website footers"
msgstr "Completa la tua pagina al meglio con fantastici footer"

#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:311
#: inc/customizer/configurations/builder/header/configs/header-builder.php:715
msgid "More design options"
msgstr "Opzioni di progettazione"

#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:308
msgid "Increased element count"
msgstr "Aumenta il numero degli elementi"

#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:305
msgid "Clone, Delete element options"
msgstr "Opzioni Clona, Elimina elemento"

#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:302
msgid "Language Switcher element"
msgstr "Menu cambio lingua"

#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:299
#: inc/customizer/configurations/builder/header/configs/header-builder.php:703
msgid "Divider element"
msgstr "Elemento divisore"

#: inc/core/class-astra-admin-settings.php:286
msgid "Use every tool at your disposal to optimize your online store for conversion. All the advantages you need to make more profit!"
msgstr "Utilizza ogni strumento a tua disposizione per ottimizzare il tuo negozio online per la conversione. Tutti i vantaggi di cui hai bisogno per aumentare il tuo profitto!"

#: inc/core/class-astra-admin-settings.php:285
msgid "Astra Works Seamlessly with WooCommerce!"
msgstr "Astra funziona perfettamente con WooCommerce!"

#: inc/core/class-astra-admin-settings.php:289
#: inc/metabox/class-astra-elementor-editor-settings.php:536
#: admin/assets/build/dashboard-app.js:1 admin/assets/build/dashboard-app.js:15
#: admin/assets/theme-builder/build/index.js:1483
#: admin/assets/utils/helpers.js:107
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Upgrade Now"
msgstr "Aggiorna subito"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4260
#: inc/core/class-astra-admin-settings.php:122
msgid "Unlock"
msgstr "Sblocca"

#: inc/core/class-astra-admin-settings.php:121
msgid "Unlock with Astra Pro"
msgstr "Sblocca con Astra Pro"

#: inc/core/class-astra-admin-settings.php:118
msgid "Astra Menu Settings"
msgstr "Impostazioni del menu Astra"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:643
msgid "Extra conversion options for store product pages means extra profit!"
msgstr "Opzioni di conversione extra per le pagine dei prodotti del negozio significa profitto extra!"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:628
msgid "Extras option for product structure"
msgstr "Opzione extra per la struttura del prodotto"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:625
msgid "Related, Upsell product controls"
msgstr "Controlli dei prodotti correlati e più venduti"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:622
msgid "Product description layouts"
msgstr "Layout della descrizione del prodotto"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:619
msgid "Sticky product summary"
msgstr "Riassunto del prodotto fisso"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:616
msgid "More product galleries"
msgstr "Altre gallerie di prodotti"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:517
msgid "Add payment title"
msgstr "Aggiungi titolo pagamento"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-misc-layout-configs.php:95
msgid "Access extra conversion tools to make more profit from your eCommerce store"
msgstr "Accedi a strumenti di conversione aggiuntivi per ottenere maggiori profitti dal tuo negozio di eCommerce"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-misc-layout-configs.php:89
msgid "Modern thank-you page design"
msgstr "Design moderno della pagina dei ringraziamenti"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-misc-layout-configs.php:86
msgid "Downloads, Orders grid view"
msgstr "Download, visualizzazione griglia ordini"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-misc-layout-configs.php:83
msgid "Modern my-account page"
msgstr "Pagina moderna per il mio account"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-misc-layout-configs.php:80
msgid "Quantity updater designs"
msgstr "Design per l'aggiornamento della quantità"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-misc-layout-configs.php:77
msgid "Ecommerce steps navigation"
msgstr "Navigazione passaggi eCommerce"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-misc-layout-configs.php:74
msgid "Sale badge modifications"
msgstr "Modifiche badge offerta"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-misc-layout-configs.php:71
msgid "Modern input style"
msgstr "Stile inserimento moderno"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:420
msgid "Optimize your WooCommerce store for maximum profit with enhanced features"
msgstr "Ottimizza il tuo negozio WooCommerce per il massimo profitto con le funzionalità avanzate"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:414
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:637
msgid "More design controls"
msgstr "Più controlli di progettazione"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:411
msgid "Box shadow design options"
msgstr "Opzioni di progettazione dell'ombreggiatura del box"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:408
msgid "More spacing options"
msgstr "Altre opzioni di spaziatura"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:405
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:634
msgid "More color options"
msgstr "Maggiore scelta a livello di colore"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:402
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:631
msgid "More typography options"
msgstr "Altre opzioni di tipografia"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:399
msgid "Shop pagination"
msgstr "Paginazione del negozio"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:396
msgid "Products quick view"
msgstr "Visualizzazione rapida dei prodotti"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:393
msgid "Offcanvas product filters"
msgstr "Filtri prodotti offcanvas"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:390
msgid "Shop toolbar structure"
msgstr "Struttura della barra degli strumenti del negozio"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:387
msgid "More shop design layouts"
msgstr "Altri layout di progettazione del negozio"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:170
msgid "Encourage last-minute purchases with extra conversion options at checkout"
msgstr "Incoraggia gli acquisti 'last-minute' con opzioni di conversione extra al momento del pagamento"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:164
msgid "Summary, Payment background"
msgstr "Riepilogo, informazioni di base sui pagamenti"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:161
msgid "Text form options"
msgstr "Opzioni del modulo di testo"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:158
msgid "Persistent checkout form data"
msgstr "Dati del modulo di pagamento persistenti"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:155
msgid "Distraction free checkout"
msgstr "Pagamento senza distrazioni"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:152
msgid "Order note, Coupon field control"
msgstr "Nota ordine, controllo campo coupon"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:149
msgid "Two-step checkout"
msgstr "Pagamento in due passaggi"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:146
msgid "Sticky order review"
msgstr "Riepilogo fisso dell'ordine"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:143
msgid "Modern order received layout"
msgstr "Layout moderno per l'ordine ricevuto"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:140
msgid "Multi-column layouts"
msgstr "Layout multicolonne"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:137
msgid "Modern layout"
msgstr "Layout moderno"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:125
msgid "Convert more, earn more with extensive cart conversion features"
msgstr "Converti di più, guadagna di più con le ampie funzionalità di conversione del carrello"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:119
msgid "Real-time quantity updater"
msgstr "Aggiornamento delle quantità in tempo reale"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:116
msgid "Sticky cart totals"
msgstr "Totali carrello fisso"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:113
msgid "Modern cart layout"
msgstr "Layout moderno del carrello"

#: inc/customizer/configurations/typography/class-astra-single-typo-configs.php:77
msgid "Extensive range of tools to help blog pages stand out."
msgstr "Un'ampia gamma di strumenti per far risaltare le pagine del blog."

#: assets/svg/logo-svg-icons/icons-v6-2.php:21
msgid "IMDB"
msgstr "IMDB"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6284
msgid "Yelp"
msgstr "Yelp"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2804
msgid "Telegram"
msgstr "Telegram"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6127
msgid "Xing"
msgstr "Xing"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5224
msgid "VK"
msgstr "VK"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2023
msgid "Behance"
msgstr "Behance"

#: assets/svg/logo-svg-icons/icons-v6-1.php:952
#: assets/svg/logo-svg-icons/icons-v6-1.php:2486
#: assets/svg/logo-svg-icons/icons-v6-1.php:4179
#: assets/svg/logo-svg-icons/icons-v6-1.php:4224
#: assets/svg/logo-svg-icons/icons-v6-1.php:4527
#: assets/svg/logo-svg-icons/icons-v6-1.php:4830
#: assets/svg/logo-svg-icons/icons-v6-1.php:6051
#: assets/svg/logo-svg-icons/icons-v6-2.php:130
#: assets/svg/logo-svg-icons/icons-v6-2.php:473
#: assets/svg/logo-svg-icons/icons-v6-2.php:901
#: assets/svg/logo-svg-icons/icons-v6-2.php:3105
#: assets/svg/logo-svg-icons/icons-v6-2.php:3705
#: assets/svg/logo-svg-icons/icons-v6-2.php:4662
#: assets/svg/logo-svg-icons/icons-v6-2.php:4729
#: assets/svg/logo-svg-icons/icons-v6-2.php:5698
#: assets/svg/logo-svg-icons/icons-v6-3.php:1091
#: assets/svg/logo-svg-icons/icons-v6-3.php:1889
#: assets/svg/logo-svg-icons/icons-v6-3.php:2046
#: assets/svg/logo-svg-icons/icons-v6-3.php:3957
#: assets/svg/logo-svg-icons/icons-v6-3.php:4065
#: assets/svg/logo-svg-icons/icons-v6-3.php:4985
#: assets/svg/logo-svg-icons/icons-v6-3.php:5118
#: assets/svg/logo-svg-icons/icons-v6-3.php:5630
#: assets/svg/logo-svg-icons/icons-v6-3.php:6142
#: assets/svg/logo-svg-icons/icons-v6-3.php:6356
msgid "Dribbble"
msgstr "Dribbble"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4190
msgid "GitHub"
msgstr "GitHub"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3528
msgid "Patreon"
msgstr "Patreon"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1943 inc/extras.php:1201
msgid "Medium"
msgstr "Medium"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4699
msgid "Pinterest"
msgstr "Pinterest"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6338
msgid "YouTube"
msgstr "YouTube"

#: assets/svg/logo-svg-icons/icons-v6-2.php:115
msgid "Instagram"
msgstr "Instagram"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4035
#: assets/svg/logo-svg-icons/icons-v6-3.php:4050
msgid "Twitter"
msgstr "Twitter"

#: assets/svg/logo-svg-icons/icons-v6-1.php:2441
msgid "Facebook"
msgstr "Facebook"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:392
msgid "Count Color"
msgstr "Colore contatore"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:157
msgid "Hide cart total label if cart is empty"
msgstr "Se il carrello è veuoto, nascondi l'etichetta del totale del carrello"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:156
msgid "Hide Cart Total Label"
msgstr "Nascondi l'etichetta del totale del carrello"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:83
msgid "Select Cart Icon"
msgstr "Seleziona l'icona del carrello"

#: inc/core/class-astra-enqueue-scripts.php:391
msgid "Minus Quantity"
msgstr "Meno Quantità"

#: inc/core/class-astra-enqueue-scripts.php:390
msgid "Plus Quantity"
msgstr "Più quantità"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:596
msgid "Note: To get design settings make sure to enable sticky add to cart."
msgstr "Nota: per ottenere le impostazioni del design, assicurati di abilitare 'aggiungi al carrello' fisso"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:495
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:516
msgid "Payment Title"
msgstr "Titolo del pagamento"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:475
msgid "Grayscale"
msgstr "Scala di grigi"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:472
msgid "Choose Icon Colors"
msgstr "Scegli i colori delle icone"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:289
msgid "Sticky Add To Cart Colors"
msgstr "Colori dell'icona fissa 'aggiungi al carrello'"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:264
msgid "Sticky Placement "
msgstr "Posizionamento fisso"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:249
msgid "Enable Sticky Add to Cart"
msgstr "Abilita Aggiungi al carrello fisso"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:233
msgid "Sticky Add To Cart"
msgstr "Aggiungi al carrello fisso"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:72
msgid "Payments"
msgstr "Pagamenti"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:47
msgid "Extras"
msgstr "Extra"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-misc-layout-configs.php:41
msgid "Adds plus and minus buttons besides product quantity"
msgstr "Aggiunge i pulsanti più e meno accanto alla quantità del prodotto"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-misc-layout-configs.php:40
msgid "Enable Quantity Plus and Minus"
msgstr "Abilita più e meno Quantità"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:211
msgid "Shop Card Design"
msgstr "Design della scheda del negozio"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:136
msgid "Horizontal Content Alignment"
msgstr "Allineamento orizzontale dei contenuti"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:115
msgid "Shop Card Styling"
msgstr "Stile della scheda del negozio"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:78
msgid "Design 3"
msgstr "Design 3"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:74
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:89
msgid "Design 2"
msgstr "Design 2"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:70
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:85
msgid "Design 1"
msgstr "Design 1"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:970
msgid "Guaranteed Safe Checkout"
msgstr "Pagamento sicuro garantito"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:300
msgid "Sale!"
msgstr "In vendita!"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1129
msgid "LinkedIn"
msgstr "LinkedIn"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5063
msgid "Video"
msgstr "Video"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:332
msgid "Slide In Cart"
msgstr "Carrello scorrevole"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:274
msgid "Slide in Cart Width"
msgstr "Larghezza carrello scorrevole"

#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:66
msgid "Menu 2"
msgstr "Menu 2"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:3512
msgid "Continue Shopping"
msgstr "Continua gli acquisti"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:3508
msgid "No products in the cart."
msgstr "Nessun prodotto nel carrello."

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:376
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:200
msgid "Heading Font"
msgstr "Font intestazione"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:40
msgid "Base Font"
msgstr "Font di base"

#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:94
msgid "Surface Color"
msgstr "Colore superficie"

#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:123
msgid "Outside"
msgstr "Esterno"

#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:106
msgid "Post Spacing"
msgstr "Spaziatura articolo"

#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:159
msgid "Borders"
msgstr "Bordi"

#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:146
msgid "Body Text"
msgstr "Contenuto"

#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:104
msgid "Links"
msgstr "Link"

#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:93
msgid "Accent"
msgstr "Enfasi"

#: inc/customizer/configurations/builder/header/configs/account.php:399
msgid "Avatar Width"
msgstr "Larghezza avatar"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:517
msgid "Cart Label Position"
msgstr "Posizione etichetta del carrello"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:337
msgid "Cart Color"
msgstr "Colore del carrello"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:301
msgid "Cart Icon"
msgstr "Icona del carrello"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:193
msgid "Cart Click Action"
msgstr "Azione quando fai clic sul carrello"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:172
msgid "Cart Click"
msgstr "Fai clic sul carrello"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:140
msgid "Display Cart Count"
msgstr "Visualizza il totale del carrello"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:129
msgid "Note: The Cart Label on the header will be displayed by using shortcodes. Type any custom string in it or click on the plus icon above to add your desired shortcode."
msgstr "Nota: L'etichetta del carrello nell'header sarà visualizzata tramite shortcode. Inserisci una stringa personalizzata o fai clic sul segno più per aggiungere lo shortcode desiderato."

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:113
msgid "Total + Currency symbol"
msgstr "Totale + simbolo valuta"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:112
msgid "Currency Symbol"
msgstr "Simbolo valuta"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:111
msgid "Total amount"
msgstr "Importo totale"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:110
msgid "Currency Name"
msgstr "Nome Valuta"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:107
msgid "Cart Label"
msgstr "Etichetta del carrello"

#: inc/core/builder/class-astra-builder-helper.php:765
#: inc/core/builder/class-astra-builder-helper.php:935
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:70
msgid "Menu 3"
msgstr "Menu 3"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:534
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:557
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:576
msgid "Button Width"
msgstr "Larghezza del pulsante"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:213
msgid "Shipping Text"
msgstr "Testo spedizione"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:177
msgid "Changes single product variation layout to be displayed inline or stacked."
msgstr "Modifica il layout di una singola variazione del prodotto per visualizzarla in linea o impilata."

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:176
msgid "Product Variation Layout"
msgstr "Layout variazione prodotto"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:163
msgid "Adds shipping text next to the product price."
msgstr "Aggiunge il testo per la spedizione accanto al prezzo del prodotto."

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:162
msgid "Enable Shipping Text"
msgstr "Abilita il testo per la spedizione"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:150
msgid "Enable Breadcrumb"
msgstr "Abilita i breadcrumb"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:31
msgid "Product Options"
msgstr "Opzioni prodotto"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:31
msgid "Product Structure Options"
msgstr "Opzioni struttura prodotto"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:195
msgid "Shop Layout"
msgstr "Layout del negozio"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:155
msgid "Shop Card Structure"
msgstr "Struttura della scheda del negozio"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:40
msgid "Add custom text for cart button"
msgstr "Aggiungi testo personalizzato sul pulsante del carrello"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:39
msgid "Change Cart Button Text"
msgstr "Modifica il testo sul pulsante del carrello"

#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:93
msgid "Hang Over Top"
msgstr "Appeso in alto"

#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:91
msgid "Notice Position"
msgstr "Posizione degli avvisi"

#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:93
msgid "Shop Display Options"
msgstr "Visualizza le opzioni del negozio"

#: inc/compatibility/woocommerce/customizer/class-astra-customizer-register-woo-section.php:67
#: inc/customizer/class-astra-customizer-register-sections-panels.php:416
msgid "Misc"
msgstr "Varie"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:930
msgid "Proceed to checkout"
msgstr "Procedi al pagamento"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:925
msgid "& Free Shipping"
msgstr "e Spedizione gratuita"

#: inc/compatibility/lifterlms/customizer/sections/layout/class-astra-lifter-general-configs.php:44
msgid "Columns"
msgstr "Colonne"

#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:47
msgid "Course/Lesson Sidebar Layout"
msgstr "Layout della barra laterale per corsi/lezioni"

#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:46
msgid "Global Sidebar Layout"
msgstr "Layout globale della barra laterale"

#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:47
msgid "Heading (H1-H6)"
msgstr "Intestazioni (H1 - H6)"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:256
#: inc/compatibility/lifterlms/customizer/sections/layout/class-astra-lifter-general-configs.php:120
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:1007
msgid "View Astra Pro Features"
msgstr "Visualizza le funzionalità di Astra Pro"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:396
msgid "Custom separator"
msgstr "Separatore personalizzato"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:43
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:43
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:51
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:43
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:49
#: inc/metabox/class-astra-elementor-editor-settings.php:309
#: inc/metabox/class-astra-meta-boxes.php:287
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:112
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:93
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:643
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Container Layout"
msgstr "Layout del contenitore"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:131
msgid "Display Settings"
msgstr "Impostazioni di visualizzazione"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:113
msgid "Note: To get design settings in action make sure to select Header Position other than None."
msgstr "Nota: per vedere le impostazioni del design in azione, assicurati di selezionare una posizione dell'header che non sia \"Nessuno\"."

#: inc/core/common-functions.php:1184
msgid "Author name: "
msgstr "Nome dell'autore: "

#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:29
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:36
msgid "Compact"
msgstr "Compatto"

#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:30
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:37
msgid "Comfort"
msgstr "Comfort"

#: inc/modules/related-posts/class-astra-related-posts-markup.php:234
msgid "Related post link"
msgstr "Link all'articolo correlato"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:406
msgid "H6 Font"
msgstr "Font H6"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:391
msgid "H5 Font"
msgstr "Font H5"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:377
msgid "H4 Font"
msgstr "Font H4"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:361
msgid "H3 Font"
msgstr "Font H3"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:346
msgid "H2 Font"
msgstr "Font H2"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:331
msgid "H1 Font"
msgstr "Font H1"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:186
msgid "Headings Font"
msgstr "Font dell'intestazione"

#: theme.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Molto grande"

#: theme.json
msgctxt "Font size name"
msgid "Large"
msgstr "Grande"

#: theme.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Medio"

#: theme.json
msgctxt "Font size name"
msgid "Small"
msgstr "Piccolo"

#: inc/customizer/class-astra-font-families.php:143
msgid "Ultra-Bold 900 Italic"
msgstr "Corsivo ultra grassetto 900"

#: inc/customizer/class-astra-font-families.php:142
msgid "Extra-Bold 800 Italic"
msgstr "Corsivo grassetto accentuato 800"

#: inc/customizer/class-astra-font-families.php:141
msgid "Bold 700 Italic"
msgstr "Corsivo grassetto 700"

#: inc/customizer/class-astra-font-families.php:140
msgid "Semi-Bold 600 Italic"
msgstr "Corsivo semigrassetto 600"

#: inc/customizer/class-astra-font-families.php:139
msgid "Medium 500 Italic"
msgstr "Corsivo medio 500"

#: inc/customizer/class-astra-font-families.php:137
#: inc/customizer/class-astra-font-families.php:138
msgid "Regular 400 Italic"
msgstr "Corsivo normale 400"

#: inc/customizer/class-astra-font-families.php:136
msgid "Light 300 Italic"
msgstr "Corsivo chiaro 300"

#: inc/customizer/class-astra-font-families.php:135
msgid "Extra Light 200 Italic"
msgstr "Corsivo chiarissimo 200"

#: inc/customizer/class-astra-font-families.php:134
msgid "Thin 100 Italic"
msgstr "Corsivo sottile 100"

#: inc/customizer/class-astra-font-families.php:128
msgid "Regular 400"
msgstr "Normale 400"

#: inc/customizer/class-astra-font-families.php:126
msgid "Extra Light 200"
msgstr "Chiarissimo 200"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:405
msgid "Block Editor"
msgstr "Editor a blocchi"

#: inc/metabox/class-astra-elementor-editor-settings.php:392
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Disable Elements"
msgstr "Disabilita elementi"

#: inc/metabox/class-astra-meta-boxes.php:976
msgid "Stick Primary Header"
msgstr "Header principale fisso"

#: inc/metabox/class-astra-meta-boxes.php:905
msgid "Disable Header"
msgstr "Disabilita header"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:57
msgid "Body Font"
msgstr "Font del contenuto"

#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:35
#: admin/assets/build/dashboard-app.js:3
msgid "Legacy"
msgstr "Legacy"

#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:94
msgid "Global padding setting for WordPress Group, Column, Cover blocks, it can be overridden by respective block's Dimension setting."
msgstr "Impostazione globale della spaziatura interna dei blocchi Gruppo, Colonna e Copertina di WordPress; L'impostazione globale può essere sovrascritta nella sezione dimensioni del relativo blocco."

#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:53
msgid "Core Blocks Spacing"
msgstr "Spaziatura interna dei blocchi principali"

#: inc/metabox/class-astra-elementor-editor-settings.php:506
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Make sure to update your post for changes to take effect."
msgstr "Assicurati di aggiornare il tuo articolo affinché le modifiche abbiano effetto."

#: inc/metabox/class-astra-elementor-editor-settings.php:425
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Header Rows"
msgstr "Righe dell'header"

#: inc/metabox/class-astra-elementor-editor-settings.php:414
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Advanced Settings"
msgstr "Impostazioni avanzate"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:424
msgid "Unicode"
msgstr "Unicode"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:392
msgid "Type 3"
msgstr "Tipo 3"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:388
msgid "Type 2"
msgstr "Tipo 2"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:384
msgid "Type 1"
msgstr "Tipo 1"

#: inc/builder/controllers/class-astra-builder-ui-controller.php:314
msgid "Close Cart Drawer"
msgstr "Chiudi il cassetto del carrello"

#: inc/metabox/class-astra-meta-boxes.php:1054
msgid "No Page Headers Found"
msgstr "Nessuna header di pagina trovato"

#: inc/metabox/class-astra-meta-boxes.php:655
msgid "Page Header"
msgstr "Header della pagina"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:464
msgid "Text / Icon"
msgstr "Testo/Icona"

#: inc/metabox/class-astra-meta-boxes.php:983
msgid "Stick Below Header"
msgstr "Fissa sotto l'header"

#: inc/metabox/class-astra-meta-boxes.php:969
msgid "Stick Above Header"
msgstr "Fissa sopra l'header"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 1"
msgstr "Colore Tema 1"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 2"
msgstr "Colore Tema 2"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 9"
msgstr "Colore Tema 9"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 8"
msgstr "Colore Tema 8"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 7"
msgstr "Colore Tema 7"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 6"
msgstr "Colore Tema 6"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 5"
msgstr "Colore Tema 5"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 4"
msgstr "Colore Tema 4"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 3"
msgstr "Colore Tema 3"

#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:123
msgid "Site Background"
msgstr "Sfondo Sito"

#: inc/customizer/configurations/typography/class-astra-global-typo-configs.php:476
msgid "Presets"
msgstr "Impostazioni predefinite"

#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:135
msgid "Content Background"
msgstr "Sfondo del contenuto"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3381
msgid "Palette"
msgstr "Palette"

#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:43
#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:54
msgid "Global Palette"
msgstr "Palette globale"

#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:47
msgid "Button Presets"
msgstr "Preimpostazioni dei pulsanti"

#: inc/class-astra-global-palette.php:315
msgid "Color 9"
msgstr "Colore 9"

#: inc/class-astra-global-palette.php:314
msgid "Color 8"
msgstr "Colore 8"

#: inc/class-astra-global-palette.php:313
msgid "Color 7"
msgstr "Colore 7"

#: inc/class-astra-global-palette.php:312
msgid "Color 6"
msgstr "Colore 6"

#: inc/class-astra-global-palette.php:311
msgid "Color 5"
msgstr "Colore 5"

#: inc/class-astra-global-palette.php:310
msgid "Color 4"
msgstr "Colore 4"

#: inc/class-astra-global-palette.php:309
msgid "Color 3"
msgstr "Colore 3"

#: inc/class-astra-global-palette.php:308
msgid "Color 2"
msgstr "Colore 2"

#: inc/class-astra-global-palette.php:307
msgid "Color 1"
msgstr "Colore 1"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6402
#: admin/assets/build/dashboard-app.js:10
msgid "Design"
msgstr "Design"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:549
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:568
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Image Position"
msgstr "Posizione dell'immagine"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5757
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Repeat"
msgstr "Ripeti"

#: inc/customizer/configurations/builder/header/configs/site-identity.php:50
msgid "Site Title & Logo Settings"
msgstr "Impostazioni titolo sito e logo"

#: inc/core/builder/class-astra-builder-helper.php:687
#: inc/core/builder/class-astra-builder-helper.php:864
#: inc/customizer/class-astra-customizer.php:1654
msgid "Site Title & Logo"
msgstr "Titolo e logo del sito"

#: inc/customizer/class-astra-customizer.php:248
msgid "Site Icons are what you see in browser tabs, bookmark bars, and within the WordPress mobile apps. Upload one here! Site Icons should be square and at least 512 × 512 pixels."
msgstr "Le icone del sito sono quelle che vedi nelle schede del browser, nelle barre dei segnalibri e all’interno delle App per i dispositivi mobili di WordPress. Carica qui un'icona! Le icone del sito devono essere quadrate e di almeno 512×512 pixel."

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:316
msgid "Underline Content Links"
msgstr "Sottolinea contenuto link"

#: inc/customizer/configurations/builder/header/configs/menu.php:154
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:312
msgid "Submenu Animation"
msgstr "Animazione del sottomenu"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:161
msgid "Title Alignment"
msgstr "Allineamento del titolo"

#: inc/builder/controllers/class-astra-builder-ui-controller.php:319
msgid "Shopping Cart"
msgstr "Carrello"

#: searchform.php:30
msgid "Search Submit"
msgstr "Invia ricerca"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1009
msgid "Section Background"
msgstr "Sfondo della sezione"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:985
msgid "Section Title"
msgstr "Titolo della sezione"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:694
msgid "Descending"
msgstr "Decrescente"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:693
msgid "Ascending"
msgstr "Crescente"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:688
msgid "Order"
msgstr "Ordinamento"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:674
msgid "Random"
msgstr "Casuale"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:673
msgid "Post Order"
msgstr "Ordine degli articoli"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:700
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:327
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:69
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:671
msgid "Date"
msgstr "Data"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:668
msgid "Order by"
msgstr "Ordina per"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2693
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:262
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:702
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:650
msgid "Tags"
msgstr "Tag"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:255
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:693
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:649
msgid "Categories"
msgstr "Categorie"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:605
msgid "4"
msgstr "4"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:604
msgid "3"
msgstr "3"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:603
msgid "2"
msgstr "2"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:602
msgid "1"
msgstr "1"

#: inc/modules/related-posts/class-astra-related-posts-loader.php:82
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:89
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:106
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:121
msgid "Related Posts"
msgstr "Articoli correlati"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1025
msgid "Meta Color"
msgstr "Colore Meta"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:840
msgid "Section Title Font"
msgstr "Font titolo sezione"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:675
msgid "Comment Counts"
msgstr "Conteggio commenti"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:224
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:207
msgid "Posts Structure"
msgstr "Struttura degli articoli"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1055
msgid "Meta Link Color"
msgstr "Colore meta link"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1370
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:895
msgid "Meta Font"
msgstr "Meta Font"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:647
msgid "Related Posts by"
msgstr "Articoli correlati di"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:629
msgid "Posts Query"
msgstr "Query degli articoli"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:572
msgid "Total Number of Related Posts"
msgstr "Numero totale degli articoli correlati"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:545
msgid "Excerpt Word Count"
msgstr "Conteggio delle parole del riassunto"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:508
msgid "Enable Post Excerpt"
msgstr "Abilita riassunto degli articoli"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:48
msgid "Title & Post Meta"
msgstr "Meta titolo e articolo"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:600
msgid "Grid Column Layout"
msgstr "Layout colonne della griglia"

#: searchform.php:27
msgid "Search for:"
msgstr "Cerca:"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:269
msgid "Popup Padding"
msgstr "Spaziatura interna del popup"

#: inc/customizer/configurations/builder/header/configs/edd-cart.php:165
msgid "EDD Cart Icon Color"
msgstr "Colore icona carrello EDD"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:549
msgid "Woo Cart Icon Color"
msgstr "Colore icona carrello Woo"

#: inc/admin-functions.php:42
#: inc/core/builder/class-astra-builder-helper.php:735
#: inc/core/builder/class-astra-builder-helper.php:879
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:41
msgid "Off-Canvas Menu"
msgstr "Menu off-canvas"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:593
msgid "HTML Color"
msgstr "Colore HTML"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:993
msgid "Text / Placeholder"
msgstr "Testo / Segnaposto"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3598
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:60
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:76
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:338
#: inc/customizer/configurations/builder/header/configs/menu.php:502
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:286
msgid "Font"
msgstr "Font"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:604
msgid "Tagline Font"
msgstr "Font del motto"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:90
msgid "Header Position"
msgstr "Posizione dell'header"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:51
msgid "After"
msgstr "Dopo"

#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:63
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:289
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:811
msgid "Content Colors"
msgstr "Colori del contenuto"

#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:128
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:196
msgid "Label Color"
msgstr "Colore dell'etichetta"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:785
msgid "Cart Button"
msgstr "Pulsante del carrello"

#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:115
msgid "Link / Text"
msgstr "Link / Testo"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:802
#: inc/metabox/class-astra-meta-boxes.php:468
#: inc/metabox/class-astra-meta-boxes.php:909
msgid "Disable Footer"
msgstr "Disabilita il footer"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:412
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:429
#: inc/customizer/configurations/builder/header/configs/menu.php:373
msgid "Text / Link"
msgstr "Testo / Link"

#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:464
msgid "Icon Radius"
msgstr "Raggio Icona"

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:521
msgid "Content Font Size"
msgstr "Dimensione Font del Contenuto"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-typo-configs.php:52
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:430
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:924
msgid "Content Font"
msgstr "Font del Contenuto"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:588
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:702
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1168
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:437
msgid "Title Font"
msgstr "Font del Titolo"

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:63
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:714
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1180
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:448
msgid "Text Font"
msgstr "Font del Testo"

#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:305
#: inc/customizer/configurations/builder/header/configs/menu.php:515
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:301
msgid "Menu Font"
msgstr "Font del Menu"

#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:256
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:424
msgid "Menu Spacing"
msgstr "Spaziatura Menu"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1026
msgid "Account Menu Color"
msgstr "Colore menu account"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:899
msgid "Toggle Color"
msgstr "Attiva/disattiva colore"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:662
msgid "Box Background"
msgstr "Sfondo del box"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:472
msgid "Social Color"
msgstr "Colori social media"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:437
msgid "Submenu Color"
msgstr "Colore sottomenu"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:201
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:255
msgid "Hover / Active"
msgstr "Hover/Attiva"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:42
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:50
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:147
msgid "Inside"
msgstr "Interno"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:893
msgid "Checkout Button"
msgstr "Pulsante di pagamento"

#: inc/customizer/configurations/builder/header/configs/account.php:559
msgid "Profile Text Color"
msgstr "Colore testo profilo"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:414
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:290
msgid "Button Background"
msgstr "Sfondo pulsante"

#: inc/customizer/configurations/builder/header/configs/edd-cart.php:282
msgid "Checkout"
msgstr "Pagamento"

#: inc/customizer/configurations/builder/header/configs/edd-cart.php:198
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:649
msgid "Cart Tray"
msgstr "Vassoio del carrello"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:234
msgid "Inner Element Spacing"
msgstr "Spaziatura degli elementi interni"

#: inc/customizer/configurations/builder/header/configs/menu.php:190
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:107
msgid "Divider Size"
msgstr "Dimensione del divisore"

#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:366
msgid "Menu Font Size"
msgstr "Dimensione font del menu"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:867
msgid "Post Title Font"
msgstr "Font del titolo dell'articolo"

#: inc/customizer/configurations/builder/header/configs/site-identity.php:313
msgid "Tagline Font Size"
msgstr "Dimensione font del motto"

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:484
#: inc/customizer/configurations/builder/header/configs/site-identity.php:232
msgid "Title Font Size"
msgstr "Dimensione font del titolo"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:776
#: inc/metabox/class-astra-meta-boxes.php:398
#: inc/metabox/class-astra-meta-boxes.php:888
msgid "Disable Mobile Header"
msgstr "Disabilita header sui dispositivi mobili"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:766
#: inc/metabox/class-astra-meta-boxes.php:387
#: inc/metabox/class-astra-meta-boxes.php:877
msgid "Disable Below Header"
msgstr "Disabilita l'header sotto"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:756
#: inc/metabox/class-astra-meta-boxes.php:363
#: inc/metabox/class-astra-meta-boxes.php:863
msgid "Disable Above Header"
msgstr "Disabilita l'header sopra"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:137
msgid "Content Link Color"
msgstr "Colore link contenuto"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:646
msgid "Search Color"
msgstr "Colore dell'icona della ricerca"

#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:218
msgid "If this color setting is not reflecting, check if colors are set from dedicated above, below or primary footer settings."
msgstr "Se questa impostazione del colore non viene applicata, controlla se i colori sono impostati dalle impostazioni dedicate sopra, sotto o del footer principale."

#: assets/svg/logo-svg-icons/icons-v6-1.php:6538
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:58
msgid "Icons"
msgstr "Icone"

#: inc/core/builder/class-astra-builder-options.php:1261
msgid "Log In"
msgstr "Accedi"

#: inc/customizer/configurations/builder/header/configs/account.php:375
msgid "Login URL"
msgstr "URL di accesso"

#: inc/customizer/configurations/builder/header/configs/account.php:321
msgid "Preview"
msgstr "Anteprima"

#: inc/customizer/configurations/builder/header/configs/edd-cart.php:90
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:316
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:553
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:576
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:624
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:646
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:795
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1598
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:471
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:490
msgid "Style"
msgstr "Stile"

#: inc/customizer/configurations/builder/header/configs/account.php:142
#: inc/customizer/configurations/builder/header/configs/account.php:171
msgid "Avatar"
msgstr "Avatar"

#: inc/admin-functions.php:56
#: inc/customizer/configurations/builder/header/configs/header-builder.php:118
#: inc/customizer/configurations/builder/header/configs/menu.php:38
msgid "Menu "
msgstr "Menu "

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:229
msgid "Widget "
msgstr "Widget "

#: inc/admin-functions.php:41
#: inc/customizer/configurations/builder/header/configs/header-builder.php:115
#: inc/customizer/configurations/builder/header/configs/menu.php:35
msgid "Secondary Menu"
msgstr "Menu secondario"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:72
#: inc/customizer/configurations/builder/header/configs/above-header.php:29
msgid "Above Header"
msgstr "Sopra l'header"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:171
msgid "Content Alignment"
msgstr "Allineamento del contenuto"

#. translators: %s Index
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:64
msgid "Button %s"
msgstr "Pulsante %s"

#. translators: %s Index
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:69
msgid "HTML %s"
msgstr "HTML %s"

#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:87
msgid "Official"
msgstr "Ufficiale"

#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:32
msgid "Primary Footer"
msgstr "Footer principale"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:32
msgid "Above Footer"
msgstr "Sopra il footer"

#: assets/svg/logo-svg-icons/icons-v6-0.php:6030
#: inc/customizer/configurations/builder/footer/configs/copyright.php:31
msgid "Copyright"
msgstr "Copyright"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:148
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:148
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:147
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:510
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:990
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:275
msgid "Vertical Alignment"
msgstr "Allineamento verticale"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:125
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:125
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:124
#: inc/customizer/configurations/builder/header/configs/above-header.php:53
#: inc/customizer/configurations/builder/header/configs/below-header.php:53
#: inc/customizer/configurations/builder/header/configs/primary-header.php:65
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:419
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:674
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:344
msgid "Height"
msgstr "Altezza"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:60
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:60
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:60
msgid "Column"
msgstr "Colonna"

#: inc/customizer/configurations/builder/footer/configs/below-footer.php:32
msgid "Below Footer"
msgstr "Sotto il footer"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:420
#: inc/customizer/configurations/builder/header/configs/menu.php:380
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:158
msgid "Menu Color"
msgstr "Colore del menu"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:826
#: inc/core/builder/class-astra-builder-helper.php:699
#: inc/core/builder/class-astra-builder-helper.php:884
#: inc/customizer/configurations/builder/header/configs/account.php:95
msgid "Account"
msgstr "Account"

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:307
msgid "Content Color"
msgstr "Colore del contenuto"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:139
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:102
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:177
#: inc/customizer/configurations/builder/header/configs/account.php:494
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:141
#: inc/customizer/configurations/builder/header/configs/search.php:61
msgid "Icon Color"
msgstr "Colore dell'icona"

#: inc/customizer/class-astra-extended-base-configuration.php:105
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:194
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:506
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:248
#: inc/customizer/configurations/builder/footer/configs/copyright.php:136
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:280
#: inc/customizer/configurations/builder/header/configs/account.php:605
#: inc/customizer/configurations/builder/header/configs/header-builder.php:675
#: inc/customizer/configurations/builder/header/configs/menu.php:355
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:448
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:308
#: inc/customizer/configurations/builder/header/configs/search.php:216
#: inc/customizer/configurations/builder/header/configs/site-identity.php:135
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:898
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1468
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:598
msgid "Margin"
msgstr "Margine"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1015
msgid "Divider"
msgstr "Divisore"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:151
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:151
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:150
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:513
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:993
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:278
msgid "Middle"
msgstr "In mezzo"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1117
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1133
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:207
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:208
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:223
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:224
#: inc/customizer/configurations/builder/header/configs/menu.php:471
#: inc/customizer/configurations/builder/header/configs/menu.php:472
#: inc/customizer/configurations/builder/header/configs/menu.php:488
#: inc/customizer/configurations/builder/header/configs/menu.php:489
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:253
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:254
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:270
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:271
msgid "Active"
msgstr "Attivo"

#: inc/customizer/class-astra-extended-base-configuration.php:150
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:292
#: inc/customizer/configurations/builder/header/configs/menu.php:262
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:207
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:464
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:570
msgid "Border Width"
msgstr "Larghezza del bordo"

#: admin/includes/class-astra-admin-ajax.php:129
#: inc/core/builder/class-astra-builder-admin.php:69
#: inc/core/builder/class-astra-builder-admin.php:96
msgid "You don't have the access"
msgstr "Non hai accesso"

#: inc/core/builder/class-astra-builder-options.php:1262
msgid "My Account"
msgstr "Il mio account"

#: inc/core/builder/class-astra-builder-options.php:1684
#: inc/core/builder/class-astra-builder-options.php:1723
msgid "Insert HTML text here."
msgstr "Inserisci qui il testo HTML."

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:110
#: inc/customizer/configurations/builder/header/configs/below-header.php:29
msgid "Below Header"
msgstr "Sotto l'header"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:120
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:440
#: inc/customizer/configurations/builder/header/configs/account.php:449
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:159
#: inc/customizer/configurations/builder/header/configs/search.php:75
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:613
msgid "Icon Size"
msgstr "Dimensione icona"

#. translators: 1: index
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:59
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:352
msgid "Social Icons"
msgstr "Icone social"

#: inc/customizer/configurations/builder/header/configs/account.php:139
#: inc/customizer/configurations/builder/header/configs/account.php:262
msgid "Profile Type"
msgstr "Tipo Profilo"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:56
msgid "Header Type"
msgstr "Tipo Header"

#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:83
msgid "Color Type"
msgstr "Tipo Colore"

#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:374
msgid "Show Label"
msgstr "Mostra etichetta"

#. translators: 1: index
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:59
msgid "Social Icons %s"
msgstr "Icone social %s"

#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:205
msgid "Background Color-Image"
msgstr "Colore e immagine di sfondo"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:91
msgid "The Image option cannot be sortable if the Product Style is selected to the List Style "
msgstr "L'opzione Immagine non può essere ordinata se lo stile del prodotto è selezionato su stile elenco"

#: inc/builder/controllers/class-astra-builder-widget-controller.php:111
msgid "Add widgets here:"
msgstr "Aggiungi i widget qui:"

#: inc/builder/controllers/class-astra-builder-ui-controller.php:169
#: inc/builder/controllers/class-astra-builder-ui-controller.php:170
#: inc/builder/controllers/class-astra-builder-ui-controller.php:189
#: inc/builder/controllers/class-astra-builder-ui-controller.php:204
msgid "Click to edit this element."
msgstr "Fai clic per modificare questo elemento."

#: inc/builder/controllers/class-astra-builder-ui-controller.php:189
#: inc/builder/controllers/class-astra-builder-ui-controller.php:204
msgid "Click to edit this Row."
msgstr "Fai clic per modificare questa riga."

#: inc/builder/class-astra-builder-loader.php:102
#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:230
msgid "Footer Builder"
msgstr "Builder del footer"

#: inc/builder/class-astra-builder-loader.php:99
#: inc/customizer/configurations/builder/header/configs/header-builder.php:168
#: inc/customizer/configurations/builder/header/configs/header-builder.php:231
#: inc/customizer/configurations/builder/header/configs/header-builder.php:523
msgid "Header Builder"
msgstr "Builder dell'header"

#: inc/builder/type/header/account/class-astra-header-account-component.php:116
msgid "Account Woo Navigation"
msgstr "Navigazione dell'account Woo"

#: inc/customizer/configurations/builder/header/configs/account.php:228
msgid "Account URL"
msgstr "URL account"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:515
msgid "Tagline"
msgstr "Motto"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:78
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:507
msgid "Background Overlay"
msgstr "Sovrapposizione dello sfondo"

#: inc/builder/controllers/class-astra-builder-ui-controller.php:429
#: inc/builder/controllers/class-astra-builder-ui-controller.php:499
msgid "Account icon link"
msgstr "Link icona dell'account"

#: inc/core/builder/class-astra-builder-helper.php:851
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:36
msgid "EDD Cart"
msgstr "Carrello EDD"

#: inc/core/builder/class-astra-builder-helper.php:729
#: inc/core/builder/class-astra-builder-helper.php:874
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:32
msgid "Toggle Button"
msgstr "Attiva/Disattiva pulsante"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:582
msgid "Header Cart Icon"
msgstr "Icona del carrello nell'header"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:601
msgid "WooCommerce Cart"
msgstr "Carrello WooCommerce"

#: inc/customizer/configurations/builder/header/configs/edd-cart.php:70
msgid "Display Cart Title"
msgstr "Visualizza il titolo del carrello"

#: inc/customizer/configurations/builder/header/configs/edd-cart.php:49
msgid "Display Cart Total"
msgstr "Visualizza il totale del carrello"

#: inc/customizer/configurations/builder/header/configs/header-builder.php:613
msgid "Header Types"
msgstr "Tipi di header"

#: inc/customizer/configurations/builder/header/configs/account.php:247
msgid "Logged Out View"
msgstr "Visualizza come disconnesso"

#: inc/customizer/configurations/builder/header/configs/account.php:121
msgid "Logged In View"
msgstr "Visualizza come se loggato"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:60
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:197
msgid "Dropdown"
msgstr "Menu a discesa"

#: inc/admin-functions.php:66
msgid "Logged In Account Menu"
msgstr "Menu per gli account loggati"

#: inc/core/builder/class-astra-builder-helper.php:841
msgid "Woo Cart"
msgstr "Carrello Woo"

#: inc/core/builder/class-astra-builder-helper.php:596
msgid "Single Row"
msgstr "Riga singola"

#: inc/core/builder/class-astra-builder-helper.php:586
msgid "Right Heavy 33/66"
msgstr "Larga a destra 33/66"

#: inc/core/builder/class-astra-builder-helper.php:582
msgid "Left Heavy 66/33"
msgstr "Larga a sinistra 66/33"

#: inc/core/builder/class-astra-builder-helper.php:568
msgid "Last Row, Previous Columns 50/50 - 100"
msgstr "Ultima riga, Colonne precedenti 50/50 – 100"

#: inc/core/builder/class-astra-builder-helper.php:564
msgid "First Row, Next Columns 100 - 50/50"
msgstr "Prima Riga, Colonne successive 100 – 50/50"

#: inc/core/builder/class-astra-builder-helper.php:560
msgid "Wide Center 20/60/20"
msgstr "Larga al centro 20/60/20"

#: inc/core/builder/class-astra-builder-helper.php:556
msgid "Center Heavy 25/50/25"
msgstr "Larga a centro 25/50/25"

#: inc/core/builder/class-astra-builder-helper.php:552
msgid "Right Heavy 25/25/50"
msgstr "Larga a destra 25/25/50"

#: inc/core/builder/class-astra-builder-helper.php:548
msgid "Left Heavy 50/25/25"
msgstr "Larga a sinistra 50/25/25"

#: inc/core/builder/class-astra-builder-helper.php:534
msgid "Two Column Grid"
msgstr "Griglia a due colonne"

#: inc/core/builder/class-astra-builder-helper.php:514
#: inc/core/builder/class-astra-builder-helper.php:524
#: inc/core/builder/class-astra-builder-helper.php:538
#: inc/core/builder/class-astra-builder-helper.php:572
#: inc/core/builder/class-astra-builder-helper.php:590
#: inc/core/builder/class-astra-builder-helper.php:608
#: inc/core/builder/class-astra-builder-helper.php:618
msgid "Collapse to Rows"
msgstr "Comprimi in righe"

#: inc/core/builder/class-astra-builder-helper.php:510
#: inc/core/builder/class-astra-builder-helper.php:520
#: inc/core/builder/class-astra-builder-helper.php:530
#: inc/core/builder/class-astra-builder-helper.php:544
#: inc/core/builder/class-astra-builder-helper.php:578
#: inc/core/builder/class-astra-builder-helper.php:604
#: inc/core/builder/class-astra-builder-helper.php:614
msgid "Equal Width Columns"
msgstr "Colonne di larghezza uguale"

#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:394
msgid "Icon Spacing"
msgstr "Spaziatura icona"

#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:59
#: inc/customizer/configurations/builder/header/configs/menu.php:79
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:58
msgid "Configure Menu from Here."
msgstr "Configura il menu da qui."

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:167
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:167
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:166
msgid "Inner Elements Layout"
msgstr "Layout degli elementi interni"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:59
msgid "Full-Screen"
msgstr "Schermo intero"

#: inc/customizer/configurations/builder/header/configs/menu.php:322
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:71
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:83
msgid "Item Divider"
msgstr "Elemento divisore"

#: inc/customizer/configurations/builder/header/configs/menu.php:241
msgid "Top Offset"
msgstr "Offset superiore"

#: inc/customizer/configurations/builder/header/configs/menu.php:174
msgid "Submenu Container"
msgstr "Contenitore del sottomenu"

#: inc/customizer/configurations/builder/header/configs/menu.php:96
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:457
msgid "Zoom In"
msgstr "Aumenta lo zoom"

#: inc/customizer/configurations/builder/header/configs/menu.php:93
msgid "Menu Hover Style"
msgstr "Stile del menu al passaggio del mouse"

#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:417
msgid "Icon Background Space"
msgstr "Spaziatura sfondo dell'icona"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:242
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:242
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:244
msgid "Inner Column Spacing"
msgstr "Spaziatura interna della colonna"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:58
msgid "Flyout"
msgstr "Flyout"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:208
msgid "Close Icon Color"
msgstr "Colore dell'icona chiudi"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:29
msgid "Off-Canvas"
msgstr "Off-Canvas"

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:189
msgid "Visibility"
msgstr "Visibilità"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6422
msgid "Social"
msgstr "Social"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:77
msgid "Enable Cross-sells"
msgstr "Attiva Cross-sells"

#. translators: 1: number of products
#: inc/compatibility/woocommerce/class-astra-woocommerce.php:853
msgctxt "product categories"
msgid "%1$s Product"
msgid_plural "%1$s Products"
msgstr[0] "%1$s prodotto"
msgstr[1] "%1$s prodotti"

#: inc/lib/bsf-analytics/class-bsf-analytics.php:290
msgid "No Thanks"
msgstr "No, grazie"

#: inc/lib/bsf-analytics/class-bsf-analytics.php:279
msgid "Yes! Allow it"
msgstr "Sì! Permettilo"

#: inc/lib/bsf-analytics/class-bsf-analytics.php:245
msgid "This will be applicable for all sites from the network."
msgstr "Questo sarà applicabile per tutti i siti della rete."

#: inc/lib/bsf-analytics/class-bsf-analytics.php:415
msgid "Usage Tracking"
msgstr "Tracciamento dell'utilizzo"

#: inc/core/class-astra-admin-settings.php:494
msgid "Details &#187;"
msgstr "Dettagli &#187;"

#: inc/core/class-astra-admin-settings.php:467
#: inc/core/class-astra-admin-settings.php:479
#: inc/core/class-astra-admin-settings.php:491
msgid "See Library &#187;"
msgstr "Vedi libreria &#187;"

#: admin/includes/class-astra-menu.php:437
#: admin/assets/build/dashboard-app.js:15
msgid "Starter Templates"
msgstr "Starter Templates"

#: inc/lib/astra-notices/class-astra-notices.php:146
msgid "WordPress Nonce not validated."
msgstr "Nonce WordPress non verificato."

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:339
msgid "Letter Spacing"
msgstr "Spaziatura tra caratteri"

#: inc/extras.php:446
msgid "Astra WordPress Theme"
msgstr "Tema WordPress Astra"

#. translators: %d: Minutes interval
#: inc/lib/batch-processing/class-astra-wp-background-process.php:410
msgid "Every %d Minutes"
msgstr "Ogni %d minuti"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:255
msgid "Customize Button Style."
msgstr "Personalizza stile pulsante."

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:289
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:449
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:216
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:529
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:270
#: inc/customizer/configurations/builder/footer/configs/copyright.php:80
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:99
msgid "Alignment"
msgstr "Allineamento"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:380
msgid "Separator"
msgstr "Separatore"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:90
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:119
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:245
msgid "Position"
msgstr "Posizione"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:40
msgid "Global"
msgstr "Globale"

#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:65
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Size"
msgstr "Dimensione"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:49
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:197
#: inc/customizer/class-astra-customizer-register-sections-panels.php:276
#: inc/customizer/class-astra-customizer.php:1686
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:659
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:72
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:393
msgid "Colors"
msgstr "Colori"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:926
#: inc/customizer/class-astra-customizer.php:1629
#: inc/customizer/class-astra-extended-base-configuration.php:136
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:680
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:98
msgid "Border"
msgstr "Bordo"

#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:110
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:393
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:636
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1103
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:384
msgid "Title Color"
msgstr "Colore del titolo"

#: admin/includes/class-astra-menu.php:386
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:221
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:237
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:38
#: inc/customizer/class-astra-extended-base-configuration.php:172
#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:84
#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:139
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:112
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:825
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:417
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:515
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Color"
msgstr "Colore"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:428
msgid "Archive Title"
msgstr "Titolo Archivio"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:161
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:163
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:128
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:129
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:164
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:165
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:181
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:218
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:219
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:235
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:272
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:273
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:498
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:504
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:536
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:542
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:608
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:724
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:728
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:758
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:762
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:792
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:795
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1049
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1073
#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:50
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:50
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:58
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:50
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:385
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:386
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:439
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:440
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:165
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:171
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:203
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:209
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:259
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:144
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:240
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:246
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:278
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:284
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:316
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:322
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:343
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:142
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:144
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:159
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:161
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:162
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:306
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:322
#: inc/customizer/configurations/builder/header/configs/menu.php:403
#: inc/customizer/configurations/builder/header/configs/menu.php:405
#: inc/customizer/configurations/builder/header/configs/menu.php:421
#: inc/customizer/configurations/builder/header/configs/menu.php:423
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:185
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:187
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:203
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:205
#: inc/customizer/configurations/builder/header/configs/site-identity.php:90
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:363
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:416
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:694
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:754
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:830
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:846
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:937
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:953
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:460
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:489
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:516
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:704
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:738
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:127
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:161
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:136
#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:119
#: inc/customizer/configurations/colors-background/class-astra-footer-colors-configs.php:56
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:53
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:411
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:450
#: inc/metabox/class-astra-meta-boxes.php:291
#: inc/metabox/class-astra-meta-boxes.php:816
#: inc/metabox/class-astra-meta-boxes.php:822
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:41
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:60
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:41
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:60
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:650
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:934
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:949
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1019
msgid "Normal"
msgstr "Normale"

#: inc/customizer/class-astra-extended-base-configuration.php:80
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:543
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:781
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:373
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:920
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1499
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:620
msgid "Padding"
msgstr "Spaziatura interna"

#: inc/customizer/class-astra-customizer.php:1749
msgid "Heading 6"
msgstr "Titolo 6"

#: inc/customizer/class-astra-customizer.php:1743
msgid "Heading 5"
msgstr "Titolo 5"

#: inc/customizer/class-astra-customizer.php:1737
msgid "Heading 4"
msgstr "Titolo 4"

#: inc/customizer/class-astra-customizer.php:1731
msgid "Heading 3"
msgstr "Titolo 3"

#: inc/customizer/class-astra-customizer.php:1725
msgid "Heading 2"
msgstr "Titolo 2"

#: inc/customizer/class-astra-customizer.php:1719
msgid "Heading 1"
msgstr "Titolo 1"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:253
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:269
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:840
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:433
msgid "Hover Color"
msgstr "Colore hover"

#: inc/customizer/class-astra-customizer.php:1412
#: inc/customizer/class-astra-customizer.php:1662
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:569
msgid "Site Icon"
msgstr "Icona del sito"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:176
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:178
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:146
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:147
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:200
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:254
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:290
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:291
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:517
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:523
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:555
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:561
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:623
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:741
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:745
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:775
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:779
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:808
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:811
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1083
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1103
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:402
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:403
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:456
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:457
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:184
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:190
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:222
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:228
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:277
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:163
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:259
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:265
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:297
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:303
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:335
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:341
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:360
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:171
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:175
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:188
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:192
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:338
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:354
#: inc/customizer/configurations/builder/header/configs/menu.php:433
#: inc/customizer/configurations/builder/header/configs/menu.php:437
#: inc/customizer/configurations/builder/header/configs/menu.php:451
#: inc/customizer/configurations/builder/header/configs/menu.php:455
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:215
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:219
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:233
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:237
#: inc/customizer/configurations/builder/header/configs/site-identity.php:104
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:380
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:434
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:710
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:770
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:862
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:878
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:969
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:985
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:476
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:502
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:529
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:721
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:755
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:144
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:178
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:149
#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:133
#: inc/customizer/configurations/colors-background/class-astra-footer-colors-configs.php:69
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:477
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:504
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1034
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1049
msgid "Hover"
msgstr "Hover"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:149
msgid "Post Content"
msgstr "Contenuto articolo"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:589
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:681
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:860
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:949
#: inc/class-astra-global-palette.php:299
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:63
#: inc/customizer/class-astra-customizer.php:1617
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:93
#: inc/customizer/configurations/builder/header/configs/account.php:143
#: inc/customizer/configurations/builder/header/configs/account.php:201
#: inc/customizer/configurations/builder/header/configs/account.php:267
#: inc/customizer/configurations/builder/header/configs/account.php:348
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:798
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:905
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:87
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:157
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:218
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Text"
msgstr "Testo"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:405
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:527
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:204
msgid "Structure"
msgstr "Struttura"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:105
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:105
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:105
#: inc/customizer/configurations/builder/header/configs/header-builder.php:645
#: inc/customizer/configurations/builder/header/configs/menu.php:133
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:803
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:395
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:400
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:355
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:71
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:654
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:324
msgid "Width"
msgstr "Larghezza"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:230
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:296
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:132
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:75
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:384
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:214
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:506
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:186
msgid "Custom Width"
msgstr "Larghezza personalizzata"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:184
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:184
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:185
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:384
msgid "Top Border Size"
msgstr "Dimensione bordo superiore"

#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:125
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:550
msgid "Menu Label"
msgstr "Etichetta del menu"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:476
msgid "Mobile Menu"
msgstr "Menu mobile"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:331
msgid "Container Border"
msgstr "Bordo del contenitore"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:289
msgid "Sub Menu"
msgstr "Sottomenu"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:120
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:690
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:349
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:823
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:258
msgid "Meta"
msgstr "Meta"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:398
msgid "Top Border Color"
msgstr "Colore bordo superiore"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:466
msgid "Take Last Item Outside Menu"
msgstr "Tieni l'ultimo elemento fuori dal menu"

#: inc/customizer/configurations/builder/header/configs/menu.php:216
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:133
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:373
msgid "Divider Color"
msgstr "Colore divisore"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:362
msgid "Submenu Divider"
msgstr "Divisore sottomenu"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:519
msgid "The parent menu should have a # link for the submenu to open on a link."
msgstr "Il menu genitore dovrebbe avere un link # per far aprire un link nel sottomenu."

#. Translators: %s is the page number.
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:409
msgid "Page %s"
msgstr "Pagina %s"

#. Translators: %s is the search query.
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:407
msgid "Search results for: %s"
msgstr "Risultati della ricerca per: %s"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:402
msgctxt "breadcrumbs aria label"
msgid "Breadcrumbs"
msgstr "Breadcrumb"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:66
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:409
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:532
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:73
msgid "Breadcrumb"
msgstr "Breadcrumb"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:404
msgid "404 Not Found"
msgstr "404: pagina non trovata"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:405
msgid "Archives"
msgstr "Archivi"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:175
msgid "Center"
msgstr "Centro"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:261
msgid "All Pages, All Posts, All Attachments"
msgstr "Tutte le pagine, tutti gli articoli, tutti gli allegati"

#. Translators: Weekly archive title. %s is the week date format.
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:415
msgid "Week %s"
msgstr "Settimana %s"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:401
msgid "Browse:"
msgstr "Sfoglia:"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:44
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:52
msgid "Before Title"
msgstr "Prima del titolo"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:73
msgid "Breadcrumb Overview"
msgstr "Panoramica breadcrumb"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:117
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:261
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:725
msgid "Separator Color"
msgstr "Colore del separatore"

#. Translators: Minute archive title. %s is the minute time format.
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:413
msgid "Minute %s"
msgstr "Minuto %s"

#. Translators: %s is the page number.
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:411
msgid "Comment Page %s"
msgstr "Commento Pagina %s"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:924
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:981
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:1352
msgctxt "monthly archives date format"
msgid "F"
msgstr "F"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:923
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:952
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:980
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:1007
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:1348
msgctxt "yearly archives date format"
msgid "Y"
msgstr "Y"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:907
msgctxt "hour archives time format"
msgid "g a"
msgstr "g a"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:873
msgctxt "minute and hour archives time format"
msgid "g:i a"
msgstr "g:i a"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:890
msgctxt "minute archives time format"
msgid "i"
msgstr "i"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:190
msgid "Latest posts page or when any page is selected as blog page"
msgstr "Pagina degli ultimi articoli o quando una qualsiasi pagina è selezionata come pagina del blog"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:350
msgid "Breadcrumb Source"
msgstr "Sorgente Breadcrumb"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:953
msgctxt "weekly archives date format"
msgid "W"
msgstr "W"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:925
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:1356
msgctxt "daily archives date format"
msgid "j"
msgstr "j"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:953
msgid "Enable on Blog / Posts Page?"
msgstr "Vuoi abilitare nel blog/pagina articoli?"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:159
msgid "Latest Posts page is your site's front page when the latest posts are displayed on the home page."
msgstr "La pagina Ultimi Articoli è la prima pagina del tuo sito quando gli ultimi articoli vengono visualizzati nella home page."

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:144
msgid "Blog Page is when Latest Posts are selected to be displayed on a particular page."
msgstr "La pagina Blog è quando vengono selezionati gli ultimi articoli da visualizzare su una determinata pagina."

#: inc/class-astra-mobile-header.php:110
#: inc/core/class-astra-walker-page.php:87
#: inc/core/class-astra-walker-page.php:98 inc/extras.php:545
msgid "Menu Toggle"
msgstr "Attiva/disattiva menu"

#: admin/includes/class-astra-admin-ajax.php:463
msgid "Plugin Successfully Deactivated"
msgstr "Plugin disattivato con successo"

#: inc/core/class-astra-admin-settings.php:147
msgid "Deactivate"
msgstr "Disattiva"

#: inc/core/class-astra-admin-settings.php:145
msgid "Deactivating"
msgstr "Disattivazione"

#: admin/includes/class-astra-menu.php:336
#: admin/includes/class-astra-theme-builder-free.php:103
#: inc/core/class-astra-admin-settings.php:144
msgid "Activating"
msgstr "Attivazione"

#: admin/includes/class-astra-menu.php:338
#: admin/includes/class-astra-theme-builder-free.php:105
#: inc/core/class-astra-admin-settings.php:146
msgid "Activate"
msgstr "Attiva"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:383
msgid "Primary Header Button"
msgstr "Pulsante header principale"

#: inc/customizer/configurations/builder/header/configs/menu.php:635
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:62
msgid "Menu"
msgstr "Menu"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:242
#: inc/customizer/configurations/builder/header/configs/site-identity.php:34
msgid "Logo"
msgstr "Logo"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:227
msgid "Button Style"
msgstr "Stile Pulsante"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:219
msgid "Border Size"
msgstr "Dimensione bordo"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:224
msgid "Theme Button"
msgstr "Pulsante tema"

#: inc/customizer/configurations/builder/header/configs/menu.php:115
msgid "Submenu"
msgstr "Sottomenu"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1051
msgid "Link / Text Color"
msgstr "Link / Colore testo"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:64
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:206
msgid "Desktop + Mobile"
msgstr "Desktop + Mobile"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2313
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:63
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:205
msgid "Mobile"
msgstr "Mobile"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:707
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:235
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:207
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:207
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:206
#: inc/customizer/configurations/builder/header/configs/menu.php:281
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:234
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:440
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:254
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:348
msgid "Border Color"
msgstr "Colore bordo"

#: assets/svg/logo-svg-icons/icons-v6-1.php:351
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:62
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:204
msgid "Desktop"
msgstr "Desktop"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:201
msgid "Enable On"
msgstr "Abilitato su"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:59
msgid "Enable on Complete Website"
msgstr "Abilita sul sito web completo"

#: inc/addons/transparent-header/classes/class-astra-ext-transparent-header-markup.php:339
#: inc/metabox/class-astra-meta-boxes.php:998
#: inc/metabox/class-astra-meta-boxes.php:1011
msgid "Enabled"
msgstr "Abilitato"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:203
msgid "Button Link"
msgstr "Link del pulsante"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:360
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:274
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:183
msgid "Button Text"
msgstr "Testo del pulsante"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:394
msgid "Transparent Header Button"
msgstr "Pulsante header trasparente"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:372
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:639
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:46
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:225
msgid "Header Button"
msgstr "Pulsante dell'header"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:152
msgid "Options"
msgstr "Opzioni"

#. translators: %s Index
#: admin/includes/class-astra-menu.php:394
#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:151
#: inc/core/builder/class-astra-builder-options.php:1339
#: inc/core/builder/class-astra-builder-options.php:1508
#: inc/core/class-astra-theme-options.php:484
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:64
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:148
msgid "Button"
msgstr "Pulsante"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6639
#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:93
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Image"
msgstr "Immagine"

#: inc/compatibility/edd/class-astra-edd.php:861
msgid "View Details"
msgstr "Visualizza i dettagli"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-single-product-layout-configs.php:41
msgid "Disable Product Navigation"
msgstr "Disabilita la navigazione del Prodotto"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:199
msgid "Archive Content Width"
msgstr "Larghezza contenitore Archivio"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:187
msgid "Variable Product Button Text"
msgstr "Testo Pulsante Prodotto Variabile"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:148
msgid "Variable Product Button"
msgstr "Pulsante Prodotto Variabile"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:126
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:53
msgid "Cart Button Text"
msgstr "Testo Pulsante Carrello"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:70
#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:90
msgid "Product Structure"
msgstr "Struttura Prodotto"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:54
msgid "Archive Columns"
msgstr "Colonne Archivio"

#: inc/compatibility/edd/customizer/class-astra-customizer-register-edd-section.php:51
msgid "Product Archive"
msgstr "Archivio Prodotto"

#: inc/compatibility/edd/class-astra-edd.php:319
#: inc/compatibility/edd/customizer/class-astra-customizer-register-edd-section.php:37
#: admin/assets/build/dashboard-app.js:10
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: inc/compatibility/edd/class-astra-edd.php:935
msgid "This sidebar will be used on EDD Single Product page."
msgstr "Questa barra laterale verrà utilizzata nella pagina del singolo prodotto EDD."

#: inc/compatibility/edd/class-astra-edd.php:933
msgid "EDD Single Product Sidebar"
msgstr "Barra laterale singolo prodotto EDD"

#: inc/compatibility/edd/class-astra-edd.php:919
msgid "Easy Digital Downloads Sidebar"
msgstr "Barra laterale Easy Digital Downloads"

#. Translators: %s is the theme name.
#. translators: %s is the Astra theme name.
#: inc/metabox/class-astra-meta-boxes.php:149
#: inc/metabox/class-astra-meta-boxes.php:596
#: inc/metabox/extend-metabox/build/elementor.js:3
msgid "%s Settings"
msgstr "Impostazioni %s"

#: inc/customizer/class-astra-customizer.php:624
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:137
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:128
msgid "900 Italic"
msgstr "900 Italic"

#: inc/customizer/class-astra-customizer.php:622
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:135
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:126
msgid "800 Italic"
msgstr "800 Italic"

#: inc/customizer/class-astra-customizer.php:620
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:133
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:124
msgid "700 Italic"
msgstr "700 Italic"

#: inc/customizer/class-astra-customizer.php:618
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:131
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:122
msgid "600 Italic"
msgstr "600 Italic"

#: inc/customizer/class-astra-customizer.php:616
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:129
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:120
msgid "500 Italic"
msgstr "500 Italic"

#: inc/customizer/class-astra-customizer.php:611
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:124
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:115
msgid "300 Italic"
msgstr "400 Italic"

#: inc/customizer/class-astra-customizer.php:609
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:122
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:113
msgid "200 Italic"
msgstr "200 Italic"

#: inc/customizer/class-astra-customizer.php:607
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:120
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:111
msgid "100 Italic"
msgstr "100 Italic"

#: inc/customizer/class-astra-customizer.php:614
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:127
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:118
msgid "400 Italic"
msgstr "400 Italic"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:326
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:93
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:201
#: inc/customizer/class-astra-extended-base-configuration.php:85
#: inc/customizer/class-astra-extended-base-configuration.php:110
#: inc/customizer/class-astra-extended-base-configuration.php:157
#: inc/customizer/class-astra-extended-base-configuration.php:189
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:72
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:296
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:319
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:200
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:470
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:511
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:253
#: inc/customizer/configurations/builder/footer/configs/copyright.php:141
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:261
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:285
#: inc/customizer/configurations/builder/header/configs/account.php:610
#: inc/customizer/configurations/builder/header/configs/header-builder.php:680
#: inc/customizer/configurations/builder/header/configs/menu.php:265
#: inc/customizer/configurations/builder/header/configs/menu.php:304
#: inc/customizer/configurations/builder/header/configs/menu.php:360
#: inc/customizer/configurations/builder/header/configs/menu.php:640
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:429
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:453
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:210
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:264
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:313
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:122
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:176
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:274
#: inc/customizer/configurations/builder/header/configs/search.php:221
#: inc/customizer/configurations/builder/header/configs/site-identity.php:140
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:248
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:491
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:522
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:549
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:573
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:596
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:784
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:806
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:376
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:398
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:539
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:128
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:153
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:334
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:904
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:926
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1475
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1505
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:604
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:626
msgid "Right"
msgstr "Destra"

#: admin/includes/class-astra-menu.php:673
msgid "Nav Menu"
msgstr "Menu di navigazione"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:328
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:92
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:203
#: inc/customizer/class-astra-extended-base-configuration.php:87
#: inc/customizer/class-astra-extended-base-configuration.php:112
#: inc/customizer/class-astra-extended-base-configuration.php:159
#: inc/customizer/class-astra-extended-base-configuration.php:191
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:74
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:298
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:321
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:202
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:472
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:513
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:255
#: inc/customizer/configurations/builder/footer/configs/copyright.php:143
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:263
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:287
#: inc/customizer/configurations/builder/header/configs/account.php:612
#: inc/customizer/configurations/builder/header/configs/header-builder.php:682
#: inc/customizer/configurations/builder/header/configs/menu.php:267
#: inc/customizer/configurations/builder/header/configs/menu.php:306
#: inc/customizer/configurations/builder/header/configs/menu.php:362
#: inc/customizer/configurations/builder/header/configs/menu.php:642
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:431
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:455
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:212
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:266
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:315
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:121
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:174
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:276
#: inc/customizer/configurations/builder/header/configs/search.php:223
#: inc/customizer/configurations/builder/header/configs/site-identity.php:142
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:247
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:493
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:521
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:551
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:575
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:598
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:786
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:808
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:378
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:400
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:541
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:130
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:155
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:336
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:906
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:928
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1477
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1507
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:606
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:628
msgid "Left"
msgstr "Sinistra"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:325
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:200
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:94
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:266
#: inc/customizer/class-astra-extended-base-configuration.php:84
#: inc/customizer/class-astra-extended-base-configuration.php:109
#: inc/customizer/class-astra-extended-base-configuration.php:156
#: inc/customizer/class-astra-extended-base-configuration.php:188
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:71
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:295
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:318
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:199
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:469
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:510
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:252
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:150
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:150
#: inc/customizer/configurations/builder/footer/configs/copyright.php:140
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:260
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:284
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:149
#: inc/customizer/configurations/builder/header/configs/account.php:609
#: inc/customizer/configurations/builder/header/configs/header-builder.php:679
#: inc/customizer/configurations/builder/header/configs/menu.php:264
#: inc/customizer/configurations/builder/header/configs/menu.php:303
#: inc/customizer/configurations/builder/header/configs/menu.php:359
#: inc/customizer/configurations/builder/header/configs/menu.php:639
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:428
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:452
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:209
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:263
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:312
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:273
#: inc/customizer/configurations/builder/header/configs/search.php:220
#: inc/customizer/configurations/builder/header/configs/site-identity.php:139
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:490
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:548
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:572
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:595
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:783
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:805
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:375
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:397
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:538
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:127
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:152
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:333
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:512
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:903
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:925
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:992
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1474
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1504
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:277
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:603
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:625
msgid "Top"
msgstr "Alto"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:327
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:202
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:95
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:267
#: inc/customizer/class-astra-extended-base-configuration.php:86
#: inc/customizer/class-astra-extended-base-configuration.php:111
#: inc/customizer/class-astra-extended-base-configuration.php:158
#: inc/customizer/class-astra-extended-base-configuration.php:190
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:73
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:297
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:320
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:201
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:471
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:512
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:254
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:152
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:152
#: inc/customizer/configurations/builder/footer/configs/copyright.php:142
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:262
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:286
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:151
#: inc/customizer/configurations/builder/header/configs/account.php:611
#: inc/customizer/configurations/builder/header/configs/header-builder.php:681
#: inc/customizer/configurations/builder/header/configs/menu.php:266
#: inc/customizer/configurations/builder/header/configs/menu.php:305
#: inc/customizer/configurations/builder/header/configs/menu.php:361
#: inc/customizer/configurations/builder/header/configs/menu.php:641
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:430
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:454
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:211
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:265
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:314
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:275
#: inc/customizer/configurations/builder/header/configs/search.php:222
#: inc/customizer/configurations/builder/header/configs/site-identity.php:141
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:492
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:523
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:550
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:574
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:597
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:785
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:807
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:377
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:399
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:540
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:129
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:154
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:335
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:514
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:905
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:927
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:994
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1476
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1506
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:279
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:605
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:627
msgid "Bottom"
msgstr "Basso"

#: inc/customizer/configurations/builder/header/configs/menu.php:159
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:317
msgid "Fade"
msgstr "Sfuma"

#: inc/customizer/configurations/builder/header/configs/menu.php:158
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:316
msgid "Slide Up"
msgstr "Scorri alto"

#: inc/customizer/configurations/builder/header/configs/menu.php:157
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:315
msgid "Slide Down"
msgstr "Scorri basso"

#: inc/customizer/configurations/builder/header/configs/menu.php:98
msgid "Overline"
msgstr "Sopralineato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4206
#: inc/customizer/configurations/builder/header/configs/menu.php:97
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:563
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:586
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:633
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:655
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:799
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1602
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:475
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:494
msgid "Underline"
msgstr "Sottolineato"

#. Translators: Author Name.
#: inc/blog/blog-config.php:435
msgid "View all posts by %1$s"
msgstr "Visualizza tutti gli articoli di %1$s"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:325
msgid "Typography Overview"
msgstr "Panoramica tipografia"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:127
msgid "Primary Header Overview"
msgstr "Panoramica Header Principale"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:100
msgid "Site Identity Overview"
msgstr "Panoramica Identità Sito"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:61
msgid "Container Overview"
msgstr "Panoramica Contenitore"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:55
msgid "Site Layout Overview"
msgstr "Panoramica Layout Sito"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:70
#: inc/customizer/class-astra-customizer-register-sections-panels.php:52
#: inc/customizer/class-astra-customizer-register-sections-panels.php:97
#: inc/customizer/class-astra-customizer-register-sections-panels.php:124
#: inc/customizer/class-astra-customizer-register-sections-panels.php:180
#: inc/customizer/class-astra-customizer-register-sections-panels.php:243
#: inc/customizer/class-astra-customizer-register-sections-panels.php:280
#: inc/customizer/class-astra-customizer-register-sections-panels.php:322
msgid "Helpful Information"
msgstr "Informazioni utili"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:246
msgid "Sidebar Overview"
msgstr "Panoramica barra laterale"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:183
msgid "Footer Bar Overview"
msgstr "Panoramica barra del footer"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:283
msgid "Colors & Background Overview"
msgstr "Panoramica Colori e Sfondo"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1103
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:572
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:965
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:115
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:151
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:152
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:650
msgid "Link"
msgstr "Link"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:641
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:823
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:892
#: inc/customizer/configurations/builder/header/configs/account.php:141
#: inc/customizer/configurations/builder/header/configs/account.php:172
#: inc/customizer/configurations/builder/header/configs/account.php:266
#: inc/customizer/configurations/builder/header/configs/account.php:294
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:151
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:649
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:112
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Icon"
msgstr "Icona"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:148
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:645
msgid "Dropdown Target"
msgstr "Menu a discesa"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:259
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:85
msgid "Different Logo For Retina Devices?"
msgstr "Logo diverso per dispositivi mobili?"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3086
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:95
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:97
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:321
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:579
msgid "Fill"
msgstr "Riempi"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:183
msgid "Mobile Logo (optional)"
msgstr "Logo dispositivi mobili (opzionale)"

#: admin/includes/class-astra-menu.php:608
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:394
msgid "Mobile Header"
msgstr "Header Dispositivi Mobili"

#: inc/customizer/configurations/builder/header/configs/header-builder.php:179
msgid "Header Layout"
msgstr "Header Layout"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:601
msgid "Toggle Button Color"
msgstr "Colore Pulsante Interruttore"

#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:99
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:581
msgid "Minimal"
msgstr "Minimal"

#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:92
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:567
msgid "Toggle Button Style"
msgstr "Stile Pulsante Interruttore"

#: inc/customizer/configurations/builder/header/configs/edd-cart.php:94
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:98
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:320
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:580
msgid "Outline"
msgstr "Contorno"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:142
msgid "Last Item in Menu"
msgstr "Nascondi ultimo elemento nel menu"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:492
msgid "Menu Breakpoint"
msgstr "Punto di interruzione menu"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:439
msgid "Hide Last Item in Menu on Mobile"
msgstr "Nascondi ultimo oggetto nel menu mobile"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:148
msgid "Different Logo For Mobile Devices?"
msgstr "Vuoi usare un Logo diverso per dispositivi mobili?"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:184
#: inc/customizer/class-astra-extended-base-configuration.php:182
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:313
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:143
#: inc/customizer/configurations/builder/header/configs/menu.php:298
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:258
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:485
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:590
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:856
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:449
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:530
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:617
msgid "Border Radius"
msgstr "Raggio Bordo"

#: admin/includes/class-astra-menu.php:374
#: inc/customizer/class-astra-customizer-register-sections-panels.php:92
#: inc/customizer/class-astra-customizer.php:224
#: inc/customizer/class-astra-customizer.php:236
msgid "Site Identity"
msgstr "Denominazione del sito"

#: inc/compatibility/learndash/customizer/class-astra-customizer-register-learndash-section.php:37
#: admin/assets/build/dashboard-app.js:10
msgid "LearnDash"
msgstr "LearnDash"

#: assets/svg/logo-svg-icons/icons-v6-3.php:259
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Scroll"
msgstr "Scorrevole"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:657
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:677
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:327
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:347
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Auto"
msgstr "Automatico"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:73
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:49
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Cover"
msgstr "Copertina"

#: inc/compatibility/learndash/customizer/sections/layout/class-astra-learndash-general-configs.php:58
msgid "Differentiate Rows"
msgstr "Differenziare le righe"

#: inc/compatibility/learndash/customizer/sections/layout/class-astra-learndash-general-configs.php:41
msgid "Display Serial Number"
msgstr "Mostra numero di serie"

#: inc/compatibility/learndash/customizer/sections/layout/class-astra-learndash-general-configs.php:45
msgid "Course Content Table"
msgstr "Tabella contenuto corso"

#: admin/includes/class-astra-menu.php:746
msgid "Supercharge your LearnDash website with amazing design features."
msgstr "Potenzia il tuo sito LearnDash con straordinarie funzionalità di design."

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:182
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:236
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:480
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:696
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:911
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:77
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:227
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:227
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:127
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:230
#: inc/customizer/configurations/builder/header/configs/above-header.php:75
#: inc/customizer/configurations/builder/header/configs/below-header.php:75
#: inc/customizer/configurations/builder/header/configs/menu.php:387
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:166
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:188
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:247
#: inc/customizer/configurations/builder/header/configs/primary-header.php:92
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:813
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:920
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:163
#: inc/customizer/configurations/colors-background/class-astra-footer-colors-configs.php:86
#: inc/metabox/class-astra-meta-boxes.php:628
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:611
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1073
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:363
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Background"
msgstr "Sfondo"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:463
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:673
#: admin/assets/build/dashboard-app.js:11
msgid "Learn More"
msgstr "Leggi di più"

#: admin/includes/class-astra-admin-ajax.php:403
msgid "Plugin Successfully Activated"
msgstr "Plugin correttamente attivato"

#: admin/includes/class-astra-admin-ajax.php:369
#: admin/includes/class-astra-admin-ajax.php:440
msgid "No plugin specified"
msgstr "Nessun plug-in specificato"

#: admin/includes/class-astra-menu.php:773
#: admin/assets/build/dashboard-app.js:10
msgid "White Label"
msgstr "Etichetta bianca"

#: admin/includes/class-astra-menu.php:647
#: admin/assets/build/dashboard-app.js:10
msgid "Site Layouts"
msgstr "Layout Sito"

#: admin/includes/class-astra-menu.php:687
msgid "Add content conditionally in the various hook areas of the theme."
msgstr "Aggiunta di contenuti condivisi nelle varie aree dei ganci del tema."

#: admin/includes/class-astra-menu.php:194
#: admin/includes/class-astra-menu.php:195
#: admin/includes/class-astra-menu.php:686
msgid "Custom Layouts"
msgstr "Layout Personalizzati"

#: admin/includes/class-astra-menu.php:702
msgid "Make your header layouts look more appealing and sexy!"
msgstr "Rendi le tue intestazioni più attraenti e sexy!"

#: admin/includes/class-astra-menu.php:701
msgid "Page Headers"
msgstr "Intestazioni Pagina"

#: admin/includes/class-astra-menu.php:621
msgid "Header Sections"
msgstr "Sezioni Header"

#: admin/includes/class-astra-menu.php:595
msgid "Blog Pro"
msgstr "Blog PRO"

#: admin/includes/class-astra-menu.php:582
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:321
#: inc/customizer/class-astra-extended-base-configuration.php:59
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:173
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:485
#: inc/customizer/configurations/builder/footer/configs/copyright.php:115
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:236
#: inc/customizer/configurations/builder/header/configs/account.php:584
#: inc/customizer/configurations/builder/header/configs/menu.php:618
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:405
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:287
#: inc/customizer/configurations/builder/header/configs/search.php:195
#: inc/customizer/configurations/builder/header/configs/site-identity.php:114
#: admin/assets/build/dashboard-app.js:10
msgid "Spacing"
msgstr "Spaziatura"

#: admin/includes/class-astra-menu.php:382
msgid "Footer Settings"
msgstr "Impostazioni Footer"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:174
msgid "Blog Layout"
msgstr "Layout Blog"

#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:66
msgid "Theme Color"
msgstr "Colore tema"

#: inc/customizer/astra-pro/class-astra-pro-upgrade-link-configs.php:31
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:463
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:673
msgid "More Options Available in Astra Pro!"
msgstr "Ottieni di più con Astra Pro!"

#: inc/core/class-astra-admin-settings.php:468
#: inc/core/class-astra-admin-settings.php:480
msgid "Activating Importer Plugin "
msgstr "Attivazione del Plug-in Importatore "

#: admin/includes/class-astra-menu.php:634
#: inc/customizer/configurations/builder/header/configs/header-builder.php:740
#: inc/metabox/class-astra-elementor-editor-settings.php:463
#: inc/metabox/class-astra-meta-boxes.php:652
msgid "Sticky Header"
msgstr "Header fisso"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:425
msgid "Scroll To Top"
msgstr "Torna in alto"

#: inc/addons/transparent-header/classes/class-astra-ext-transparent-header-markup.php:335
#: inc/addons/transparent-header/classes/class-astra-transparent-header-panels-and-sections.php:45
#: inc/customizer/configurations/builder/header/configs/header-builder.php:630
#: inc/metabox/class-astra-elementor-editor-settings.php:450
#: inc/metabox/class-astra-meta-boxes.php:654
msgid "Transparent Header"
msgstr "Header trasparente"

#: inc/lib/bsf-analytics/class-bsf-analytics.php:467
msgid "Learn More."
msgstr "Approfondisci."

#: inc/customizer/class-astra-customizer.php:1817
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:72
msgid "Default System Font"
msgstr "Font di sistema predefinito"

#: inc/compatibility/lifterlms/customizer/sections/layout/class-astra-lifter-general-configs.php:69
msgid "Course Columns"
msgstr "Colonne Corso"

#: inc/compatibility/lifterlms/customizer/sections/layout/class-astra-lifter-general-configs.php:96
msgid "Membership Columns"
msgstr "Colonne Abbonamento"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:275
msgid "Shop Archive Content Width"
msgstr "Larghezza contenitore archivio negozio"

#: inc/compatibility/lifterlms/customizer/class-astra-liferlms-section-configs.php:36
#: inc/customizer/configurations/builder/header/configs/account.php:44
#: admin/assets/build/dashboard-app.js:10
msgid "LifterLMS"
msgstr "LifterLMS"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:317
msgid "Leave Review"
msgstr "Lascia recensione"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:313
msgid "Review Text is required."
msgstr "Il testo della recensione è richiesto."

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:312
msgid "Review Text"
msgstr "Testo recensione"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:311
msgid "Review Title is required."
msgstr "Il titolo della recensione è richiesto."

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:310
msgid "Review Title"
msgstr "Titolo recensione"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:309
msgid "Write a Review"
msgstr "Scrivi una recensione"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:302
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:320
msgid "Thank you for your review!"
msgstr "Grazie per la tua recensione!"

#. translators: 1 Author Name.
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:265
msgid "By: %s"
msgstr "Da: %s"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:234
msgid "What Others Have Said"
msgstr "Quello che altri hanno detto"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:109
msgid "Sidebar width will apply only when one of the above sidebar is set."
msgstr "La larghezza della barra laterale si applica solo quando è impostata una barra laterale sopra."

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:199
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:220
msgid "Cart Page"
msgstr "Pagina Carrello"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:373
msgid "Inline Logo & Site Title"
msgstr "Logo in linea e Titolo sito"

#: inc/core/class-astra-admin-settings.php:148
#: admin/assets/build/dashboard-app.js:15
msgid "Settings"
msgstr "Impostazioni"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2310
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:1000
#: admin/assets/build/dashboard-app.js:10
msgid "Blog"
msgstr "Blog"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:93
#: inc/customizer/class-astra-customizer-register-sections-panels.php:119
#: inc/customizer/configurations/builder/header/configs/primary-header.php:41
msgid "Primary Header"
msgstr "Header Principale"

#: inc/compatibility/woocommerce/woocommerce-common-functions.php:134
msgid "Availability:"
msgstr "Disponibilità:"

#: inc/compatibility/woocommerce/woocommerce-common-functions.php:81
msgid "Out of stock"
msgstr "Esaurito"

#: inc/compatibility/edd/class-astra-edd.php:859
#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:98
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:53
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:60
msgid "Add To Cart"
msgstr "Aggiungi al carrello"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:97
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:178
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:119
msgid "Short Description"
msgstr "Breve descrizione"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:64
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:115
msgid "Ratings"
msgstr "Valutazioni"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:96
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:177
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:114
msgid "Price"
msgstr "Prezzo"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:95
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:176
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:113
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:264
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:251
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:531
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:134
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:672
msgid "Title"
msgstr "Titolo"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:253
msgid "Products Per Page"
msgstr "Prodotti per Pagina"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:237
msgid "Shop Columns"
msgstr "Colonne Negozio"

#: inc/metabox/class-astra-meta-boxes.php:417
#: inc/metabox/class-astra-meta-boxes.php:927
msgid "Disable Breadcrumb"
msgstr "Disabilita Breadcrumb"

#: inc/compatibility/edd/class-astra-edd.php:101
#: inc/compatibility/woocommerce/class-astra-woocommerce.php:557
#: inc/compatibility/woocommerce/customizer/class-astra-customizer-register-woo-section.php:52
#: inc/core/builder/class-astra-builder-helper.php:841
#: inc/core/builder/class-astra-builder-helper.php:851
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:66
msgid "Cart"
msgstr "Carrello"

#: inc/compatibility/edd/customizer/class-astra-customizer-register-edd-section.php:60
#: inc/compatibility/woocommerce/customizer/class-astra-customizer-register-woo-section.php:44
msgid "Single Product"
msgstr "Singolo Prodotto"

#: assets/svg/logo-svg-icons/icons-v6-3.php:621
#: inc/compatibility/woocommerce/customizer/class-astra-customizer-register-woo-section.php:35
msgid "Shop"
msgstr "Negozio"

#: inc/compatibility/edd/customizer/class-astra-customizer-register-edd-section.php:43
#: inc/compatibility/learndash/customizer/class-astra-customizer-register-learndash-section.php:43
#: inc/compatibility/lifterlms/customizer/class-astra-liferlms-section-configs.php:45
#: inc/compatibility/woocommerce/customizer/class-astra-customizer-register-woo-section.php:59
#: inc/customizer/class-astra-customizer-register-sections-panels.php:203
#: admin/assets/build/dashboard-app.js:12
msgid "General"
msgstr "Generale"

#: inc/customizer/configurations/builder/header/configs/account.php:48
#: admin/assets/build/dashboard-app.js:10
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/compatibility/edd/class-astra-edd.php:439
msgid "View your shopping cart"
msgstr "Visualizza il tuo carrello"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:1210
msgid "This sidebar will be used on Single Product page."
msgstr "Questa barra laterale verrà utilizzata nella pagina del singolo prodotto."

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:1208
msgid "Product Sidebar"
msgstr "Barra laterale prodotto"

#: inc/compatibility/edd/class-astra-edd.php:921
#: inc/compatibility/woocommerce/class-astra-woocommerce.php:360
#: inc/compatibility/woocommerce/class-astra-woocommerce.php:1196
msgid "This sidebar will be used on Product archive, Cart, Checkout and My Account pages."
msgstr "Questa barra laterale verrà utilizzata nelle pagine dell'archivio prodotti, carrello, cassa ed il mio account."

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:358
#: inc/compatibility/woocommerce/class-astra-woocommerce.php:1194
msgid "WooCommerce Sidebar"
msgstr "Barra laterale WooCommerce"

#: inc/core/common-functions.php:1071
msgid "This page doesn't seem to exist."
msgstr "Questa pagina non sembra esistere."

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:403
#: inc/core/common-functions.php:1065
msgid "Home"
msgstr "Home"

#: inc/metabox/class-astra-meta-boxes.php:425
msgid "Disable Featured Image"
msgstr "Disabilita l’immagine in evidenza"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:297
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:129
msgid "Retina Logo"
msgstr "Logo retina"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:324
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:313
msgid "Logo Width"
msgstr "Larghezza Logo"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:793
#: inc/metabox/class-astra-meta-boxes.php:456
msgid "Disable Footer Widgets"
msgstr "Disabilita i widget del Footer"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:294
msgid "Paragraph Margin Bottom"
msgstr "Margine basso paragrafi"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:248
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:357
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:41
msgid "Featured Image"
msgstr "Immagine in evidenza"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:92
msgid "Single Product Structure"
msgstr "Struttura prodotto singolo"

#. translators: %s: Name of current post.
#: template-parts/content.php:69
msgid "Continue reading %s"
msgstr "Continua a leggere %s"

#: inc/widgets.php:173
msgid "Footer Widget Area 4"
msgstr "Widget Footer Area 4"

#: inc/widgets.php:164
msgid "Footer Widget Area 3"
msgstr "Widget Footer Area 3"

#: inc/widgets.php:146
msgid "Footer Widget Area 1"
msgstr "Widget Footer Area 1"

#: inc/markup-extras.php:1853
msgid "Click here to assign a widget for this area."
msgstr "Clicca qui per assegnare un widget a quest'area."

#: inc/class-astra-global-palette.php:298
#: inc/customizer/class-astra-customizer-register-sections-panels.php:348
#: inc/customizer/class-astra-customizer.php:1707
msgid "Headings"
msgstr "Intestazioni"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:838
msgid "Full Width / Stretched"
msgstr "Larghezza piena / esteso"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:837
msgid "Full Width / Contained"
msgstr "Larghezza piena / contenuto"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:443
msgid "Layout 4"
msgstr "Layout 4"

#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:108
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:439
msgid "Disable"
msgstr "Disabilita"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1087
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1118
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:121
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:321
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:230
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:737
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:85
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:311
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:667
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1133
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:407
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:956
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1040
msgid "Link Color"
msgstr "Colore link"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:339
msgid "Base Typography"
msgstr "Base tipografia"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:297
msgid "Base Colors"
msgstr "Base colori"

#: inc/widgets.php:155
msgid "Footer Widget Area 2"
msgstr "Widget Footer Area 2"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:44
msgid "Default Layout"
msgstr "Layout predefinito"

#: inc/template-parts.php:168
msgid "Main Menu"
msgstr "Menu principale"

#. translators: 1: Post type label
#: inc/core/class-theme-strings.php:78 inc/core/class-theme-strings.php:93
msgid "Next %s"
msgstr "%s successivo"

#. translators: 1: Post type label
#: inc/core/class-theme-strings.php:80 inc/core/class-theme-strings.php:95
msgid "Previous %s"
msgstr "%s precedente "

#: inc/customizer/class-astra-customizer-register-sections-panels.php:174
msgid "Footer Bar"
msgstr "Barra del footer"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:51
msgid "Footer Bar Layout 1"
msgstr "Layout 1 barra del footer"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:55
msgid "Footer Bar Layout 2"
msgstr "Layout 2 barra del footer"

#: inc/addons/transparent-header/classes/class-astra-ext-transparent-header-markup.php:338
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:822
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:834
#: inc/metabox/class-astra-meta-boxes.php:290
#: inc/metabox/class-astra-meta-boxes.php:308
#: inc/metabox/class-astra-meta-boxes.php:323
#: inc/metabox/class-astra-meta-boxes.php:339
#: inc/metabox/class-astra-meta-boxes.php:779
#: inc/metabox/class-astra-meta-boxes.php:815
#: inc/metabox/class-astra-meta-boxes.php:821
msgid "Customizer Setting"
msgstr "Impostazioni di personalizzazione"

#: inc/widgets.php:121
msgid "Footer Bar Section 1"
msgstr "Sezione 1 barra del footer"

#: inc/widgets.php:130
msgid "Footer Bar Section 2"
msgstr "Sezione 2 barra del footer"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3341
msgid "Pager"
msgstr "Cercapersone"

#: inc/customizer/class-astra-customizer.php:623
#: inc/customizer/class-astra-font-families.php:133
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:136
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:127
msgid "Ultra-Bold 900"
msgstr "Ultra-Bold 900"

#: inc/customizer/class-astra-customizer.php:621
#: inc/customizer/class-astra-font-families.php:132
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:134
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:125
msgid "Extra-Bold 800"
msgstr "Extra-Bold 800"

#: inc/customizer/class-astra-customizer.php:619
#: inc/customizer/class-astra-font-families.php:131
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:132
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:123
msgid "Bold 700"
msgstr "Bold 700"

#: inc/customizer/class-astra-customizer.php:617
#: inc/customizer/class-astra-font-families.php:130
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:130
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:121
msgid "Semi-Bold 600"
msgstr "Semi-Bold 600"

#: inc/customizer/class-astra-customizer.php:615
#: inc/customizer/class-astra-font-families.php:129
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:128
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:119
msgid "Medium 500"
msgstr "Medio 500"

#: inc/customizer/class-astra-customizer.php:612
#: inc/customizer/class-astra-customizer.php:613
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:125
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:126
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:116
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:117
msgid "Normal 400"
msgstr "Normale 400"

#: inc/customizer/class-astra-customizer.php:610
#: inc/customizer/class-astra-font-families.php:127
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:123
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:114
msgid "Light 300"
msgstr "Leggero 300"

#: inc/customizer/class-astra-customizer.php:608
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:121
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:112
msgid "Extra-Light 200"
msgstr "Ultra-leggero 200"

#: inc/customizer/class-astra-customizer.php:606
#: inc/customizer/class-astra-font-families.php:125
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:119
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:110
msgid "Thin 100"
msgstr "Sottile 100"

#. translators: %s: Name of current post
#: inc/template-tags.php:35 template-parts/content-page.php:34
#: template-parts/single/single-layout.php:53
msgid "Edit %s"
msgstr "Modifica %s"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:784
#: inc/metabox/class-astra-meta-boxes.php:407
msgid "Disable Title"
msgstr "Disabilita Titolo"

#: inc/metabox/class-astra-meta-boxes.php:351
#: inc/metabox/class-astra-meta-boxes.php:649
msgid "Disable Sections"
msgstr "Disabilita Sezione"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:810
#: inc/metabox/class-astra-elementor-editor-settings.php:282
msgid "Astra Settings"
msgstr "Impostazioni Astra"

#: inc/markup-extras.php:1813
msgid "Add Widget"
msgstr "Aggiungi Widget"

#: inc/builder/type/header/account/class-astra-header-account-component.php:63
#: inc/builder/type/header/account/class-astra-header-account-component.php:148
#: inc/builder/type/header/mobile-menu/class-astra-mobile-menu-component.php:131
#: inc/markup-extras.php:1335 inc/markup-extras.php:1371
msgid "Site Navigation"
msgstr "Navigazione sito"

#: inc/markup-extras.php:1115
msgid "Assign Footer Menu"
msgstr "Assegna Menu Footer"

#: inc/markup-extras.php:908
msgid "Add Custom HTML"
msgstr "Aggiungi HTML personalizzato"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:398
msgid "Site Title"
msgstr "Titolo del sito"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-typo-configs.php:154
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:317
msgid "Line Height"
msgstr "Altezza di linea"

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:301
msgid "Lowercase"
msgstr "Minuscolo"

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:300
msgid "Uppercase"
msgstr "Maiuscolo"

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:297
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:108
#: inc/metabox/class-astra-meta-boxes.php:997
#: inc/metabox/class-astra-meta-boxes.php:1010
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Inherit"
msgstr "Eredita"

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:290
msgid "Text Transform"
msgstr "Trasformazione testo"

#: inc/customizer/configurations/layout/class-astra-site-layout-configs.php:39
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:340
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:462
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:145
msgid "Container Width"
msgstr "Larghezza Contenitore"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:185
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:169
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:169
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:81
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:168
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:413
msgid "Stack"
msgstr "Pila"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:184
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:170
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:170
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:80
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:169
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:409
msgid "Inline"
msgstr "In linea"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:367
#: inc/customizer/configurations/builder/header/configs/above-header.php:92
#: inc/customizer/configurations/builder/header/configs/below-header.php:92
#: inc/customizer/configurations/builder/header/configs/primary-header.php:107
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:117
msgid "Bottom Border Color"
msgstr "Colore bordo basso"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:344
#: inc/customizer/configurations/builder/header/configs/above-header.php:110
#: inc/customizer/configurations/builder/header/configs/below-header.php:110
#: inc/customizer/configurations/builder/header/configs/primary-header.php:127
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:90
msgid "Bottom Border Size"
msgstr "Dimensione bordo basso"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:281
msgid "Custom Menu Text / HTML"
msgstr "Testo menu personalizzato / HTML"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:149
msgid "Text / HTML"
msgstr "Testo / HTML"

#: inc/core/builder/class-astra-builder-helper.php:693
#: inc/core/builder/class-astra-builder-helper.php:869
#: inc/customizer/configurations/builder/header/configs/search.php:32
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:147
#: inc/markup-extras.php:876 searchform.php:31 searchform.php:37
#: admin/assets/build/dashboard-app.js:15
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Search"
msgstr "Cerca"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:126
msgid "Disable Menu"
msgstr "Disabilita menu"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:55
msgid "Logo Right"
msgstr "Logo destra"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:51
msgid "Logo Center"
msgstr "Logo centrato"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:47
msgid "Logo Left"
msgstr "Logo sinistra"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:108
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:108
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:108
#: inc/customizer/configurations/builder/header/configs/header-builder.php:648
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:103
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:45
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:358
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:74
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:184
msgid "Content Width"
msgstr "Larghezza contenuto"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:54
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:54
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:62
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:54
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:107
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:107
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:107
#: inc/customizer/configurations/builder/header/configs/header-builder.php:647
#: inc/customizer/configurations/comments/class-astra-comments-configs.php:93
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:357
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:73
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:61
#: inc/metabox/class-astra-meta-boxes.php:295
#: inc/metabox/class-astra-meta-boxes.php:817
#: inc/metabox/class-astra-meta-boxes.php:824
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:49
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:64
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:342
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:49
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:64
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:464
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:590
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:147
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:658
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:767
msgid "Full Width"
msgstr "Larghezza piena"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:188
msgid "Section 2 Custom Text"
msgstr "Testo personalizzato Sezione 2"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:154
msgid "Section 2"
msgstr "Sezione 2"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:122
msgid "Section 1 Custom Text"
msgstr "Testo personalizzato Sezione 1"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:88
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:126
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:158
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:150
msgid "Widget"
msgstr "Widget"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:125
msgid "Custom Text"
msgstr "Testo personalizzato"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:41
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:49
#: inc/customizer/configurations/builder/header/configs/account.php:265
#: inc/customizer/configurations/builder/header/configs/menu.php:95
#: inc/customizer/configurations/builder/header/configs/menu.php:156
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:298
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:456
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:611
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:86
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:124
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:156
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:146
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:314
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:221
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:227
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:551
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:570
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:953
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:350
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:231
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "None"
msgstr "Nessuno"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:83
msgid "Section 1"
msgstr "Sezione 1"

#: inc/addons/transparent-header/classes/class-astra-ext-transparent-header-markup.php:340
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:47
#: inc/metabox/class-astra-meta-boxes.php:999
msgid "Disabled"
msgstr "Disabilitato"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:831
#: inc/metabox/class-astra-meta-boxes.php:648
msgid "Content Layout"
msgstr "Layout contenuto"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:156
msgid "Full Content"
msgstr "Pieno Contenuto"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:204
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:278
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:31
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:38
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:86
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:106
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:354
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:48
#: inc/extras.php:1225
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:222
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:228
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:343
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:187
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:465
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:610
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:148
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:351
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:281
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Custom"
msgstr "Personalizzato"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2678
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:76
msgid "Tag"
msgstr "Tag"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:694
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:334
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:62
msgid "Author"
msgstr "Autore"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:94
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:182
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:121
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:55
msgid "Category"
msgstr "Categoria"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5818
#: inc/customizer/configurations/comments/class-astra-comments-configs.php:38
#: inc/customizer/configurations/comments/class-astra-comments-configs.php:49
#: inc/customizer/configurations/comments/class-astra-comments-configs.php:61
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:692
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:826
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:261
msgid "Comments"
msgstr "Commenti"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:682
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1148
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:418
msgid "Link Hover Color"
msgstr "Colore Link Hover"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:954
msgid "Content"
msgstr "Contenuto"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:33
#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:46
#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:74
#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:47
#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:79
#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:203
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:46
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:74
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:47
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:79
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:54
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:82
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:68
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:100
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:123
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:155
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:46
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:74
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:46
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:78
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:277
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:331
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:355
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:474
#: inc/customizer/configurations/builder/header/configs/account.php:24
#: inc/customizer/configurations/builder/header/configs/account.php:170
#: inc/customizer/configurations/builder/header/configs/account.php:293
#: inc/customizer/configurations/comments/class-astra-comments-configs.php:74
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:105
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:329
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:561
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:584
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:632
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:654
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:47
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:92
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:119
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:253
#: inc/metabox/class-astra-meta-boxes.php:834
#: inc/metabox/class-astra-meta-boxes.php:846
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:37
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:56
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:130
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:156
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:186
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:37
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:56
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:111
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:136
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:166
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:186
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:797
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:926
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1600
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:646
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:675
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:697
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:724
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:454
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:473
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:492
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:711
msgid "Default"
msgstr "Predefinito"

#: admin/includes/class-astra-menu.php:390
#: admin/includes/class-astra-menu.php:569
#: inc/customizer/class-astra-customizer-register-sections-panels.php:317
#: inc/customizer/class-astra-customizer.php:1703
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:335
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:194
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:588
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:604
#: admin/assets/build/dashboard-app.js:10
msgid "Typography"
msgstr "Tipografia"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:216
msgid "Single Post"
msgstr "Articolo singolo"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:209
msgid "Blog / Archive"
msgstr "Blog / Archivio"

#: inc/addons/transparent-header/classes/class-astra-transparent-header-panels-and-sections.php:54
#: inc/customizer/class-astra-customizer-register-sections-panels.php:80
#: inc/customizer/configurations/builder/header/configs/primary-header.php:34
#: inc/widgets.php:107 admin/assets/build/dashboard-app.js:10
#: admin/assets/theme-builder/build/index.js:60752
#: admin/assets/theme-builder/build/index.js:61181
msgid "Header"
msgstr "Header"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:47
#: inc/metabox/class-astra-elementor-editor-settings.php:299
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Container"
msgstr "Contenitore"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:96
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:316
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:134
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:112
#: inc/customizer/configurations/builder/footer/configs/copyright.php:103
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:214
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:665
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:412
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:708
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:725
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:131
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:148
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:123
#: inc/customizer/configurations/colors-background/class-astra-footer-colors-configs.php:41
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:651
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1117
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:395
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:941
msgid "Text Color"
msgstr "Colore del testo"

#: admin/includes/class-astra-menu.php:402
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:82
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:82
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:70
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:82
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:193
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:44
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:434
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:40
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:420
msgid "Layout"
msgstr "Layout"

#: inc/customizer/class-astra-customizer-sanitizes.php:467
msgid "Enter valid email address!"
msgstr "Inserire un indirizzo email valido!"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6181
msgid "Y"
msgstr "Y"

#: admin/includes/class-astra-menu.php:180
#: admin/includes/class-astra-menu.php:181
#: inc/compatibility/woocommerce/class-astra-woocommerce.php:3592
#: admin/assets/build/dashboard-app.js:1
msgid "Customize"
msgstr "Personalizza"

#: inc/core/class-theme-strings.php:65 inc/core/class-theme-strings.php:76
msgid "Pages:"
msgstr "Pagine:"

#: inc/core/class-theme-strings.php:60
msgid "Comment navigation"
msgstr "Navigazione commenti"

#: inc/core/class-theme-strings.php:59
msgid "Comments are closed."
msgstr "I commenti sono chiusi."

#: inc/core/class-theme-strings.php:55
msgid "Type here.."
msgstr "Scrivi qui.."

#: inc/core/class-theme-strings.php:29
msgid "Post Comment &raquo;"
msgstr "Commento Articolo &raquo;"

#: inc/core/class-theme-strings.php:53
msgid "Cancel Reply"
msgstr "Annulla risposta"

#: inc/core/class-theme-strings.php:52 inc/core/class-theme-strings.php:67
msgid "Leave a Comment"
msgstr "Lascia un commento"

#: inc/core/class-theme-strings.php:51
msgid "Your comment is awaiting moderation."
msgstr "Il tuo commento è in attesa di moderazione."

#: inc/core/class-theme-strings.php:50
msgid "Edit"
msgstr "Modifica"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5771
#: inc/core/class-theme-strings.php:49
msgid "Reply"
msgstr "Rispondi"

#: inc/core/class-theme-strings.php:43
msgid "Start typing and press enter to search"
msgstr "Inizia a scrivere e premi Invio per cercare"

#: inc/core/class-theme-strings.php:41
msgid "Nothing Found"
msgstr "Nessun risultato trovato"

#: inc/core/class-theme-strings.php:38
msgid "It looks like the link pointing here was faulty. Maybe try searching?"
msgstr "Sembra che il link che stai cercando di raggiungere non funzioni. Puoi provare a ricercare?"

#: inc/core/class-theme-strings.php:35
msgid "Skip to content"
msgstr "Vai al contenuto"

#: inc/core/class-astra-theme-options.php:483
msgid "Contact Us"
msgstr "Contattaci"

#: inc/core/class-astra-theme-options.php:439
#: inc/core/class-astra-theme-options.php:441
msgid "Copyright &copy; [current_year] [site_title] | Powered by [theme_author]"
msgstr "Copyright &copy; [current_year] [site_title] | Powered by [theme_author]"

#: inc/compatibility/class-astra-beaver-themer.php:389
msgid "Loop End"
msgstr "Fine ciclo"

#: inc/compatibility/class-astra-beaver-themer.php:388
msgid "After Comments"
msgstr "Dopo i commenti"

#: inc/compatibility/class-astra-beaver-themer.php:387
msgid "Before Comments"
msgstr "Prima dei commenti"

#: inc/compatibility/class-astra-beaver-themer.php:386
msgid "After Post"
msgstr "Dopo il post"

#: inc/compatibility/class-astra-beaver-themer.php:385
msgid "After Post Content"
msgstr "Dopo il contenuto del post"

#: inc/compatibility/class-astra-beaver-themer.php:384
msgid "Before Post Content"
msgstr "Prima del contenuto del post"

#: inc/compatibility/class-astra-beaver-themer.php:383
msgid "Before Post"
msgstr "Prima del post"

#: inc/compatibility/class-astra-beaver-themer.php:382
msgid "Loop Start"
msgstr "Inizio ciclo"

#: inc/compatibility/class-astra-beaver-themer.php:362
msgid "After Content"
msgstr "Dopo il contenuto"

#: inc/compatibility/class-astra-beaver-themer.php:361
msgid "Before Content"
msgstr "Prima del contenuto"

#: inc/compatibility/class-astra-beaver-themer.php:348
msgid "After Page"
msgstr "Dopo la pagina"

#: inc/compatibility/class-astra-beaver-themer.php:347
msgid "Before Page"
msgstr "Prima della pagina"

#: inc/blog/single-blog.php:104
msgid "(Edit)"
msgstr "(Modifica)"

#: inc/blog/single-blog.php:104
msgid "Pingback:"
msgstr "Pingback:"

#: inc/blog/blog-config.php:739
msgid "Read More &raquo;"
msgstr "Leggi altro &raquo;"

#. translators: 1: number of comments
#: comments.php:58
msgctxt "comments title"
msgid "%1$s thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] "%1$s commento su &ldquo;%2$s&rdquo;"
msgstr[1] "%1$s commenti su &ldquo;%2$s&rdquo;"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:147
#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:138
#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:171
#: admin/assets/build/dashboard-app.js:10
#: admin/assets/theme-builder/build/index.js:60795
#: admin/assets/theme-builder/build/index.js:61188
msgid "Footer"
msgstr "Footer"

#: inc/core/class-theme-strings.php:66
msgid "By "
msgstr "Di "

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:157
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:277
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:533
msgid "Excerpt"
msgstr "Riassunto"

#: inc/core/class-theme-strings.php:42
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Non c’è nessuna corrispondenza con i termini di ricerca che hai indicato. Riprova con termini diversi."

#: inc/core/class-theme-strings.php:61
msgid "Newer Comments"
msgstr "Commenti più recenti"

#: inc/core/class-theme-strings.php:69
msgid "% Comments"
msgstr "% commenti"

#: comments.php:71 comments.php:94
msgid "Comments Navigation"
msgstr "Navigazione commenti"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:51
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:51
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:825
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:72
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:127
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:50
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:47
#: inc/metabox/class-astra-meta-boxes.php:326
#: inc/metabox/class-astra-meta-boxes.php:780
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:160
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:140
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:701
msgid "No Sidebar"
msgstr "Nessuna barra laterale"

#. translators: 1: link to new post
#: template-parts/content-none.php:23
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr "Pronto a pubblicare il tuo primo articolo? <a href=\"%1$s\">Inizia da qui</a>."

#: inc/core/class-theme-strings.php:58
msgid "Website"
msgstr "Sito web"

#: inc/core/class-theme-strings.php:62
msgid "Older Comments"
msgstr "Commenti meno recenti"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:819
#: inc/customizer/class-astra-customizer-register-sections-panels.php:238
#: inc/metabox/class-astra-elementor-editor-settings.php:346
#: inc/metabox/class-astra-meta-boxes.php:644
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Sidebar"
msgstr "Barra laterale"

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:299
msgid "Capitalize"
msgstr "Iniziali maiuscole"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:75
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:159
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1072
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1099
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1132
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:340
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:147
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:215
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:245
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:181
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:677
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:426
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:742
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:759
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:165
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:182
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:39
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:266
msgid "Background Color"
msgstr "Colore di sfondo"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:76
#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:81
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:76
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:81
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:835
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:84
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:102
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:157
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:76
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:80
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:74
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:82
#: inc/metabox/class-astra-meta-boxes.php:310
#: inc/metabox/class-astra-meta-boxes.php:341
#: inc/metabox/class-astra-meta-boxes.php:836
#: inc/metabox/class-astra-meta-boxes.php:848
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:132
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:188
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:113
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:168
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:677
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:726
msgid "Boxed"
msgstr "Boxed"

#: inc/admin-functions.php:30
#: inc/customizer/class-astra-customizer-register-sections-panels.php:141
#: inc/customizer/configurations/builder/header/configs/header-builder.php:112
#: inc/customizer/configurations/builder/header/configs/menu.php:32
msgid "Primary Menu"
msgstr "Menu principale"

#: inc/core/class-theme-strings.php:83
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "Non riusciamo a trovare quello che cerchi. Forse eseguire una ricerca potrebbe essere di aiuto."

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-typo-configs.php:115
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:166
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:206
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:391
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:85
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:129
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:391
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:444
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:322
#: inc/customizer/configurations/builder/header/configs/menu.php:566
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:355
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:358
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:387
#: inc/customizer/configurations/builder/header/configs/site-identity.php:162
#: inc/customizer/configurations/builder/header/configs/site-identity.php:197
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:235
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:138
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:88
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:178
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:268
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:358
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:447
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:536
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:765
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:850
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1231
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1316
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1421
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:487
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:556
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1103
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1188
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1273
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1357
msgid "Font Size"
msgstr "Dimensione del font"

#: inc/admin-functions.php:77
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:42
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:89
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:127
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:159
msgid "Footer Menu"
msgstr "Menu del footer"

#: inc/compatibility/class-astra-beaver-themer.php:376
msgid "After Sidebar"
msgstr "Dopo la barra laterale"

#: inc/compatibility/class-astra-beaver-themer.php:375
msgid "Before Sidebar"
msgstr "Prima della barra laterale"

#: inc/compatibility/class-astra-beaver-themer.php:369
msgid "After Footer"
msgstr "Dopo il footer"

#: inc/compatibility/class-astra-beaver-themer.php:368
msgid "Before Footer"
msgstr "Prima del footer"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:43
#: inc/compatibility/class-astra-beaver-themer.php:355
msgid "After Header"
msgstr "Dopo l'header"

#: inc/compatibility/class-astra-beaver-themer.php:354
msgid "Before Header"
msgstr "Prima dell'header"

#: inc/widgets.php:93
msgid "Main Sidebar"
msgstr "Barra laterale principale"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:91
msgid "Sidebar Width"
msgstr "Larghezza barra laterale"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:59
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:59
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:824
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:80
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:135
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:58
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:55
#: inc/metabox/class-astra-meta-boxes.php:325
#: inc/metabox/class-astra-meta-boxes.php:782
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:168
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:148
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:709
msgid "Right Sidebar"
msgstr "Barra laterale destra"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:55
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:55
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:823
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:76
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:131
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:54
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:51
#: inc/metabox/class-astra-meta-boxes.php:324
#: inc/metabox/class-astra-meta-boxes.php:781
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:164
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:144
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:705
msgid "Left Sidebar"
msgstr "Barra laterale sinistra"

#: inc/core/class-theme-strings.php:68
msgid "1 Comment"
msgstr "1 commento"

#: admin/includes/class-astra-menu.php:660
#: inc/customizer/class-astra-customizer-register-sections-panels.php:166
#: inc/customizer/class-astra-customizer-register-sections-panels.php:306
msgid "Footer Widgets"
msgstr "Widget del footer"

#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:148
msgid "Footer Layout"
msgstr "Layout del footer"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:688
msgid "Button Color"
msgstr "Colore del pulsante"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:224
msgid "Page"
msgstr "Pagina"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:44
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:44
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:29
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:43
#: inc/metabox/class-astra-elementor-editor-settings.php:356
#: inc/metabox/class-astra-meta-boxes.php:320
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:151
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:132
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:693
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Sidebar Layout"
msgstr "Layout della barra laterale"

#. translators: 1: date, 2: time
#: inc/blog/single-blog.php:163
msgid "%1$s at %2$s"
msgstr "%1$s alle %2$s"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:108
#: inc/customizer/class-astra-customizer-register-sections-panels.php:361
#: inc/customizer/class-astra-customizer.php:1670
msgid "Buttons"
msgstr "Pulsanti"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:746
#: inc/metabox/class-astra-meta-boxes.php:375
#: inc/metabox/class-astra-meta-boxes.php:870
msgid "Disable Primary Header"
msgstr "Disabilita Header principale"

#: admin/includes/class-astra-menu.php:556
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:386
msgid "Colors & Background"
msgstr "Colori e Sfondo"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:836
msgid "Content Boxed"
msgstr "Contenuto boxed"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-typo-configs.php:97
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:128
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:146
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:374
#: inc/customizer/configurations/builder/header/configs/menu.php:551
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:340
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:276
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:122
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:237
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:64
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:154
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:246
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:335
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:425
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:515
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:749
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:833
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1214
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1299
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1405
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:475
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:544
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1086
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1171
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1256
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1341
msgid "Font Weight"
msgstr "Font weight"

#: inc/core/class-astra-theme-options.php:187
msgid "Search Results for:"
msgstr "Risultati della ricerca per:"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-typo-configs.php:79
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:94
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:111
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:356
#: inc/customizer/configurations/builder/header/configs/menu.php:533
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:322
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:223
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:76
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:218
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:47
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:136
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:227
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:316
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:407
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:496
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:732
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:816
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1197
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1282
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1388
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:462
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:531
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1069
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1154
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1239
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1324
msgid "Font Family"
msgstr "Famiglia di font"

#: inc/core/class-theme-strings.php:57
msgid "Email"
msgstr "Email"

#. Author URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wpastra.com/about/?utm_source=theme_preview&utm_medium=author_link&utm_campaign=astra_theme"
msgstr "https://wpastra.com/about/?utm_source=theme_preview&utm_medium=author_link&utm_campaign=astra_theme"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "Brainstorm Force"
msgstr "Brainstorm Force"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wpastra.com/"
msgstr "https://wpastra.com/"