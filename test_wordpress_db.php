<?php
/**
 * <PERSON>ript per testare la connessione WordPress al database
 */

// Includi la configurazione WordPress
require_once 'wp-load.php';

echo "Test connessione database WordPress...\n";

global $wpdb;

if ($wpdb->last_error) {
    echo "✗ Errore di connessione: " . $wpdb->last_error . "\n";
    exit(1);
}

// Test connessione
$result = $wpdb->get_results("SELECT 1 as test");

if ($wpdb->last_error) {
    echo "✗ Errore nel test: " . $wpdb->last_error . "\n";
    exit(1);
}

echo "✓ Connessione al database riuscita!\n";
echo "Database host: " . DB_HOST . "\n";
echo "Database name: " . DB_NAME . "\n"; 
echo "Database user: " . DB_USER . "\n";

// Verifica tabella CSRF
$table_name = 'wpcd_office_addin_csrf_tokens';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");

if ($table_exists) {
    echo "✓ Tabella CSRF '$table_name' trovata!\n";
    
    // Testa inserimento token
    $test_token = 'test_' . time();
    $user_id = 1;
    $expires_at = date('Y-m-d H:i:s', time() + 3600);
    
    $inserted = $wpdb->insert(
        $table_name,
        array(
            'token' => $test_token,
            'user_id' => $user_id,
            'expires_at' => $expires_at
        ),
        array('%s', '%d', '%s')
    );
    
    if ($inserted) {
        echo "✓ Test inserimento token riuscito!\n";
        
        // Rimuovi il token di test
        $wpdb->delete($table_name, array('token' => $test_token), array('%s'));
        echo "✓ Token di test rimosso\n";
    } else {
        echo "✗ Errore nell'inserimento token: " . $wpdb->last_error . "\n";
    }
    
} else {
    echo "✗ Tabella CSRF '$table_name' NON trovata!\n";
}

echo "\nTest completato!\n";
?>
