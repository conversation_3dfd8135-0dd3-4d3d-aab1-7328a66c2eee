<?php
/**
 * Script per creare la tabella CSRF con la struttura corretta
 */

// Configurazione database per Docker
$db_config = [
    'host' => 'wp-db',
    'name' => 'claryfin_db', 
    'user' => 'root',
    'password' => 'rootpass',
    'charset' => 'utf8mb4'
];

echo "Creazione tabella CSRF con struttura corretta...\n";

try {
    // Connessione diretta al database
    $mysqli = new mysqli($db_config['host'], $db_config['user'], $db_config['password'], $db_config['name']);
    
    if ($mysqli->connect_error) {
        throw new Exception("Errore di connessione: " . $mysqli->connect_error);
    }
    
    echo "Connessione riuscita al database: {$db_config['name']}\n";
    
    // Imposta il charset
    $mysqli->set_charset($db_config['charset']);
    
    // SQL per creare la tabella CSRF tokens con la struttura corretta
    $table_sql = "CREATE TABLE IF NOT EXISTS wpcd_office_addin_csrf_tokens (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        token varchar(64) NOT NULL,
        action varchar(50) NOT NULL,
        user_id bigint(20) NOT NULL,
        ip_address varchar(45) NOT NULL,
        user_agent varchar(255) NOT NULL,
        created_at timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY token (token),
        KEY user_action (user_id, action),
        KEY created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    echo "Creazione tabella CSRF tokens...\n";
    
    if ($mysqli->query($table_sql)) {
        echo "✓ Tabella 'wpcd_office_addin_csrf_tokens' creata con successo!\n";
        
        // Verifica che la tabella sia stata creata
        $check_query = "SHOW TABLES LIKE 'wpcd_office_addin_csrf_tokens'";
        $result = $mysqli->query($check_query);
        
        if ($result && $result->num_rows > 0) {
            echo "✓ Verifica: tabella esistente nel database\n";
            
            // Mostra la struttura della tabella
            $desc_result = $mysqli->query("DESCRIBE wpcd_office_addin_csrf_tokens");
            echo "\nStruttura della tabella:\n";
            while ($row = $desc_result->fetch_assoc()) {
                echo "- {$row['Field']}: {$row['Type']}\n";
            }
        } else {
            echo "✗ Errore: tabella non trovata dopo la creazione\n";
        }
        
    } else {
        throw new Exception("Errore nella creazione della tabella: " . $mysqli->error);
    }
    
    $mysqli->close();
    echo "\nTabella CSRF creata con struttura corretta!\n";
    
} catch (Exception $e) {
    echo "✗ Errore: " . $e->getMessage() . "\n";
    exit(1);
}
?>
