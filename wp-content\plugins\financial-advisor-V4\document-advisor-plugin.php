<?php
/**
 * Correzioni apportate (2025-05-19):
 * - Aggiunto hook wp_ajax_nopriv_save_document_analysis per consentire agli utenti non autenticati di salvare le analisi
 * - Aggiunto hook wp_ajax_nopriv_export_analysis_pdf per consentire agli utenti non autenticati di esportare in PDF
 * - Implementata la chiamata a do_action('save_document_analysis', ...) per aggiornare le statistiche utente
 * - Aggiunto calcolo e tracciamento dei token utilizzati per il corretto aggiornamento delle statistiche
 */

// Inizializza la sessione prima di qualsiasi output
if (!session_id() && !headers_sent()) {
    session_start();
}

/**
 * Plugin Name: Financial Advisor Plugin
 * Plugin URI: https://example.com/document-viewer-plugin
 * Description: AI to analyze document and make analysis. Supports widget and shortcode.
 * Version: 1.1
 * Author: Inventyx
 * Author URI: https://inventyx.tech
 */

if (!defined('ABSPATH')) {
    exit;
}

// Initialize security layer transparently (non-invasive)
if (file_exists(plugin_dir_path(__FILE__) . 'security-init.php')) {
    require_once plugin_dir_path(__FILE__) . 'security-init.php';
    // Activate security layer without modifying existing functionality
    office_addin_initialize_security();
}

// Include test security data generator (only in debug mode)
if (defined('WP_DEBUG') && WP_DEBUG && file_exists(plugin_dir_path(__FILE__) . 'test-security-data.php')) {
    require_once plugin_dir_path(__FILE__) . 'test-security-data.php';
}

// Define plugin version constant
if (!defined('DOCUMENT_ADVISOR_VERSION')) {
    define('DOCUMENT_ADVISOR_VERSION', '1.1');
}

// Load payment gateway testing functionality
require_once(plugin_dir_path(__FILE__) . 'includes/payment-gateway-testing.php');

// Includi l'autoloader di Composer
require_once __DIR__ . '/vendor/autoload.php';

// Add debugging constant
if (!defined('DOCUMENT_VIEWER_DEBUG')) {
    define('DOCUMENT_VIEWER_DEBUG', false); // set to true to enable debug logging
}

/**
 * Filtro di fallback per il redirect di login
 *
 * Questo filtro fornisce un modo standardizzato per gestire i redirect dopo il login.
 * Viene utilizzato dai componenti che necessitano di reindirizzare gli utenti dopo l'autenticazione.
 */
add_filter('fa_login_redirect', function($redirect_to) {
    $global_redirect_url = get_option('login_global_redirect_url', '');
    return !empty($global_redirect_url) ? $global_redirect_url : home_url();
});

// Include widget files
require_once plugin_dir_path(__FILE__) . 'includes/widgets/chat-model-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/document-viewer-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/user-subscription-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/login-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/subscriber-management-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/report-formatter-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-report-formatter-themes.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/spreadsheet-analysis-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/word-analysis-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/balance-sheet-analysis-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/report-advanced-analyzer-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/financial-expert-assistant-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/planning-simulation-widget.php';

// Include balance sheet schemas admin
require_once plugin_dir_path(__FILE__) . 'includes/class-balance-sheet-schemas-admin.php';

// Include what-if queries admin
require_once plugin_dir_path(__FILE__) . 'includes/class-whatif-queries-admin.php';

// Include test files (only in debug mode)
if (defined('DOCUMENT_VIEWER_DEBUG') && DOCUMENT_VIEWER_DEBUG) {
    // Note: Test files are not included as they don't exist in production
    // require_once plugin_dir_path(__FILE__) . 'test-report-formatter.php';
    // require_once plugin_dir_path(__FILE__) . 'test-spreadsheet-analysis.php';
    // require_once plugin_dir_path(__FILE__) . 'test-balance-sheet-widget.php';
    // Note: test-planning-simulation.php should be accessed directly, not included here
}

// Include logger class
require_once plugin_dir_path(__FILE__) . 'includes/class-document-viewer-logger.php';

// Include access control class
require_once plugin_dir_path(__FILE__) . 'includes/class-fa-access-control.php';

// Include database setup file
require_once plugin_dir_path(__FILE__) . 'includes/database-setup.php';

// Include access helper functions
require_once plugin_dir_path(__FILE__) . 'includes/access-helper.php';

// Include template functions
require_once plugin_dir_path(__FILE__) . 'includes/template-functions.php';

// Include shortcodes
require_once plugin_dir_path(__FILE__) . 'includes/shortcodes.php';

// Include template loader
require_once plugin_dir_path(__FILE__) . 'includes/template-loader.php';

// Include document management class
require_once plugin_dir_path(__FILE__) . 'includes/class-document-management.php';

// Include document stats class
require_once plugin_dir_path(__FILE__) . 'includes/class-document-stats.php';

// Include Financial Academy Manager class
require_once plugin_dir_path(__FILE__) . 'includes/class-financial-academy-manager.php';

// Include Menu Manager class - NUOVO!
require_once plugin_dir_path(__FILE__) . 'includes/class-menu-manager.php';

// Include Widget Help Migration class FIRST (needed by system)
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-migration.php';

// Include Widget Help Manager class SECOND (new system)
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-manager.php';

// Include Widget Help System class THIRD (legacy compatibility)
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-system.php';

// Include Widget Help Admin class
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-admin.php';

// Include Widget Help Admin Simple class (New Simplified Interface)
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-admin-simple.php';

// Include Widget Help Simple Migration class
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-simple-migration.php';

// Include Widget Help Simple Migration
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-simple-migration.php';

// Include Financial Academy Manager class
require_once plugin_dir_path(__FILE__) . 'includes/class-financial-academy-manager.php';

// Include User Subscription Manager class
require_once plugin_dir_path(__FILE__) . 'includes/class-user-subscription-manager.php';

// Include User Subscription Admin class
require_once plugin_dir_path(__FILE__) . 'includes/class-user-subscription-admin.php';

// Include Payment Gateway Admin class
// Include Enhanced Payment Gateway Test first
require_once plugin_dir_path(__FILE__) . 'admin-payment-gateway-test.php';

// Include Payment Gateway Admin after test file
require_once plugin_dir_path(__FILE__) . 'includes/class-payment-gateway-admin.php';

// Include Office Add-in integration
require_once plugin_dir_path(__FILE__) . 'office-addin.php';

// Include Office Add-in manifest generator
require_once plugin_dir_path(__FILE__) . 'includes/class-office-addin-manifest.php';

// Backward compatibility wrapper functions
function dv_debug_log($msg, $context = 'default') {
    dv_logger()->debug_log($msg, $context);
}

function dv_clear_shared_log($context = 'all') {
    return dv_logger()->clear_shared_log();
}

function dv_get_shared_log($context = 'all') {
    return dv_logger()->get_log_content($context);
}

class Document_Viewer_Plugin {
    private $version = '1.2';
    private $plugin_url = '';
    private $author = 'Your Name';
    private $author_profile = '';
    private $logger;

    public function __construct() {
        // Initialize the logger
        $this->logger = dv_logger();

        // Initialize properties
        $this->plugin_url = plugin_dir_url(__FILE__);
        $this->author_profile = 'https://example.com';

        // Initialize admin classes
        if (is_admin()) {
            whatif_queries_admin();
        }

        // Initialize Planning & Simulation widget help content
        add_action('wp_loaded', array($this, 'initialize_planning_simulation_help'));

        // Add test functionality for Planning & Simulation widget (debug mode only)
        if (defined('DOCUMENT_VIEWER_DEBUG') && DOCUMENT_VIEWER_DEBUG) {
            add_action('wp_loaded', array($this, 'register_planning_simulation_test_page'));
        }

        // Require settings file early
        require_once plugin_dir_path(__FILE__) . 'settings.php';

        // Add shortcodes
        add_shortcode('document_viewer', array($this, 'render_document_viewer'));
        add_shortcode('chat_viewer', array($this, 'render_chat_viewer'));

        // Core actions
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('widgets_init', array($this, 'register_widget'));
        add_action('activate_financial-advisor-V4/document-advisor-plugin.php', array($this, 'create_document_analysis_table'));
        add_action('activate_financial-advisor-V4/document-advisor-plugin.php', array($this, 'create_preset_queries_table'));
        add_action('activate_financial-advisor-V4/document-advisor-plugin.php', array($this, 'initialize_database_tables'));

        // AJAX handlers for document operations
        add_action('wp_ajax_clear_document', array($this, 'clear_document'));
        // Handle document analysis for logged-in users
        add_action('wp_ajax_analyze_document', array($this, 'analyze_document'));
        // Also handle document analysis for non-logged-in users with proper authentication
        add_action('wp_ajax_nopriv_analyze_document', array($this, 'analyze_document'));

        // Handle document text extraction only (without analysis)
        add_action('wp_ajax_extract_document_text', array($this, 'extract_document_text'));
        add_action('wp_ajax_nopriv_extract_document_text', array($this, 'extract_document_text'));

        // Handle balance sheet analysis
        add_action('wp_ajax_analyze_balance_sheet', array($this, 'analyze_balance_sheet'));
        add_action('wp_ajax_nopriv_analyze_balance_sheet', array($this, 'analyze_balance_sheet'));

        // Handle balance sheet analysis saving
        add_action('wp_ajax_save_balance_sheet_analysis', array($this, 'save_balance_sheet_analysis'));
        add_action('wp_ajax_nopriv_save_balance_sheet_analysis', array($this, 'save_balance_sheet_analysis'));

        // Handle balance sheet schema management
        add_action('wp_ajax_save_balance_sheet_schema', array($this, 'handle_save_balance_sheet_schema'));
        add_action('wp_ajax_delete_balance_sheet_schema', array($this, 'handle_delete_balance_sheet_schema'));
        add_action('wp_ajax_get_balance_sheet_schema', array($this, 'handle_get_balance_sheet_schema'));

        // Balance Sheet Analysis export handlers
        add_action('wp_ajax_export_balance_sheet_analysis_pdf', array($this, 'export_balance_sheet_analysis_pdf'));
        add_action('wp_ajax_nopriv_export_balance_sheet_analysis_pdf', array($this, 'export_balance_sheet_analysis_pdf'));
        add_action('wp_ajax_export_balance_sheet_analysis_word', array($this, 'export_balance_sheet_analysis_word'));
        add_action('wp_ajax_nopriv_export_balance_sheet_analysis_word', array($this, 'export_balance_sheet_analysis_word'));

        add_action('wp_ajax_export_analysis_pdf', array($this, 'export_analysis_pdf'));
        add_action('wp_ajax_nopriv_export_analysis_pdf', array($this, 'export_analysis_pdf')); // Aggiungiamo l'hook per gli utenti non loggati
        add_action('wp_ajax_chat_model', array($this, 'chat_model'));
        add_action('wp_ajax_nopriv_chat_model', array($this, 'chat_model')); // Aggiungiamo l'hook per gli utenti non loggati
        add_action('wp_ajax_save_document_analysis', array($this, 'save_document_analysis'));
        add_action('wp_ajax_nopriv_save_document_analysis', array($this, 'save_document_analysis')); // Aggiungiamo l'hook per gli utenti non loggati
        add_action('wp_ajax_convert_word_to_pdf', array($this, 'convert_word_to_pdf'));
        add_action('wp_ajax_nopriv_convert_word_to_pdf', array($this, 'convert_word_to_pdf')); // Aggiungiamo l'hook per gli utenti non loggati

        // NUOVA FUNZIONALITÀ: Handler per recuperare analisi salvate
        add_action('wp_ajax_get_saved_analyses', array($this, 'handle_get_saved_analyses'));
        add_action('wp_ajax_nopriv_get_saved_analyses', array($this, 'handle_get_saved_analyses'));

        // Debug hook per diagnosticare problemi con le sottoscrizioni utente
        add_action('wp_ajax_debug_user_subscription', array($this, 'debug_user_subscription'));
        add_action('wp_ajax_nopriv_debug_user_subscription', array($this, 'debug_user_subscription'));

        // Report Formatter Widget AJAX handlers
        add_action('wp_ajax_rf_load_analyses', array($this, 'rf_load_analyses'));
        add_action('wp_ajax_nopriv_rf_load_analyses', array($this, 'rf_load_analyses'));
        add_action('wp_ajax_rf_load_documents', array($this, 'rf_load_documents'));
        add_action('wp_ajax_nopriv_rf_load_documents', array($this, 'rf_load_documents'));
        add_action('wp_ajax_rf_save_document', array($this, 'rf_save_document'));
        add_action('wp_ajax_nopriv_rf_save_document', array($this, 'rf_save_document'));
        add_action('wp_ajax_rf_load_document', array($this, 'rf_load_document'));
        add_action('wp_ajax_nopriv_rf_load_document', array($this, 'rf_load_document'));
        add_action('wp_ajax_rf_delete_document', array($this, 'rf_delete_document'));
        add_action('wp_ajax_nopriv_rf_delete_document', array($this, 'rf_delete_document'));
        add_action('wp_ajax_rf_export_pdf', array($this, 'rf_export_pdf'));
        add_action('wp_ajax_nopriv_rf_export_pdf', array($this, 'rf_export_pdf'));
        add_action('wp_ajax_rf_export_word', array($this, 'rf_export_word'));
        add_action('wp_ajax_nopriv_rf_export_word', array($this, 'rf_export_word'));
        add_action('wp_ajax_rf_get_analysis_content', array($this, 'rf_get_analysis_content'));
        add_action('wp_ajax_nopriv_rf_get_analysis_content', array($this, 'rf_get_analysis_content'));
        add_action('wp_ajax_rf_get_theme_preview', array($this, 'rf_get_theme_preview'));
        add_action('wp_ajax_nopriv_rf_get_theme_preview', array($this, 'rf_get_theme_preview'));
        add_action('wp_ajax_rf_create_table', array($this, 'rf_create_table'));
        add_action('wp_ajax_nopriv_rf_create_table', array($this, 'rf_create_table'));
        add_action('wp_ajax_rf_upload_logo', array($this, 'rf_upload_logo'));
        add_action('wp_ajax_nopriv_rf_upload_logo', array($this, 'rf_upload_logo'));
        add_action('wp_ajax_rf_remove_logo', array($this, 'rf_remove_logo'));
        add_action('wp_ajax_nopriv_rf_remove_logo', array($this, 'rf_remove_logo'));
        add_action('wp_ajax_rf_set_default_logo', array($this, 'rf_set_default_logo'));
        add_action('wp_ajax_nopriv_rf_set_default_logo', array($this, 'rf_set_default_logo'));
        add_action('wp_ajax_rf_get_logo', array($this, 'rf_get_logo'));
        add_action('wp_ajax_nopriv_rf_get_logo', array($this, 'rf_get_logo'));
        add_action('wp_ajax_rf_delete_analysis', array($this, 'rf_delete_analysis'));
        add_action('wp_ajax_nopriv_rf_delete_analysis', array($this, 'rf_delete_analysis'));

        // Spreadsheet Analysis Widget AJAX handlers
        add_action('wp_ajax_analyze_spreadsheet_data', array($this, 'analyze_spreadsheet_data'));
        add_action('wp_ajax_nopriv_analyze_spreadsheet_data', array($this, 'analyze_spreadsheet_data'));
        add_action('wp_ajax_export_spreadsheet_analysis_pdf', array($this, 'export_spreadsheet_analysis_pdf'));
        add_action('wp_ajax_nopriv_export_spreadsheet_analysis_pdf', array($this, 'export_spreadsheet_analysis_pdf'));
        add_action('wp_ajax_check_ai_status', array($this, 'check_ai_status'));
        add_action('wp_ajax_nopriv_check_ai_status', array($this, 'check_ai_status'));

        // Excel file processing handlers
        add_action('wp_ajax_parse_excel_sheets', array($this, 'parse_excel_sheets'));
        add_action('wp_ajax_nopriv_parse_excel_sheets', array($this, 'parse_excel_sheets'));
        add_action('wp_ajax_load_excel_sheet', array($this, 'load_excel_sheet'));
        add_action('wp_ajax_nopriv_load_excel_sheet', array($this, 'load_excel_sheet'));

        // Financial Expert Assistant Widget AJAX handlers
        add_action('wp_ajax_chat_with_ai', array($this, 'chat_with_ai'));
        add_action('wp_ajax_nopriv_chat_with_ai', array($this, 'chat_with_ai'));
        add_action('wp_ajax_save_financial_expert_chat', array($this, 'save_financial_expert_chat'));
        add_action('wp_ajax_nopriv_save_financial_expert_chat', array($this, 'save_financial_expert_chat'));
        add_action('wp_ajax_get_saved_chats', array($this, 'get_saved_chats'));
        add_action('wp_ajax_nopriv_get_saved_chats', array($this, 'get_saved_chats'));
        add_action('wp_ajax_load_saved_chat', array($this, 'load_saved_chat'));
        add_action('wp_ajax_nopriv_load_saved_chat', array($this, 'load_saved_chat'));
        add_action('wp_ajax_delete_saved_chat', array($this, 'delete_saved_chat'));
        add_action('wp_ajax_nopriv_delete_saved_chat', array($this, 'delete_saved_chat'));

        // Word Analysis Widget AJAX handlers
        add_action('wp_ajax_extract_word_content', array($this, 'extract_word_content'));
        add_action('wp_ajax_nopriv_extract_word_content', array($this, 'extract_word_content'));
        add_action('wp_ajax_analyze_word_content', array($this, 'analyze_word_content'));
        add_action('wp_ajax_nopriv_analyze_word_content', array($this, 'analyze_word_content'));
        add_action('wp_ajax_export_word_analysis_pdf', array($this, 'export_word_analysis_pdf'));
        add_action('wp_ajax_nopriv_export_word_analysis_pdf', array($this, 'export_word_analysis_pdf'));
        add_action('wp_ajax_word_advanced_export_pdf', array($this, 'word_advanced_export_pdf'));
        add_action('wp_ajax_nopriv_word_advanced_export_pdf', array($this, 'word_advanced_export_pdf'));

        // Initialize Financial Academy Manager (needed for frontend AJAX)
        new Financial_Academy_Manager();

        // Initialize admin classes
        if (is_admin()) {
            add_action('admin_init', array($this, 'init_admin_classes'));
        }
    }

    /**
     * Initialize admin classes
     */
    public function init_admin_classes() {
        // Initialize Payment Gateway Admin
        new Payment_Gateway_Admin();

        // Initialize Menu Manager
        Financial_Advisor_Menu_Manager::get_instance();
    }

    /**
     * Get current user information regardless of user type (WordPress or external)
     *
     * @return array User information with keys: id, type, data
     */
    public function get_current_user_info() {
        $user_info = [
            'id' => 0,
            'type' => 'guest',
            'data' => null
        ];

        // Check for WordPress user first
        $wp_user_id = get_current_user_id();
        if ($wp_user_id > 0) {
            $user_info['id'] = $wp_user_id;
            $user_info['type'] = 'wordpress';
            $user_info['data'] = get_userdata($wp_user_id);
            return $user_info;
        }

        // Check for external subscriber
        if (class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            if (fa_access_control()->is_current_user_subscriber()) {
                $subscriber_data = fa_access_control()->get_current_subscriber_data();
                if (isset($subscriber_data['id']) && intval($subscriber_data['id']) > 0) {
                    $user_info['id'] = intval($subscriber_data['id']);
                    $user_info['type'] = 'subscriber';
                    $user_info['data'] = $subscriber_data;
                    return $user_info;
                }
            }
        }

        // Last resort: check cookie directly
        if (isset($_COOKIE['fa_subscriber_login'])) {
            try {
                $cookie_data = stripslashes($_COOKIE['fa_subscriber_login']);
                $subscriber_data = json_decode(base64_decode($cookie_data), true);
                if (isset($subscriber_data['id']) && intval($subscriber_data['id']) > 0) {
                    $user_info['id'] = intval($subscriber_data['id']);
                    $user_info['type'] = 'subscriber';
                    $user_info['data'] = $subscriber_data;
                    return $user_info;
                }
            } catch (Exception $e) {
                dv_debug_log('Error decoding subscriber cookie: ' . $e->getMessage());
            }
        }

        return $user_info;
    }

    /**
     * Update user statistics in a transactional manner
     *
     * @param int $user_id User ID
     * @param string $user_type User type (wordpress|subscriber)
     * @param array $stats_update Statistics to update
     * @return bool Success or failure
     */
    public function update_user_stats_transaction($user_id, $user_type, $stats_update) {
        global $wpdb;

        // Start transaction
        $wpdb->query('START TRANSACTION');

        try {
            $success = false;

            if ($user_type === 'subscriber') {
                $users_table = 'wpcd_user_subscription';

                // Get current values
                $current = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM {$users_table} WHERE id = %d FOR UPDATE",
                    $user_id
                ));

                if (!$current) {
                    throw new Exception("User not found: {$user_id}");
                }

                // Calculate new values
                $updates = [];
                $formats = [];

                if (isset($stats_update['tokens_used'])) {
                    $updates['tokens_used'] = $current->tokens_used + $stats_update['tokens_used'];
                    $formats[] = '%d';
                }

                if (isset($stats_update['analysis_count'])) {
                    $updates['analysis_count'] = $current->analysis_count + $stats_update['analysis_count'];
                    $formats[] = '%d';
                }

                if (isset($stats_update['cost'])) {
                    $updates['actual_cost'] = $current->actual_cost + $stats_update['cost'];
                    $updates['tot_cost'] = $current->tot_cost + $stats_update['cost'];
                    // Supporta sia credit che credits_available
                    if (property_exists($current, 'credits_available')) {
                        $updates['credits_available'] = max(0, $current->credits_available - $stats_update['cost']);
                    } else {
                        $updates['credit'] = max(0, $current->credit - $stats_update['cost']);
                    }
                    $formats[] = '%f';
                    $formats[] = '%f';
                    $formats[] = '%f';
                }

                $updates['updated_at'] = current_time('mysql');
                $formats[] = '%s';

                // Update the database
                $result = $wpdb->update(
                    $users_table,
                    $updates,
                    ['id' => $user_id],
                    $formats,
                    ['%d']
                );

                $success = ($result !== false);
            } else if ($user_type === 'wordpress') {
                // Handle WordPress user updates using Document_Stats class
                if (class_exists('Document_Stats')) {
                    $stats = new Document_Stats();

                    // Get current stats
                    $current_stats = $stats->get_user_stats($user_id);

                    if (!$current_stats) {
                        $stats->initialize_user_stats($user_id);
                        $current_stats = $stats->get_user_stats($user_id);
                    }

                    // Update WordPress user stats
                    $updates = [
                        'analysis_count' => $current_stats->analysis_count,
                        'tokens_used' => $current_stats->tokens_used,
                        'credits_available' => $current_stats->credits_available,
                        'actual_cost' => $current_stats->actual_cost,
                        'tot_cost' => $current_stats->tot_cost,
                        'last_update' => current_time('mysql')
                    ];

                    if (isset($stats_update['tokens_used'])) {
                        $updates['tokens_used'] += $stats_update['tokens_used'];
                    }

                    if (isset($stats_update['analysis_count'])) {
                        $updates['analysis_count'] += $stats_update['analysis_count'];
                    }

                    if (isset($stats_update['cost'])) {
                        $updates['actual_cost'] += $stats_update['cost'];
                        $updates['tot_cost'] += $stats_update['cost'];
                        $updates['credits_available'] = max(0, $updates['credits_available'] - $stats_update['cost']);
                    }

                    // Update the database
                    $result = $wpdb->update(
                        $stats->stats_table,
                        $updates,
                        ['user_id' => $user_id],
                        ['%d', '%d', '%f', '%f', '%f', '%s'],
                        ['%d']
                    );

                    $success = ($result !== false);
                } else {
                    throw new Exception("Document_Stats class not available");
                }
            }

            if (!$success) {
                throw new Exception("Failed to update user stats: {$wpdb->last_error}");
            }

            // Commit transaction
            $wpdb->query('COMMIT');
            return true;
        } catch (Exception $e) {
            // Rollback on error
            $wpdb->query('ROLLBACK');
            dv_debug_log('Transaction error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Debug delle sottoscrizioni utente
     * Funzione per diagnosticare problemi con i dati delle sottoscrizioni utente
     */
    public function debug_user_subscription() {
        global $wpdb;

        // Get user ID from cookie
        $external_user_id = 0;
        if (isset($_COOKIE['fa_subscriber_login'])) {
            try {
                $cookie_data = stripslashes($_COOKIE['fa_subscriber_login']);
                $subscriber_data = json_decode(base64_decode($cookie_data), true);
                if (isset($subscriber_data['id'])) {
                    $external_user_id = intval($subscriber_data['id']);
                }
            } catch (Exception $e) {
                wp_send_json_error(['error' => $e->getMessage()]);
            }
        }

        if ($external_user_id > 0) {
            $users_table = 'wpcd_user_subscription';
            $user = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$users_table} WHERE id = %d",
                $external_user_id
            ), ARRAY_A);

            wp_send_json_success([
                'user_data' => $user,
                'cookie_data' => $subscriber_data,
                'message' => 'Dati utente recuperati con successo'
            ]);
        } else {
            wp_send_json_error(['message' => 'Nessun ID utente valido trovato nel cookie']);
        }
    }

    /**
     * Enqueue scripts and styles for the plugin
     */    public function enqueue_scripts() {
        // Enqueue WordPress dashicons for the logout button
        wp_enqueue_style('dashicons');

        // Register and enqueue the main document viewer CSS
        wp_register_style(
            'document-viewer-style',
            $this->plugin_url . 'assets/css/document-viewer-main.css',
            array(),
            $this->version
        );
        wp_enqueue_style('document-viewer-style');

        // Document stats CSS viene caricato solo quando il widget document viewer è utilizzato
        // Rimosso da qui per evitare conflitti con altri widget



        // Register and enqueue the text enhancer CSS
        wp_register_style(
            'text-enhancer-style',
            $this->plugin_url . 'assets/css/text-enhancer.css',
            array('document-viewer-style'),
            $this->version
        );
        wp_enqueue_style('text-enhancer-style');

        // Register and enqueue the mega tooltip CSS
        wp_register_style(
            'mega-tooltip-style',
            $this->plugin_url . 'assets/css/mega-tooltip.css',
            array('document-viewer-style'),
            $this->version
        );
        wp_enqueue_style('mega-tooltip-style');

        // Register and enqueue the OCR script
        wp_register_script(
            'document-ocr-script',
            $this->plugin_url . 'assets/js/ocr.js',
            array('jquery'),
            $this->version,
            true
        );
          // Register and enqueue the document stats JavaScript
        wp_register_script(
            'document-stats-script',
            $this->plugin_url . 'assets/js/document-stats.js',
            array('jquery'),
            $this->version,
            true
        );

        // Localize the script with AJAX object for document stats
        wp_localize_script(
            'document-stats-script',
            'document_stats_ajax_object',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('document_viewer_nonce')
            )
        );

        // Register and enqueue the text enhancer JavaScript
        wp_register_script(
            'text-enhancer-script',
            $this->plugin_url . 'assets/js/text-enhancer.js',
            array('jquery'),
            $this->version,
            true
        );

        // Register and enqueue the mega tooltip JavaScript
        wp_register_script(
            'mega-tooltip-script',
            $this->plugin_url . 'assets/js/mega-tooltip.js',
            array('jquery'),
            $this->version,
            true
        );

        // Register and enqueue the main document viewer JavaScript
        wp_register_script(
            'document-viewer-script',
            $this->plugin_url . 'assets/js/document-viewer.js',
            array('jquery', 'document-ocr-script', 'document-stats-script', 'text-enhancer-script', 'mega-tooltip-script'),
            $this->version,
            true
        );

        // Registra lo script di debug per COST_RATE (solo in modalità sviluppo)
        wp_register_script(
            'cost-rate-debug-script',
            $this->plugin_url . 'assets/js/cost-rate-debug.js',
            array('jquery', 'document-viewer-script', 'document-stats-script'),
            $this->version,
            true
        );

        // Register and enqueue the Office Add-in JavaScript
        wp_register_script(
            'office-addin-script',
            $this->plugin_url . 'assets/js/office-addin.js',
            array('jquery', 'document-stats-script'),
            $this->version,
            true
        );

        // Register and enqueue the Office Add-in Preview JavaScript
        wp_register_script(
            'office-addin-preview-script',
            $this->plugin_url . 'assets/js/office-addin-preview.js',
            array('jquery', 'dashicons'),
            $this->version,
            true
        );

        // Register and enqueue the Office Add-in Editor JavaScript
        wp_register_script(
            'office-addin-editor-script',
            $this->plugin_url . 'assets/js/office-addin-editor.js',
            array('jquery', 'wp-color-picker', 'editor'),
            $this->version,
            true
        );

        // Register and enqueue the Office Add-in Preview CSS
        wp_register_style(
            'office-addin-preview-style',
            $this->plugin_url . 'assets/css/office-addin-preview.css',
            array('dashicons'),
            $this->version
        );

        // Register and enqueue the Office Add-in Editor CSS
        wp_register_style(
            'office-addin-editor-style',
            $this->plugin_url . 'assets/css/office-addin-editor.css',
            array('wp-color-picker', 'editor-buttons'),
            $this->version
        );

        // Localize the script with necessary data
        wp_localize_script(
            'document-viewer-script',
            'documentViewerParams',
            array(
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('document_viewer_nonce'),
                'pluginUrl' => $this->plugin_url,
                'i18n' => array(
'uploadError' => __('Error uploading file. Please try again.', 'document-viewer-plugin'),
                    'analysisError' => __('Error analyzing document. Please try again.', 'document-viewer-plugin'),
                    'exportError' => __('Error exporting PDF. Please try again.', 'document-viewer-plugin'),
                    'clearConfirm' => __('Are you sure you want to clear the current document? All analysis will be lost.', 'document-viewer-plugin'),
                    'saving' => __('Saving...', 'document-viewer-plugin'),
                    'saved' => __('Saved', 'document-viewer-plugin'),
                    'saveError' => __('Error saving changes', 'document-viewer-plugin'),
                    'ocrProcessing' => __('OCR Processing in progress...', 'document-viewer-plugin'),
                    'ocrComplete' => __('OCR Completed Successfully', 'document-viewer-plugin'),
                    'ocrError' => __('Error during OCR processing', 'document-viewer-plugin'),
                    'statsError' => __('Error loading statistics', 'document-viewer-plugin'),
                    'refreshStats' => __('Refresh Stats', 'document-viewer-plugin'),
                    'statsLoading' => __('Loading statistics...', 'document-viewer-plugin'),
                    'noAnalyses' => __('No analyses available', 'document-viewer-plugin'),
                )
            )
        );

        wp_enqueue_script('document-ocr-script');
        wp_enqueue_script('document-stats-script');
        wp_enqueue_script('text-enhancer-script');
        wp_enqueue_script('document-viewer-script');

        // Carica lo script di debug COST_RATE (solo in ambiente di sviluppo)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            wp_enqueue_script('cost-rate-debug-script');
        }


    }

    public function render_document_viewer($atts) {
        // Carica il CSS document-stats solo quando il widget document viewer viene utilizzato
        wp_enqueue_style('document-stats-style',
            $this->plugin_url . 'assets/css/document-stats.css',
            array('document-viewer-style'),
            $this->version
        );

        // Verifica che l'utente sia loggato (WP o non WP)
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            return '<div class="document-viewer-login-required">
                <p>' . __('Devi effettuare il login per accedere a questa funzionalità.', 'document-viewer-plugin') . '</p>
                <a href="' . site_url('/') . '" class="login-button">' . __('Accedi', 'document-viewer-plugin') . '</a>
            </div>';
        }

        $atts = shortcode_atts(array(
            'title' => __('Analisi Documento', 'document-viewer-plugin')
        ), $atts, 'document_viewer');

        ob_start();
        ?>
        <div class="document-viewer-container">
            <!-- Widget header with help trigger -->
            <div class="widget-header">
                <button class="widget-help-trigger-right" data-help-trigger title="Aiuto">?</button>
                <h3><?php echo esc_html($atts['title']); ?></h3>
            </div>

            <div class="document-viewer-widget">
            <!-- Colonna delle statistiche (sinistra) -->
            <div class="stats-column" id="stats-column">
                <h3><?php _e('Monitor activity', 'document-viewer-plugin'); ?></h3>

                <?php
                // Verifica se l'utente è un sottoscrittore esterno
                $is_subscriber = false;
                if (class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
                    $is_subscriber = fa_access_control()->is_current_user_subscriber();
                }

                // Mostra il messaggio di login solo se l'utente non è né un utente WordPress né un sottoscrittore
                if (!is_user_logged_in() && !$is_subscriber): ?>
                    <!-- Messaggio per utenti non loggati -->
                    <div class="login-required">
                        <p><?php _e('Effettua il login per visualizzare le tue statistiche', 'document-viewer-plugin'); ?></p>
                        <a href="<?php echo wp_login_url(get_permalink()); ?>"><?php _e('Accedi', 'document-viewer-plugin'); ?></a>
                    </div>
                    <script>
                        // Aggiungi classe not-logged-in per il JavaScript
                        jQuery(document).ready(function($) {
                            $('#stats-column').addClass('not-logged-in');
                        });
                    </script>
                <?php else: ?>
                    <!-- Contenitore statistiche utente -->
                    <div id="user-stats-container" class="stats-section">
                        <div class="stats-section-header">
                            <span><?php _e('Dati Utilizzo', 'document-viewer-plugin'); ?></span>
                            <span class="toggle-icon expanded"></span>
                        </div>
                        <div class="stats-section-content">                            <!-- Info utente -->
                            <div class="user-info">
                                <div class="user-avatar" id="user-avatar"></div>
                                <div class="user-details">
                                    <div class="user-name" id="user-name">...</div>
                                    <div class="user-role" id="user-role">...</div>
                                </div>
                                <div class="stats-logout-button">
                                    <a href="<?php echo esc_url(wp_logout_url(get_permalink())); ?>" class="stats-logout-link" onclick="cleanUserDataBeforeLogout(event)">
                                        <span class="dashicons dashicons-exit"></span> Logout
                                    </a>
                                </div>
                            </div>

                            <!-- Statistiche in grid con wrapper container per CSS specificity -->
                            <div class="document-viewer-stats-container">
                                <div class="stats-grid">
                                    <div class="stats-row usage-row">
                                        <div class="stats-item">
                                            <div class="stats-label">
                                                <?php _e('Analisi', 'document-viewer-plugin'); ?>
                                                <span class="stats-info-icon">i</span>
                                                <div class="stats-tooltip"><?php _e('Numero totale di analisi effettuate', 'document-viewer-plugin'); ?></div>
                                            </div>
                                            <div class="stats-value" id="analyses-count">-</div>
                                        </div>
                                        <div class="stats-item">
                                            <div class="stats-label">
                                                <?php _e('Token', 'document-viewer-plugin'); ?>
                                                <span class="stats-info-icon">i</span>
                                                <div class="stats-tooltip"><?php _e('Numero totale di token utilizzati in tutte le analisi', 'document-viewer-plugin'); ?></div>
                                            </div>
                                            <div class="stats-value" id="tokens-count">-</div>
                                        </div>
                                    </div>

                                    <div class="stats-row costs-row">
                                        <div class="stats-item cost-item">
                                            <div class="stats-label">
                                                <?php _e('Spesa Stimata', 'document-viewer-plugin'); ?>
                                                <span class="stats-info-icon">i</span>
                                                <div class="stats-tooltip"><?php _e('Stima del costo basata sul numero di token utilizzati', 'document-viewer-plugin'); ?></div>
                                            </div>
                                            <div class="stats-value cost-highlight" id="cost-estimate">€0.00</div>
                                        </div>
                                        <div class="stats-item cost-item">
                                        <div class="stats-label">
                                                <?php _e('Spesa Effettiva', 'document-viewer-plugin'); ?>
                                                <span class="stats-info-icon">i</span>
                                                <div class="stats-tooltip"><?php _e('Costo effettivo dell\'analisi completata', 'document-viewer-plugin'); ?></div>
                                            </div>
                                            <div class="stats-value cost-highlight" id="actual-cost">€0.00</div>
                                        </div>
                                    </div>

                                    <!-- Costo totale nascosto - mantenuto solo nel database per statistiche -->
                                    <div class="stats-row total-cost-row" style="display: none;">
                                        <div class="stats-item total-cost-item">
                                            <div class="stats-label">
                                                <?php _e('Spesa Totale', 'document-viewer-plugin'); ?>
                                                <span class="stats-info-icon">i</span>
                                                <div class="stats-tooltip"><?php _e('Costo totale di tutte le analisi (dato statistico)', 'document-viewer-plugin'); ?></div>
                                            </div>
                                            <div class="stats-value total-cost-highlight" id="tot-cost">€0.00</div>
                                        </div>
                                    </div>

                                    <div class="stats-row credit-row">
                                        <div class="stats-item credit-item">
                                            <div class="stats-label">
                                                <?php _e('Credito Disponibile', 'document-viewer-plugin'); ?>
                                            </div>
                                            <div class="stats-value credit-highlight" id="credits-available">€0.00</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Rimossa sezione date prima e ultima analisi -->
                        </div>
                    </div>

                    <!-- Analisi recenti -->
                    <div id="recent-analyses-container" class="stats-section">
                        <div class="stats-section-header">
                            <span><?php _e('Analisi Recenti', 'document-viewer-plugin'); ?></span>
                            <span class="toggle-icon expanded"></span>
                        </div>
                        <div class="stats-section-content">
                            <div id="recent-analyses-list" class="recent-analyses-list">
                                <!-- Le analisi verranno caricate dinamicamente -->
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Colonna del form (centro) -->
        <div class="document-form-column">
                <h3><?php _e('Carica e Analizza', 'document-viewer-plugin'); ?></h3>

                <form id="document-viewer-form" enctype="multipart/form-data">
                    <!-- Custom logo upload - SPOSTATO IN ALTO -->
                    <div class="form-row custom-logo-field">
                        <input type="file" id="custom-logo-upload" accept="image/*" />
                        <label for="custom-logo-upload"><?php _e('Scegli Logo', 'document-viewer-plugin'); ?></label>
                        <label for="custom-logo-upload" class="logo-description"><?php _e('Logo Personalizzato (opzionale)', 'document-viewer-plugin'); ?></label>
                        <div class="logo-preview-container">
                            <img id="logo-preview" src="" alt="Logo Preview" style="display: none;">
                            <div id="logo-dimensions-info" style="display: none;"></div>
                        </div>
                    </div>

                    <!-- Titolo analisi - SPOSTATO SUBITO DOPO IL LOGO -->
                    <div class="form-row">
                        <label for="analysis-title"><?php _e('Titolo Analisi:', 'document-viewer-plugin'); ?></label>
                        <input type="text" id="analysis-title" placeholder="<?php _e('Inserisci un titolo per l\'analisi', 'document-viewer-plugin'); ?>" />
                    </div>

                    <!-- File upload section - SPOSTATO DOPO IL TITOLO -->
                    <div class="form-row file-upload-row">
                        <label for="document-upload"><?php _e('Carica Documento (PDF, Word o Immagine):', 'document-viewer-plugin'); ?></label>
                        <div class="file-upload-container">
                            <div class="file-upload-input">
                                <input type="file" id="document-upload" accept=".pdf,.doc,.docx" />
                                <label for="document-upload"><?php _e('Scegli PDF/Word', 'document-viewer-plugin'); ?></label>

                                <!-- Campo per il caricamento delle immagini -->
                                <input type="file" id="image-upload" accept="image/*" />
                                <label for="image-upload"><?php _e('Scegli Immagine', 'document-viewer-plugin'); ?></label>
                            </div>
                            <div id="document-info-inline" class="document-info-inline" style="display: none;">
                                <span id="document-name-inline"></span>
                                <span id="document-size-inline"></span>
                                <span id="document-type-inline"></span>
                                <span id="document-chars-inline"></span>
                                <span id="document-tokens-inline"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Descrizione/domanda -->
                    <div class="form-row">
                        <label for="document-description"><?php _e('Descrizione o Domanda:', 'document-viewer-plugin'); ?></label>

                        <!-- NUOVA FUNZIONALITÀ: Menu per analisi salvate -->
                        <div class="saved-analyses-container">
                            <label for="saved-analyses-search"><?php _e('Carica Analisi Salvata:', 'document-viewer-plugin'); ?></label>
                            <div class="saved-analyses-search-container">
                                <input type="text" id="saved-analyses-search" class="saved-analyses-search"
                                       placeholder="<?php _e('Cerca analisi salvate...', 'document-viewer-plugin'); ?>" />
                                <div class="saved-analyses-dropdown" id="saved-analyses-dropdown" style="display: none;">
                                    <div class="saved-analyses-list" id="saved-analyses-list">
                                        <!-- Le analisi salvate verranno caricate dinamicamente qui -->
                                    </div>
                                </div>
                            </div>
                            <span class="saved-analyses-info-tip" title="<?php _e('Cerca e carica un\'analisi precedentemente salvata come base per una nuova analisi', 'document-viewer-plugin'); ?>">?</span>
                        </div>

                        <!-- Menu a tendina per richieste predefinite -->
                        <div class="preset-queries-container">
                            <select id="preset-queries" class="preset-queries-select">
                                <option value=""><?php _e('-- Seleziona una richiesta predefinita --', 'document-viewer-plugin'); ?></option>
                                <?php
                                // Recupera le richieste predefinite dal database
                                $preset_queries = $this->get_preset_queries();
                                foreach ($preset_queries as $query) {
                                    echo '<option value="' . esc_attr($query['query_text']) . '">' . esc_html($query['title']) . '</option>';
                                }
                                ?>
                            </select>
                            <span class="preset-info-tip" title="<?php _e('Seleziona una richiesta predefinita o scrivi la tua domanda personalizzata', 'document-viewer-plugin'); ?>">?</span>
                        </div>

                        <textarea id="document-description" rows="4"
                                placeholder="<?php _e('Inserisci una domanda o descrivi cosa vuoi analizzare nel documento', 'document-viewer-plugin'); ?>"></textarea>
                    </div>

                    <!-- Action buttons -->
                    <div class="form-row">
                        <button type="button" id="analyze-description"><?php _e('Analizza', 'document-viewer-plugin'); ?></button>
                        <button type="button" id="clear-document"><?php _e('Cancella', 'document-viewer-plugin'); ?></button>
                    </div>

                    <!-- Note/Annotations section -->
                    <div class="form-row">
                        <label for="document-annotations"><?php _e('Note Aggiuntive (opzionale):', 'document-viewer-plugin'); ?></label>
                        <textarea id="document-annotations" rows="3"
                                placeholder="<?php _e('Eventuali note da includere nel report', 'document-viewer-plugin'); ?>"></textarea>
                    </div>

                    <!-- Export actions -->
                    <div class="export-actions">
                        <button type="button" id="export-pdf"><?php _e('Esporta in PDF', 'document-viewer-plugin'); ?></button>
                        <button type="button" id="save-analysis"><?php _e('Salva Analisi', 'document-viewer-plugin'); ?></button>
                        <div id="save-result-message" class="save-result-message"></div>
                    </div>
                </form>
            </div>

            <!-- Colonna di visualizzazione (destra) -->
            <div class="document-display-column">
                <!-- Analysis results - SPOSTATO IN ALTO -->
                <h3><?php _e('', 'document-viewer-plugin'); ?></h3>
                <div id="analysis-results">
                <div style="background-image: url('<?php echo esc_url(plugins_url('/financial-advisor-V4/images/bg-da.jpg', dirname(__FILE__))); ?>'); background-size: cover; background-position: center; padding: 40px 20px; border-radius: 8px; min-height: 150px; display: flex; align-items: center; padding: 30px; border-radius: 8px; max-width: 800px;">
                <div style="padding: 20px; border-radius: 8px; max-width: 600px;">
                <h5 style="text-align: left; margin: 0 0 15px 0; color: #2d3748; font-size: 1.5em; line-height: 1.4;">
                  <?php _e('Carica un documento', 'document-viewer-plugin'); ?>
                            </h5><p>
                        <h5 style="text-align: left; margin: 0 0 15px 0; color: #2d3748; font-size: 1.5em; line-height: 1.4;">
                        <?php _e('per iniziare l\'analisi', 'document-viewer-plugin'); ?>
                       </h5></p>
                   <h5 style="text-align: left; margin: 0; color: #2d3748; font-size: 1.5em; line-height: 1.4;">
                    <?php _e('con supporto AI Financial Expert', 'document-viewer-plugin'); ?>
                  </h5>
               </div>
               </div>
                </div>

                <!-- Document display - SPOSTATO IN BASSO -->
                <h3><?php _e('Visualizzazione Documento', 'document-viewer-plugin'); ?></h3>

                <!-- Status notification area -->
                <div id="document-notification-area" class="document-notification-area" style="display: none;">
                    <div class="notification-content"></div>
                </div>

                <!-- Zoom controls -->
                <div class="zoom-controls">
                    <button type="button" id="zoom-in" class="zoom-btn" style="display: none;">+</button>
                    <button type="button" id="zoom-out" class="zoom-btn" style="display: none;">-</button>
                </div>

                <div id="document-display" style="display: none;">
                    <iframe id="document-frame" style="display: none;"></iframe>
                </div>


            </div>

        </div> <!-- End document-viewer-widget -->
        </div> <!-- End document-viewer-container -->
        <?php
        return ob_get_clean();
    }

    public function render_chat_viewer() {
        // Verifica che l'utente sia loggato
        if (!is_user_logged_in()) {
            return '<div class="document-viewer-login-required">
                <p>' . __('Devi effettuare il login per accedere a questa funzionalità.', 'document-viewer-plugin') . '</p>
                <a href="' . site_url('/') . '" class="login-button">' . __('Accedi', 'document-viewer-plugin') . '</a>
            </div>';
        }

        // Get API settings that will be needed for the chat
        $api_endpoint = get_option('document_viewer_api_endpoint');
        $api_model = get_option('document_viewer_model');

        ob_start();
        ?>
        <div class="chat-widget"
             data-api-endpoint="<?php echo esc_attr($api_endpoint); ?>"
             data-api-model="<?php echo esc_attr($api_model); ?>">
            <h3><?php _e('Chat with Model', 'document-viewer-plugin'); ?></h3>
            <div class="chat-form">
                <div class="chat-log"></div>
                <div class="input-wrapper">
                    <input type="text" class="chat-input" placeholder="<?php _e('Enter your message...', 'document-viewer-plugin'); ?>" />
                    <button type="button" class="send-btn" aria-label="<?php _e('Send Message', 'document-viewer-plugin'); ?>"></button>
                </div>
                <div class="chat-controls">
                    <button type="button" class="send-chat"><?php _e('Send', 'document-viewer-plugin'); ?></button>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    public function clear_document() {
        check_ajax_referer('document_viewer_nonce', 'nonce');
        wp_send_json_success(array('message' => __('Document cleared successfully.', 'document-viewer-plugin')));
    }

    public function upload_document() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Verifica se l'utente è un subscriber non WordPress
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);

        // Se l'utente non è loggato in WordPress e non è un subscriber, blocca l'accesso
        if (!is_user_logged_in() && !$is_subscriber_logged_in) {
            wp_die(__('Devi effettuare il login per caricare documenti.', 'document-viewer-plugin'));
            return;
        }

        // Rest of the function remains the same
    }

    public function analyze_document() {
        error_log('=== INIZIO analyze_document() ===');
        error_log('POST data: ' . print_r($_POST, true));

        // Check nonce for security - ma non interrompere l'esecuzione in caso di errore
        // per consentire agli utenti non WordPress di utilizzare la funzionalità
        $nonce_verified = false;

        try {
            $nonce_verified = check_ajax_referer('document_viewer_nonce', 'nonce', false);
            error_log('Nonce verification result: ' . ($nonce_verified ? 'SUCCESS' : 'FAILED'));
            if (!$nonce_verified) {
                error_log('Nonce verification failed but continuing for non-WordPress users');
            }
        } catch (Exception $e) {
            error_log('Exception during nonce verification: ' . $e->getMessage());
        }

        // Verifica se l'utente è un subscriber non WordPress
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);

        // Controllo di accesso semplificato
        if (!is_user_logged_in() && !$is_subscriber_logged_in) {
            wp_send_json_error(['message' => __('Devi effettuare il login per utilizzare questa funzionalità.', 'document-viewer-plugin')]);
            return;
        }

        // Check for developer mode
        $developer_mode = get_option('document_viewer_developer_mode', false);
        $developer_response = get_option('document_viewer_developer_response', '');
        $description = isset($_POST['description']) ? $_POST['description'] : '';

        // If developer mode is enabled, description is provided, and we're trying to analyze (not just extract content)
        if ($developer_mode && !empty($developer_response) && !empty($description)) {
            error_log('Developer mode active - returning custom response for document analysis');

            // Format the response just like normal API response, including the HTML structure with edit buttons
            // but with a specific class for developer mode
            $formatted_html = '';
            $lines = explode("\n", $developer_response);

            foreach ($lines as $line) {
                // Convert multiple spaces to &nbsp; entities to preserve spacing
                $line = preg_replace('/  /', '&nbsp;&nbsp;', $line);

                // Add paragraph tags for better structure instead of just line breaks
                if (trim($line) !== '') {
                    $formatted_html .= '<p>' . htmlspecialchars($line) . '</p>';
                } else {
                    $formatted_html .= '<p>&nbsp;</p>'; // Empty paragraph for spacing
                }
            }

            $formatted_response = '
                <div id="analysis-content" class="developer-content">' . $formatted_html . '</div>
            ';

            wp_send_json_success(array(
                'results' => $formatted_response,
                'message' => __('Analysis completed in developer mode.', 'document-viewer-plugin')
            ));
            return;
        }

        // Check PDF Parser availability first
        if (!$this->check_pdf_parser()) {
            error_log('PDF Parser not available - check your composer dependencies');
            wp_send_json_error(array('message' => __('PDF Parser not available. Please contact administrator.', 'document-viewer-plugin')));
            return;
        }

        // Get upload directory - handle both WordPress and non-WordPress users
        if (function_exists('wp_upload_dir')) {
            $upload_dir = wp_upload_dir();
            $plugin_upload_dir = $upload_dir['basedir'] . '/document-viewer';
        } else {
            // Fallback for non-WordPress users
            $plugin_upload_dir = dirname(__FILE__) . '/uploads/document-viewer';

            // Make sure the uploads directory exists for non-WordPress users
            if (!file_exists(dirname(__FILE__) . '/uploads')) {
                @mkdir(dirname(__FILE__) . '/uploads', 0755, true);
            }

            // Ensure the document-viewer directory exists with correct permissions
            if (!file_exists($plugin_upload_dir)) {
                @mkdir($plugin_upload_dir, 0755, true);
                dv_debug_log("Created non-WordPress upload directory: $plugin_upload_dir", 'analysis');
            }
        }

        // Log permissions for debugging
        error_log("Upload directory: $plugin_upload_dir");
        error_log("Upload directory exists: " . (file_exists($plugin_upload_dir) ? 'yes' : 'no'));
        error_log("Upload directory writable: " . (is_writable($plugin_upload_dir) ? 'yes' : 'no'));

        $document_data = isset($_POST['document_data']) ? $_POST['document_data'] : '';
        $description = isset($_POST['description']) ? $_POST['description'] : '';
        $save_content = isset($_POST['save_content']) ? (bool)$_POST['save_content'] : false;

        $extracted_content = '';

        // Handle file upload - only extract content, don't analyze
        if (isset($_FILES['document_file'])) {
            try {
                $file = $_FILES['document_file'];
                dv_debug_log("Received file upload: " . print_r($file, true));

                if ($file['error'] !== UPLOAD_ERR_OK) {
                    $error_message = $this->get_upload_error_message($file['error']);
                    dv_debug_log("File upload error: " . $error_message, 'analysis');
                    throw new Exception($error_message);
                }

                // Verify temp file exists and is readable
                if (!is_readable($file['tmp_name'])) {
                    dv_debug_log("Uploaded file is not readable: " . $file['tmp_name'], 'analysis');
                    throw new Exception('Uploaded file is not readable.');
                }

                $mime_type = $file['type'];
                $temp_file = $file['tmp_name'];

                // Log file details
                dv_debug_log("File details - Name: " . $file['name'] . ", Type: " . $mime_type . ", Size: " . $file['size'] . " bytes", 'analysis');

                // Make sure upload directory exists
                if (!file_exists($plugin_upload_dir)) {
                    dv_debug_log("Creating upload directory: " . $plugin_upload_dir, 'analysis');
                    // Usa mkdir invece di wp_mkdir_p per supportare anche utenti non WordPress
                    if (!function_exists('wp_mkdir_p') || !wp_mkdir_p($plugin_upload_dir)) {
                        // Fallback per utenti non WordPress
                        if (!@mkdir($plugin_upload_dir, 0755, true)) {
                            dv_debug_log("Failed to create upload directory", 'analysis');
                            throw new Exception('Could not create upload directory.');
                        }
                    }
                }

                // Move file to plugin directory temporarily
                // Usa una funzione custom per sanitizzare il nome del file se sanitize_file_name non è disponibile
                $safe_filename = function_exists('sanitize_file_name') ?
                    sanitize_file_name($file['name']) :
                    preg_replace('/[^a-zA-Z0-9_.-]/', '_', $file['name']);

                // Add a unique prefix to avoid filename collisions
                $safe_filename = uniqid('doc_') . '_' . $safe_filename;

                $temp_dest = $plugin_upload_dir . '/' . $safe_filename;
                if (!move_uploaded_file($temp_file, $temp_dest)) {
                    dv_debug_log("Failed to move uploaded file to: " . $temp_dest, 'analysis');
                    throw new Exception('Failed to move uploaded file.');
                }

                dv_debug_log("File moved successfully to: $temp_dest");
                dv_debug_log("File exists at destination: " . (file_exists($temp_dest) ? 'yes' : 'no'));
                dv_debug_log("File is readable: " . (is_readable($temp_dest) ? 'yes' : 'no'));
                dv_debug_log("File size: " . filesize($temp_dest) . " bytes");

                // Process the file based on type
                switch ($mime_type) {
                    case 'application/pdf':
                        dv_debug_log("Avvio estrazione contenuto da file PDF", 'analysis');
                        if (!$this->check_pdf_parser()) {
                            dv_debug_log("PDF Parser not available", 'analysis');
                            throw new Exception('PDF Parser not available');
                        }
                        $extracted_content = $this->extract_pdf_content($temp_dest);
                        dv_debug_log("PDF extraction complete. Content length: " . strlen($extracted_content), 'analysis');
                        break;

                    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                    case 'application/msword':
                        dv_debug_log("Avvio estrazione contenuto da documento Word", 'analysis');
                        if (!$this->check_phpword()) {
                            dv_debug_log("PHPWord library not available", 'analysis');
                            throw new Exception('PHPWord library not available');
                        }
                        $extracted_content = $this->extract_docx_content($temp_dest);
                        dv_debug_log("Word extraction complete. Content length: " . strlen($extracted_content), 'analysis');
                        break;

                    case 'text/plain':
                        dv_debug_log("Avvio estrazione contenuto da file di testo", 'analysis');
                        $extracted_content = file_get_contents($temp_dest);
                        $extracted_content = $this->clean_extracted_text($extracted_content);
                        dv_debug_log("Text extraction complete. Content length: " . strlen($extracted_content), 'analysis');
                        break;

                    default:
                        dv_debug_log("Unsupported file type: " . $mime_type, 'analysis');
                        throw new Exception('Unsupported file type: ' . $mime_type);
                }

                // Cleanup
                if (file_exists($temp_dest)) {
                    unlink($temp_dest);
                    dv_debug_log("Temporary file removed", 'analysis');
                }

                if ($save_content) {
                    dv_debug_log("Content extracted successfully, length: " . strlen($extracted_content));
                    // Return only the extracted content without analysis when save_content is true
                    wp_send_json_success(array(
                        'document_content' => $extracted_content,
                        'message' => __('Document content extracted successfully.', 'document-viewer-plugin')
                    ));
                    return;
                }

            } catch (Exception $e) {
                dv_debug_log('Document processing error: ' . $e->getMessage(), 'analysis');
                wp_send_json_error(array('message' => 'Error processing document: ' . $e->getMessage()));
                return;
            }
        }

        // If we get here and have no description, it means we're not supposed to analyze yet
        if (empty($description)) {
            // If we have extracted content but no description, just return the content without analysis
            if (!empty($extracted_content)) {
                dv_debug_log("Fase di estrazione completata. Contenuto estratto: " . strlen($extracted_content) . " caratteri. In attesa di richiesta di analisi.", 'analysis');
                wp_send_json_success(array(
                    'document_content' => $extracted_content,
                    'message' => __('Document content extracted successfully.', 'document-viewer-plugin')
                ));
                return;
            }

            // If no description and no content, it's an error
            if (empty($document_data)) {
                dv_debug_log("No document content or description provided", 'analysis');
                wp_send_json_error(array('message' => __('No document content or description provided.', 'document-viewer-plugin')));
                return;
            }
        }

        // Use either extracted content or posted document data
        $content_to_analyze = $extracted_content ?: $document_data;

        if (empty($content_to_analyze)) {
            dv_debug_log("No document content available for analysis", 'analysis');
            wp_send_json_error(array('message' => __('No document content available for analysis.', 'document-viewer-plugin')));
            return;
        }

        // Only proceed with analysis if we have a description (indicating the Analyze button was clicked)
        if (!empty($description)) {
            // Get Analysis API settings
            $api_key = get_option('document_viewer_api_key');
            $api_endpoint = get_option('document_viewer_api_endpoint');
            $api_model = get_option('document_viewer_model');

            // If Analysis API settings are not set, try using primary API settings
            if (empty($api_key) || empty($api_endpoint) || empty($api_model)) {
                dv_debug_log("Analysis API settings not found, using primary API settings", 'analysis');
            }

            // Check if we have the necessary API settings
            if (empty($api_key)) {
                dv_debug_log("API key is not configured for analysis", 'analysis');
                wp_send_json_error(array('message' => __('API key is not configured. Please check settings.', 'document-viewer-plugin')));
                return;
            }

            if (empty($api_endpoint)) {
                dv_debug_log("API endpoint is not configured for analysis", 'analysis');
                wp_send_json_error(array('message' => __('API endpoint is not configured. Please check settings.', 'document-viewer-plugin')));
                return;
            }

            if (empty($api_model)) {
                dv_debug_log("API model is not configured for analysis", 'analysis');
                wp_send_json_error(array('message' => __('API model is not configured. Please check settings.', 'document-viewer-plugin')));
                return;
            }

            // Ensure the endpoint points to the chat completions endpoint
            $api_url = rtrim($api_endpoint, '/') . '/chat/completions';
            dv_debug_log("FASE DI ANALISI: inizio elaborazione con modello AI", 'analysis');
            dv_debug_log("Invio richiesta di analisi a: " . $api_url, 'analysis');
            dv_debug_log("Utilizzo modello: " . $api_model, 'analysis');

            // Set up prompt
            $system_prompt = "You are an expert financial document analyzer. Analyze the provided document content and respond with a detailed breakdown addressing the user's specific question or request. Format your response in clear paragraphs with appropriate headings.";

            // Structure the request body according to OpenRouter's expectations
            $request_body = array(
                'model' => $api_model,
                'messages' => array(
                    array(
                        'role' => 'system',
                        'content' => $system_prompt
                    ),
                    array(
                        'role' => 'user',
                        'content' => "Here is the document content to analyze:\n\n" . $content_to_analyze . "\n\nUser's request: " . $description
                    )
                ),
                'temperature' => 0.3,
                'max_tokens' => 4000
            );

            // Headers for the request
            $headers = array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $api_key,
                'HTTP-Referer' => site_url(), // Required by OpenRouter
                'X-Title' => 'Document Viewer Plugin', // Identify app to OpenRouter
                'Accept' => 'application/json'
            );

            $request_args = array(
                'headers' => $headers,
                'body' => wp_json_encode($request_body),
                'method' => 'POST',
                'timeout' => 120, // Longer timeout for analysis
                'data_format' => 'body',
                'sslverify' => true,
                'redirection' => 5,
                'httpversion' => '1.1',
                'blocking' => true,
                'cookies' => array()
            );

            dv_debug_log("FASE DI ANALISI: richiesta inviata al servizio AI, in attesa di risposta...", 'analysis');

            // Make the request
            $response = wp_remote_post($api_url, $request_args);

            // Check for errors
            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();
                dv_debug_log("FASE DI ANALISI: errore nella richiesta API: " . $error_message, 'analysis');
                wp_send_json_error(array('message' => __('API request failed: ', 'document-viewer-plugin') . $error_message));
                return;
            }

            // Get response code and body
            $status_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);

            dv_debug_log("FASE DI ANALISI: risposta API ricevuta, codice: " . $status_code, 'analysis');
            dv_debug_log("FASE DI ANALISI: inizio elaborazione risposta", 'analysis');

            // Parse the response
            $data = json_decode($body, true);

            // Check for error responses
            if ($status_code !== 200) {
                $error_message = isset($data['error']) && isset($data['error']['message'])
                    ? $data['error']['message']
                    : __('API returned status code: ', 'document-viewer-plugin') . $status_code;

                dv_debug_log("FASE DI ANALISI: errore API: " . $error_message, 'analysis');
                wp_send_json_error(array('message' => $error_message));
                return;
            }

            // Extract the analysis result from the response
            $analysis_content = $this->extract_analysis_result($data);

            if (empty($analysis_content)) {
                dv_debug_log("FASE DI ANALISI: impossibile estrarre il contenuto dell'analisi dalla risposta API", 'analysis');
                dv_debug_log("FASE DI ANALISI: struttura risposta API: " . print_r($data, true), 'analysis');
                wp_send_json_error(array('message' => __('Could not extract analysis from API response.', 'document-viewer-plugin')));
                return;
            }

            // Format the response to HTML for display
            $formatted_html = '';
            $lines = explode("\n", $analysis_content);

            foreach ($lines as $line) {
                if (trim($line) !== '') {
                    $formatted_html .= '<p>' . htmlspecialchars($line) . '</p>';
                } else {
                    $formatted_html .= '<p>&nbsp;</p>'; // Empty paragraph for spacing
                }
            }

            $formatted_response = '
                <div id="analysis-content">' . $formatted_html . '</div>
            ';

            error_log("FASE DI ANALISI: elaborazione completata con successo.");

            // Calcola le statistiche aggiornate per la risposta (versione semplificata per debug)
            $token_count = $this->estimate_token_count($content_to_analyze);
            $analysis_cost = $token_count * 0.000002; // €0.000002 per token

            // Statistiche semplificate per evitare errori
            $stats_data = array(
                'actual_cost' => number_format($analysis_cost, 6, '.', ''),
                'credits_available' => '10.00', // Valore fisso per debug
                'tokens_used' => $token_count
            );

            wp_send_json_success(array(
                'results' => $formatted_response,
                'message' => __('Analysis completed successfully.', 'document-viewer-plugin'),
                'stats' => $stats_data
            ));
        } else {
            // If there's no description but we have content, just return the content
            error_log("Testo estratto correttamente. In attesa di richiesta di analisi.");
            wp_send_json_success(array(
                'document_content' => $content_to_analyze,
                'message' => __('Document content extracted successfully.', 'document-viewer-plugin')
            ));
        }
    }

    public function chat_model() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Verifica se l'utente è un subscriber non WordPress
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);

        // Se l'utente non è loggato in WordPress e non è un subscriber, blocca l'accesso
        if (!is_user_logged_in() && !$is_subscriber_logged_in) {
            wp_send_json_error(array('message' => __('Devi effettuare il login per utilizzare la chat.', 'document-viewer-plugin')));
            return;
        }

        // Check for developer mode
        $developer_mode = get_option('document_viewer_developer_mode', false);
        $developer_response = get_option('document_viewer_developer_response', '');

        if ($developer_mode && !empty($developer_response)) {
            dv_debug_log('Developer mode active - returning custom response instead of API call', 'primary');
            wp_send_json_success(array('reply' => $developer_response));
            return;
        }

        // Verifica configurazione API
        $api_key = get_option('document_viewer_api_key');
        // Ensure the endpoint always points to the chat completions endpoint
        $api_endpoint = get_option('document_viewer_api_endpoint');
        $api_url = rtrim($api_endpoint, '/') . '/chat/completions';
        $api_model = get_option('document_viewer_model');

        if (empty($api_key)) {
            wp_send_json_error(array('message' => __('API key is not configured', 'document-viewer-plugin')));
            return;
        }

        if (empty($api_endpoint)) {
            wp_send_json_error(array('message' => __('API endpoint is not configured', 'document-viewer-plugin')));
            return;
        }

        if (empty($api_model)) {
            wp_send_json_error(array('message' => __('API model is not configured', 'document-viewer-plugin')));
            return;
        }

        $chat_message = isset($_POST['chat_message']) ? sanitize_text_field($_POST['chat_message']) : '';
        if (empty($chat_message)) {
            wp_send_json_error(array('message' => __('Message is required', 'document-viewer-plugin')));
            return;
        }

        if (strtolower(trim($chat_message)) === 'ping') {
            wp_send_json_success(array('reply' => 'Pong! Connection is working.'));
            return;
        }

        // Get chat history if provided
        $chat_history = isset($_POST['chat_history']) ? $_POST['chat_history'] : '';
        $messages = array();

        // Add system message
        $messages[] = array(
            'role' => 'system',
            'content' => 'You are an assistant that helps users with financial document analysis and questions. Be concise and helpful.'
        );

        // Add chat history if provided
        if (!empty($chat_history)) {
            $history_messages = json_decode(stripslashes($chat_history), true);
            if (is_array($history_messages)) {
                $messages = array_merge($messages, $history_messages);
            }
        }

        // Add current user message
        $messages[] = array(
            'role' => 'user',
            'content' => $chat_message
        );

        // Set up the request body
        $request_body = array(
            'model' => $api_model,
            'messages' => $messages,
            'temperature' => 0.7,
            'max_tokens' => 800
        );

        // Headers for the request
        $headers = array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
            'HTTP-Referer' => site_url(), // Required by OpenRouter
            'X-Title' => 'Document Viewer Plugin', // Identify app to OpenRouter
            'Accept' => 'application/json'
        );

        // Request args
        $args = array(
            'headers' => $headers,
            'body' => wp_json_encode($request_body),
            'method' => 'POST',
            'timeout' => 60,
            'data_format' => 'body',
            'sslverify' => true,
            'redirection' => 5,
            'httpversion' => '1.1',
            'blocking' => true
        );

        dv_debug_log('Sending chat message to API: ' . $chat_message, 'chat');

        // Make the API request
        $response = wp_remote_post($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            dv_debug_log('Chat API request error: ' . $error_message, 'chat');
            wp_send_json_error(array('message' => __('API request failed: ', 'document-viewer-plugin') . $error_message));
            return;
        }

        // Get response code and body
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        dv_debug_log('Chat API response received, status code: ' . $status_code, 'chat');

        // Parse the response
        $data = json_decode($body, true);

        // Check for error responses
        if ($status_code !== 200) {
            $error_message = isset($data['error']) && isset($data['error']['message'])
                ? $data['error']['message']
                : __('API returned status code: ', 'document-viewer-plugin') . $status_code;

            dv_debug_log('Chat API error: ' . $error_message, 'chat');
            wp_send_json_error(array('message' => $error_message));
            return;
        }

        // Extract the AI reply from the response
        $ai_reply = '';
        if (isset($data['choices']) && is_array($data['choices']) && !empty($data['choices'])) {
            $choice = $data['choices'][0];
            if (isset($choice['message']) && isset($choice['message']['content'])) {
                // ChatGPT/GPT-4 format
                $ai_reply = $choice['message']['content'];
            } else if (isset($choice['text'])) {
                // Older GPT-3 format
                $ai_reply = $choice['text'];
            }
        }

        if (empty($ai_reply)) {
            dv_debug_log('Could not extract AI reply from API response', 'chat');
            wp_send_json_error(array('message' => __('Could not extract reply from API response.', 'document-viewer-plugin')));
            return;
        }

        dv_debug_log('Chat AI reply extracted successfully, length: ' . strlen($ai_reply), 'chat');

        // Return success response with the AI reply
        wp_send_json_success(array('reply' => $ai_reply));
    }

    /**
     * Handle chat with AI for Financial Expert Assistant
     */
    public function chat_with_ai() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'financial_expert_assistant_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'document-viewer-plugin')));
            return;
        }

        // Check user authentication
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);
        if (!is_user_logged_in() && !$is_subscriber_logged_in) {
            wp_send_json_error(array('message' => __('Devi effettuare il login per utilizzare la chat.', 'document-viewer-plugin')));
            return;
        }

        // Check for developer mode
        $developer_mode = get_option('document_viewer_developer_mode', false);
        $developer_response = get_option('document_viewer_developer_response', '');

        if ($developer_mode && !empty($developer_response)) {
            dv_debug_log('Developer mode active - returning custom response instead of API call', 'financial_expert');
            wp_send_json_success(array('reply' => $developer_response));
            return;
        }

        // Verify API configuration
        $api_key = get_option('document_viewer_api_key');
        $api_endpoint = get_option('document_viewer_api_endpoint');
        $api_url = rtrim($api_endpoint, '/') . '/chat/completions';
        $api_model = get_option('document_viewer_model');

        if (empty($api_key) || empty($api_endpoint) || empty($api_model)) {
            wp_send_json_error(array('message' => __('API configuration is incomplete', 'document-viewer-plugin')));
            return;
        }

        $message = isset($_POST['message']) ? sanitize_textarea_field($_POST['message']) : '';
        $context = isset($_POST['context']) ? sanitize_text_field($_POST['context']) : '';

        if (empty($message)) {
            wp_send_json_error(array('message' => __('Message is required', 'document-viewer-plugin')));
            return;
        }

        // Prepare messages array
        $messages = array();

        // Add system message for financial expert context
        $messages[] = array(
            'role' => 'system',
            'content' => 'You are a financial expert assistant. You help users analyze financial documents and answer questions about financial data. Be precise, professional, and provide actionable insights. When analyzing data, focus on key financial metrics, trends, and recommendations.'
        );

        // Add user message
        $messages[] = array(
            'role' => 'user',
            'content' => $message
        );

        // Set up the request body
        $request_body = array(
            'model' => $api_model,
            'messages' => $messages,
            'temperature' => 0.7,
            'max_tokens' => 1000
        );

        // Headers for the request
        $headers = array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
            'HTTP-Referer' => site_url(),
            'X-Title' => 'Financial Expert Assistant',
            'Accept' => 'application/json'
        );

        // Request args
        $args = array(
            'headers' => $headers,
            'body' => wp_json_encode($request_body),
            'method' => 'POST',
            'timeout' => 60,
            'data_format' => 'body',
            'sslverify' => true,
            'redirection' => 5,
            'httpversion' => '1.1',
            'blocking' => true
        );

        dv_debug_log('Sending financial expert message to API', 'financial_expert');

        // Make the API request
        $response = wp_remote_post($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            dv_debug_log('Financial Expert API request error: ' . $error_message, 'financial_expert');
            wp_send_json_error(array('message' => __('API request failed: ', 'document-viewer-plugin') . $error_message));
            return;
        }

        // Get response code and body
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        dv_debug_log('Financial Expert API response received, status code: ' . $status_code, 'financial_expert');

        // Parse the response
        $data = json_decode($body, true);

        // Check for error responses
        if ($status_code !== 200) {
            $error_message = isset($data['error']) && isset($data['error']['message'])
                ? $data['error']['message']
                : __('API returned status code: ', 'document-viewer-plugin') . $status_code;

            dv_debug_log('Financial Expert API error: ' . $error_message, 'financial_expert');
            wp_send_json_error(array('message' => $error_message));
            return;
        }

        // Extract the AI reply from the response
        $ai_reply = '';
        if (isset($data['choices']) && is_array($data['choices']) && !empty($data['choices'])) {
            $choice = $data['choices'][0];
            if (isset($choice['message']) && isset($choice['message']['content'])) {
                $ai_reply = $choice['message']['content'];
            } else if (isset($choice['text'])) {
                $ai_reply = $choice['text'];
            }
        }

        if (empty($ai_reply)) {
            dv_debug_log('Could not extract AI reply from Financial Expert API response', 'financial_expert');
            wp_send_json_error(array('message' => __('Could not extract reply from API response.', 'document-viewer-plugin')));
            return;
        }

        dv_debug_log('Financial Expert AI reply extracted successfully, length: ' . strlen($ai_reply), 'financial_expert');

        // Return success response with the AI reply
        wp_send_json_success(array('reply' => $ai_reply));
    }

    /**
     * Save Financial Expert chat conversation
     */
    public function save_financial_expert_chat() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'financial_expert_assistant_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'document-viewer-plugin')));
            return;
        }

        // Check user authentication
        $user_info = $this->get_current_user_info();
        if (!$user_info) {
            wp_send_json_error(array('message' => __('User not authenticated', 'document-viewer-plugin')));
            return;
        }

        $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : '';
        $messages = isset($_POST['messages']) ? $_POST['messages'] : '';
        $analysis_id = isset($_POST['analysis_id']) ? intval($_POST['analysis_id']) : null;

        if (empty($title) || empty($messages)) {
            wp_send_json_error(array('message' => __('Title and messages are required', 'document-viewer-plugin')));
            return;
        }

        // Decode and validate messages
        $messages_array = json_decode(stripslashes($messages), true);
        if (!is_array($messages_array)) {
            wp_send_json_error(array('message' => __('Invalid messages format', 'document-viewer-plugin')));
            return;
        }

        try {
            global $wpdb;

            // Create table if not exists
            $this->create_financial_expert_chats_table();

            $table_name = $wpdb->prefix . 'financial_expert_chats';

            $result = $wpdb->insert(
                $table_name,
                array(
                    'user_id' => $user_info['id'],
                    'user_type' => $user_info['type'],
                    'title' => $title,
                    'messages' => wp_json_encode($messages_array),
                    'analysis_id' => $analysis_id,
                    'created_at' => current_time('mysql')
                ),
                array('%d', '%s', '%s', '%s', '%d', '%s')
            );

            if ($result === false) {
                dv_debug_log('Error saving financial expert chat: ' . $wpdb->last_error, 'financial_expert');
                wp_send_json_error(array('message' => __('Error saving chat', 'document-viewer-plugin')));
                return;
            }

            dv_debug_log('Financial expert chat saved successfully with ID: ' . $wpdb->insert_id, 'financial_expert');
            wp_send_json_success(array('message' => __('Chat saved successfully', 'document-viewer-plugin')));

        } catch (Exception $e) {
            dv_debug_log('Exception saving financial expert chat: ' . $e->getMessage(), 'financial_expert');
            wp_send_json_error(array('message' => __('Error saving chat: ', 'document-viewer-plugin') . $e->getMessage()));
        }
    }

    /**
     * Create financial expert chats table
     */
    private function create_financial_expert_chats_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'financial_expert_chats';
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            user_type varchar(20) NOT NULL DEFAULT 'wordpress',
            title varchar(255) NOT NULL,
            messages longtext NOT NULL,
            analysis_id bigint(20) unsigned DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY user_type (user_type),
            KEY analysis_id (analysis_id),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Get saved chats for Financial Expert Assistant
     */
    public function get_saved_chats() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'financial_expert_assistant_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'document-viewer-plugin')));
            return;
        }

        // Check user authentication
        $user_info = $this->get_current_user_info();
        if (!$user_info) {
            wp_send_json_error(array('message' => __('User not authenticated', 'document-viewer-plugin')));
            return;
        }

        try {
            global $wpdb;

            // Create table if not exists
            $this->create_financial_expert_chats_table();

            $table_name = $wpdb->prefix . 'financial_expert_chats';

            $results = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT id, title, created_at, analysis_id FROM $table_name
                     WHERE user_id = %d AND user_type = %s
                     ORDER BY created_at DESC",
                    $user_info['id'],
                    $user_info['type']
                ),
                ARRAY_A
            );

            if ($results === false) {
                dv_debug_log('Error getting saved chats: ' . $wpdb->last_error, 'financial_expert');
                wp_send_json_error(array('message' => __('Error loading saved chats', 'document-viewer-plugin')));
                return;
            }

            dv_debug_log('Loaded ' . count($results) . ' saved chats for user', 'financial_expert');
            wp_send_json_success($results);

        } catch (Exception $e) {
            dv_debug_log('Exception getting saved chats: ' . $e->getMessage(), 'financial_expert');
            wp_send_json_error(array('message' => __('Error loading saved chats: ', 'document-viewer-plugin') . $e->getMessage()));
        }
    }

    /**
     * Load a saved chat conversation
     */
    public function load_saved_chat() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'financial_expert_assistant_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'document-viewer-plugin')));
            return;
        }

        // Check user authentication
        $user_info = $this->get_current_user_info();
        if (!$user_info) {
            wp_send_json_error(array('message' => __('User not authenticated', 'document-viewer-plugin')));
            return;
        }

        $chat_id = isset($_POST['chat_id']) ? intval($_POST['chat_id']) : 0;

        if (!$chat_id) {
            wp_send_json_error(array('message' => __('Chat ID is required', 'document-viewer-plugin')));
            return;
        }

        try {
            global $wpdb;

            $table_name = $wpdb->prefix . 'financial_expert_chats';

            $chat = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT * FROM $table_name
                     WHERE id = %d AND user_id = %d AND user_type = %s",
                    $chat_id,
                    $user_info['id'],
                    $user_info['type']
                ),
                ARRAY_A
            );

            if (!$chat) {
                wp_send_json_error(array('message' => __('Chat not found', 'document-viewer-plugin')));
                return;
            }

            // Decode messages
            $messages = json_decode($chat['messages'], true);
            if (!is_array($messages)) {
                wp_send_json_error(array('message' => __('Invalid chat data', 'document-viewer-plugin')));
                return;
            }

            // Get analysis data if linked
            $analysis = null;
            if ($chat['analysis_id']) {
                $analysis_table = $wpdb->prefix . 'document_analysis';
                $analysis = $wpdb->get_row(
                    $wpdb->prepare(
                        "SELECT * FROM $analysis_table WHERE id = %d",
                        $chat['analysis_id']
                    ),
                    ARRAY_A
                );
            }

            dv_debug_log('Loaded saved chat: ' . $chat['title'], 'financial_expert');
            wp_send_json_success(array(
                'chat' => $chat,
                'messages' => $messages,
                'analysis' => $analysis
            ));

        } catch (Exception $e) {
            dv_debug_log('Exception loading saved chat: ' . $e->getMessage(), 'financial_expert');
            wp_send_json_error(array('message' => __('Error loading chat: ', 'document-viewer-plugin') . $e->getMessage()));
        }
    }

    /**
     * Delete a saved chat conversation
     */
    public function delete_saved_chat() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'financial_expert_assistant_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'document-viewer-plugin')));
            return;
        }

        // Check user authentication
        $user_info = $this->get_current_user_info();
        if (!$user_info) {
            wp_send_json_error(array('message' => __('User not authenticated', 'document-viewer-plugin')));
            return;
        }

        $chat_id = isset($_POST['chat_id']) ? intval($_POST['chat_id']) : 0;

        if (!$chat_id) {
            wp_send_json_error(array('message' => __('Chat ID is required', 'document-viewer-plugin')));
            return;
        }

        try {
            global $wpdb;

            $table_name = $wpdb->prefix . 'financial_expert_chats';

            $result = $wpdb->delete(
                $table_name,
                array(
                    'id' => $chat_id,
                    'user_id' => $user_info['id'],
                    'user_type' => $user_info['type']
                ),
                array('%d', '%d', '%s')
            );

            if ($result === false) {
                dv_debug_log('Error deleting saved chat: ' . $wpdb->last_error, 'financial_expert');
                wp_send_json_error(array('message' => __('Error deleting chat', 'document-viewer-plugin')));
                return;
            }

            if ($result === 0) {
                wp_send_json_error(array('message' => __('Chat not found or access denied', 'document-viewer-plugin')));
                return;
            }

            dv_debug_log('Deleted saved chat ID: ' . $chat_id, 'financial_expert');
            wp_send_json_success(array('message' => __('Chat deleted successfully', 'document-viewer-plugin')));

        } catch (Exception $e) {
            dv_debug_log('Exception deleting saved chat: ' . $e->getMessage(), 'financial_expert');
            wp_send_json_error(array('message' => __('Error deleting chat: ', 'document-viewer-plugin') . $e->getMessage()));
        }
    }

    public function register_widget() {
        register_widget('Document_Viewer_Widget');
        register_widget('Chat_Model_Widget'); // Registra il nuovo widget
        register_widget('Report_Formatter_Widget'); // Registra il widget Report Formatter
        register_widget('Spreadsheet_Analysis_Widget'); // Registra il widget Spreadsheet Analysis
        register_widget('Word_Analysis_Widget'); // Registra il widget Word Analysis
        register_widget('Balance_Sheet_Analysis_Widget'); // Registra il widget Balance Sheet Analysis
        register_widget('Report_Advanced_Analyzer_Widget'); // Registra il widget Report Advanced Analyzer
    }

    public function export_analysis_pdf() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        if (!$this->check_mpdf()) {
            wp_send_json_error(['message' => 'mPDF not available']);
            return;
        }

        try {
            // Get selected sections flags (new parameters)
            $include_query = isset($_POST['include_query']) ? ($_POST['include_query'] === '1' || $_POST['include_query'] === 'true') : true;
            $include_full_analysis = isset($_POST['include_full_analysis']) ? ($_POST['include_full_analysis'] === '1' || $_POST['include_full_analysis'] === 'true') : true;
            $include_key_points = isset($_POST['include_key_points']) ? ($_POST['include_key_points'] === '1' || $_POST['include_key_points'] === 'true') : true;
            $include_original_doc = isset($_POST['include_original_doc']) ? ($_POST['include_original_doc'] === '1' || $_POST['include_original_doc'] === 'true') : true;

            // Receive HTML content directly - supports both 'analysis_html' (new) and 'analysis' (old)
            $analysis_html = isset($_POST['analysis_html']) ? $_POST['analysis_html'] : (isset($_POST['analysis']) ? $_POST['analysis'] : '');
            $description = isset($_POST['description']) ? sanitize_textarea_field($_POST['description']) : '';
            $annotations = isset($_POST['annotations']) ? sanitize_textarea_field($_POST['annotations']) : '';
            $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : __('Document Analysis Report', 'document-viewer-plugin');
            $uploaded_pdf = isset($_FILES['uploaded_pdf']) ? $_FILES['uploaded_pdf'] : null;
            $custom_logo = isset($_FILES['custom_logo']) ? $_FILES['custom_logo'] : null;

            // Log for debugging
            dv_debug_log('export_analysis_pdf called with the following parameters:');
            dv_debug_log('- include_query: ' . ($include_query ? 'yes' : 'no'));
            dv_debug_log('- include_full_analysis: ' . ($include_full_analysis ? 'yes' : 'no'));
            dv_debug_log('- include_key_points: ' . ($include_key_points ? 'yes' : 'no'));
            dv_debug_log('- include_original_doc: ' . ($include_original_doc ? 'yes' : 'no'));
            dv_debug_log('- using analysis_html param: ' . (isset($_POST['analysis_html']) ? 'yes' : 'no'));
            dv_debug_log('- using analysis param: ' . (isset($_POST['analysis']) ? 'yes' : 'no'));
            dv_debug_log('- content length: ' . strlen($analysis_html));
            dv_debug_log('- is_preview: ' . (isset($_POST['preview_mode']) ? $_POST['preview_mode'] : 'false'));

            // Check if it's a preview request
            $is_preview = isset($_POST['preview_mode']) && $_POST['preview_mode'] === 'true';

            // Create custom mpdf configuration for better document layout and improved image handling
            $mpdf = new \Mpdf\Mpdf([
                'format' => 'A4',
                'margin_left' => 20,
                'margin_right' => 20,
                'margin_top' => 20,
                'margin_bottom' => 20,
                'margin_header' => 10,
                'margin_footer' => 10,
                'simpleTables' => true,
                'use_kwt' => true, // Keep text with tables
                'keep_table_proportions' => true,
                'img_dpi' => 300, // Higher DPI for better image quality
                'dpi' => 96, // Set DPI for overall PDF resolution
                'tempDir' => sys_get_temp_dir(), // Specify temp directory
                'watermarkImgBehind' => false,
                'adjustFontDescLineheight' => 1.5, // Better line height ratio
                'interpolateImages' => true, // Better image rendering
                'useSubstitutions' => true, // Allow font substitutions for better appearance
                'autoLangToFont' => true, // Automatic language to font mapping
                'showWatermarkImage' => true // Ensure watermarks and images are visible
            ]);

            // Set better font and line height for improved readability
            $mpdf->SetDefaultBodyCSS('font-family', 'Arial, sans-serif');
            $mpdf->SetDefaultBodyCSS('line-height', 1.6);

            // Enable embedding external images (crucial for ensuring images are included)
            $mpdf->curlAllowUnsafeSslRequests = true;
            $mpdf->showImageErrors = true;

            // Create CSS to support modern web styling
            $css_file = plugin_dir_path(__FILE__) . 'assets/css/document-viewer.css';

            $css = '';
            if (file_exists($css_file)) {
                $css = file_get_contents($css_file);
            } else {
                // Basic fallback CSS if no file exists
                $css = '
                    body { font-family: Arial, sans-serif; line-height: 1.6; }
                    .analysis-container { margin: 20px 0; }
                    .analysis-content { padding: 15px; }
                    h1, h2, h3 { color: #333; margin: 20px 0 10px 0; }
                    p { margin: 10px 0; }
                ';
            }

            // Import the document's CSS for styling (or use defaults)
            $mpdf->WriteHTML('
            <style>
                /* Base styles for PDF document */
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    color: #333;
                    font-size: 11pt;
                }

                /* Header styling */
                .header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    page-break-inside: avoid;
                }

                .logo {
                    max-height: 60px;
                    max-width: 200px;
                    display: block;
                }

                .title {
                    color: #2c3e50;
                    text-align: center;
                    font-size: 24px;
                    margin: 0;
                    flex-grow: 1;
                    font-weight: bold;
                }

                .date {
                    text-align: right;
                    font-size: 12px;
                    color: #7f8c8d;
                }

                /* Separator line */
                .separator {
                    border: none;
                    border-top: 2px solid #3498db;
                    margin: 15px 0 25px 0;
                }

                /* Section styling */
                .section {
                    margin: 20px 0;
                    padding: 15px;
                    background: #f5f9fc;
                    border-left: 4px solid #3498db;
                    page-break-inside: avoid;
                    border-radius: 4px;
                }

                .analysis-section {
                    margin: 20px 0;
                    padding: 15px;
                    background: #f8f9fa;
                    border-left: 4px solid #2ecc71;
                    border-radius: 4px;
                }

                .notes-section {
                    margin: 20px 0;
                    padding: 15px;
                    background: #fff7f0;
                    border-left: 4px solid #e67e22;
                    page-break-inside: avoid;
                    border-radius: 4px;
                }

                /* Heading styles */
                h1, h2, h3, h4, h5, h6 {
                    color: #2c3e50;
                    margin-top: 0;
                    page-break-after: avoid;
                }

                /* Analysis content styling */
                .analysis-content {
                    color: #34495e;
                    font-family: Arial, sans-serif;
                    font-size: 11pt;
                    line-height: 1.5;
                    background-color: #fcfcfc;
                    padding: 15px;
                    border-radius: 4px;
                    border: 1px solid #eaeaea;
                    margin-top: 10px;
                }

                /* Charts and graphics styling */
                .chart, .graph {
                    max-width: 100%;
                    margin: 15px auto;
                    display: block;
                    page-break-inside: avoid;
                }

                /* Lists and bullet points styling */
                ul, ol {
                    padding-left: 20px;
                    margin-bottom: 15px;
                }

                ul li, ol li {
                    margin-bottom: 5px;
                    line-height: 1.5;
                }

                /* Improved table styling */
                table {
                    border-collapse: collapse;
                    width: 100%;
                    margin: 15px 0;
                    page-break-inside: avoid;
                }

                table, th, td {
                    border: 1px solid #ddd;
                }

                th {
                    background-color: #f2f2f2;
                    padding: 8px;
                    font-weight: bold;
                    text-align: left;
                }

                td {
                    padding: 8px;
                    vertical-align: top;
                }

                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }

                /* Page break control */
                .page-break {
                    page-break-before: always;
                }

                /* Image styling */
                img {
                    max-width: 100%;
                    height: auto !important;
                    display: block;
                    margin: 10px auto;
                }

                /* Code block styling */
                pre, code {
                    background-color: #f8f8f8;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                    padding: 8px;
                    font-family: Consolas, Monaco, monospace;
                    font-size: 10pt;
                    white-space: pre-wrap;
                    word-break: break-word;
                    overflow-x: hidden;
                }

                /* Key points styling */
                .key-points-list {
                    background-color: #fff7f0;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 15px 0;
                }

                .key-points-list li {
                    margin-bottom: 8px;
                    line-height: 1.5;
                }

                /* Preserve analysis styles */
                .analysis-container {
                    position: relative;
                    margin: 15px 0;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background-color: #f9f9f9;
                    padding: 15px;
                }

                .analysis-title {
                    margin-top: 0;
                    padding-bottom: 10px;
                    border-bottom: 1px solid #eee;
                }

                /* Analysis tabs styling */
                .analysis-tab {
                    padding: 8px 12px;
                    font-weight: 500;
                    border-right: 1px solid #e0e0e0;
                    margin-bottom: 0;
                }

                .analysis-tab.active {
                    background-color: #fff;
                    border-bottom: 2px solid #0073aa;
                }

                /* Make sure paragraphs have proper spacing */
                p {
                    margin-bottom: 10px;
                    line-height: 1.5;
                }

                /* Document info styling */
                .document-title {
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    margin: 20px 0;
                    color: #2c3e50;
                }

                /* Preserve additional styling from the document-viewer.css */
                ' . $css . '
            </style>
            ', \Mpdf\HTMLParserMode::HEADER_CSS);

            // Start building HTML content
            $html = '
            <div class="header">
            ';

            // Add logo if uploaded, otherwise use default logo
            if ($custom_logo && !empty($custom_logo['tmp_name'])) {
                try {
                    $logo_mime = mime_content_type($custom_logo['tmp_name']);
                    if (strpos($logo_mime, 'image/') === 0) {
                        $image_data = file_get_contents($custom_logo['tmp_name']);
                        if ($image_data) {
                            $logo_base64 = base64_encode($image_data);
                            $html .= '<div><img class="logo" src="data:' . $logo_mime . ';base64,' . $logo_base64 . '" alt="Logo" /></div>';
                        }
                    }
                } catch (Exception $e) {
                    dv_debug_log("Logo image error: " . $e->getMessage());
                }
            } else {
                               // Default logo or empty space
                $html .= '<div style="width:200px;"></div>';
            }

            // Add title
            $html .= '<h1 class="title">' . esc_html($title) . '</h1>';

            // Add date
            $html .= '<div class="date">' . date('d/m/Y') . '</div>';
            $html .= '</div>';

            // Add separator line
            $html .= '<hr class="separator" />';

            // Add the query/description if selected and present
            if ($include_query && !empty($description)) {
                $html .= '<div class="section">';
                $html .= '<h2>' . __('Analysis Request Detail', 'document-viewer-plugin') . '</h2>';
                $html .= '<p>' . nl2br(esc_html($description)) . '</p>';
                $html .= '</div>';
            }

            // Add the analysis results if selected and with proper HTML formatting
            if ($include_full_analysis && !empty($analysis_html)) {
                $html .= '<div class="analysis-section">';
                $html .= '<h2>' . __('Analysis Results', 'document-viewer-plugin') . '</h2>';
                $html .= '<div class="analysis-content">';

                // Handle HTML content with improved preservation of formatting

                // 1. Remove any script tags for security
                $analysis_html = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $analysis_html);

                // 2. Remove the Edit Analysis button which shouldn't appear in PDFs
                $analysis_html = preg_replace('/<button[^>]*class="edit-analysis-btn"[^>]*>.*?<\/button>/is', '', $analysis_html);

                // 3. Process SVG content to ensure it's properly rendered
                $analysis_html = $this->process_svg_for_pdf($analysis_html);

                // 4. Fix relative URLs in images to absolute
                $site_url = site_url();
                $analysis_html = preg_replace('/src=["\'](\/[^"\']+)["\']/', 'src="' . $site_url . '$1"', $analysis_html);

                // 5. Ensure paragraphs have proper spacing and line breaks are preserved
                if (strpos($analysis_html, '<p') === false) {
                    $analysis_html = '<p>' . str_replace("\n", "</p>\n<p>", $analysis_html) . '</p>';
                }

                // 6. Remove # character from Analisi Completa section
                $analysis_html = str_replace('Analisi #Completa', 'Analisi Completa', $analysis_html);
                $analysis_html = str_replace('#Analisi Completa', 'Analisi Completa', $analysis_html);
                $analysis_html = str_replace('# Analisi Completa', 'Analisi Completa', $analysis_html);
                $analysis_html = str_replace('Analisi # Completa', 'Analisi Completa', $analysis_html);

                // Clean up markdown formatting characters
                $analysis_html = $this->clean_text_for_pdf_export($analysis_html);

                // Add the HTML content with preserved formatting
                $html .= $analysis_html;

                $html .= '</div>'; // Close analysis-content
                $html .= '</div>'; // Close analysis-section
            }

            // Get key points HTML if available and selected
            $key_points_html = isset($_POST['key_points_html']) ? $_POST['key_points_html'] : '';

            // Add the key points section if selected and available
            if ($include_key_points && !empty($key_points_html)) {
                // Add a page break between analysis and key points sections
                if ($include_full_analysis && !empty($analysis_html)) {
                    $html .= '<div class="page-break"></div>';
                }
                $html .= '<div class="analysis-section" style="margin-top:30px; border-left: 4px solid #e67e22;">';
                $html .= '<h2>' . __('Key Points', 'document-viewer-plugin') . '</h2>';
                $html .= '<div class="analysis-content" style="background-color: #fff7f0;">';

                // Remove any script tags for security
                $key_points_html = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $key_points_html);

                // Process SVG content in key points
                $key_points_html = $this->process_svg_for_pdf($key_points_html);

                // Fix relative URLs in images to absolute
                $key_points_html = preg_replace('/src=["\'](\/[^"\']+)["\']/', 'src="' . $site_url . '$1"', $key_points_html);

                // Maintain paragraph structure by preserving paragraph tags
                // If key points are in <li> elements, preserve them and add proper spacing
                if (strpos($key_points_html, '<li>') !== false) {
                    // If there's a <ul> or <ol> tag present, keep it as is
                    if (strpos($key_points_html, '<ul') === false && strpos($key_points_html, '<ol') === false) {
                        // If there's no list container, add it
                        $key_points_html = '<ul class="key-points-list" style="margin-left: 20px; line-height: 1.8;">' . $key_points_html . '</ul>';
                    }

                    // Make sure each list item has proper spacing
                    $key_points_html = preg_replace('/<\/li>\s*<li>/is', '</li>' . "\n" . '<li>', $key_points_html);
                } else {
                    // If it's not in a list format, make sure paragraphs are properly spaced
                    $key_points_html = preg_replace('/<\/p>\s*<p>/is', '</p>' . "\n" . '<p>', $key_points_html);

                    // If there are no paragraph tags at all, add them
                    if (strpos($key_points_html, '<p>') === false) {
                        $lines = explode("\n", strip_tags($key_points_html));
                        $key_points_html = '';
                        foreach ($lines as $line) {
                            if (trim($line) !== '') {
                                $key_points_html .= '<p>' . trim($line) . '</p>' . "\n";
                            }
                        }
                    }
                }

                // Clean up markdown formatting characters
                $key_points_html = $this->clean_text_for_pdf_export($key_points_html);

                // Add the HTML content with preserved formatting
                $html .= $key_points_html;

                $html .= '</div>'; // Close analysis-content
                $html .= '</div>'; // Close analysis-section
            }

            // Add annotations if present
            if (!empty($annotations)) {
                $html .= '<div class="notes-section">';
                $html .= '<h2>' . __('Additional Notes', 'document-viewer-plugin') . '</h2>';
                $html .= '<p>' . nl2br(esc_html($annotations)) . '</p>';
                $html .= '</div>';
            }

            // Flag to track if we've written the HTML to the PDF yet
            $html_written_during_pdf_import = false;

            // Add the original document on a new page if selected
            if ($include_original_doc && $uploaded_pdf && is_array($uploaded_pdf) && $uploaded_pdf['error'] === UPLOAD_ERR_OK) {
                // If the document is a PDF, import it directly
                if ($uploaded_pdf['type'] === 'application/pdf') {
                    try {
                        // Check if the file is readable
                        if (is_readable($uploaded_pdf['tmp_name'])) {
                            // Set the HTML we've created so far
                            $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);
                            $html_written_during_pdf_import = true;

                            // Aggiungi una nuova pagina per il documento originale
                            $mpdf->AddPage();
                              // Aggiungi titolo centrato per la nuova pagina
                            $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);

                            // Add extra space between title and document content
                            $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);

                            // Now import the original PDF as additional pages
                            $pagecount = $mpdf->SetSourceFile($uploaded_pdf['tmp_name']);
                            dv_debug_log("PDF added with $pagecount pages");

                            // Import all pages of the original PDF
                            for ($i = 1; $i <= $pagecount; $i++) {
                                $tplId = $mpdf->ImportPage($i);

                                // Get the imported page's size
                                $pageSize = $mpdf->getTemplateSize($tplId);
                                $pageWidth = $pageSize['width'];
                                $pageHeight = $pageSize['height'];

                                // Posiziona il documento in alto a sinistra invece di centrarlo
                                $x = 0;
                                $y = 0;

                                // Use the template at the original position
                                $mpdf->UseTemplate($tplId, $x, $y);

                                // Add a new page only if it's not the last
                                if ($i < $pagecount) {
                                    $mpdf->AddPage();
                                }
                            }

                            // We've already written all HTML and added the original PDF
                            $html = ''; // Reset HTML to avoid writing it again
                        } else {
                            dv_debug_log("Uploaded PDF is not readable");
                            $html .= '<div style="text-align:center; color:red;">';
                            $html .= __('Unable to read the uploaded document.', 'document-viewer-plugin');
                            $html .= '</div>';
                        }
                    } catch (Exception $e) {
                        dv_debug_log("Error importing PDF: " . $e->getMessage());
                        $html .= '<div style="text-align:center; color:red;">';
                        $html .= __('Error importing document: ', 'document-viewer-plugin') . esc_html($e->getMessage());
                        $html .= '</div>';
                    }
                }
                // Gestione documenti Word (DOC/DOCX)
                else if ($uploaded_pdf['type'] === 'application/msword' || $uploaded_pdf['type'] === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                    try {
                        // Scrivi prima il contenuto HTML esistente
                        $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);
                        $html_written_during_pdf_import = true;

                        // Verifica che PHPWord sia disponibile
                        if (!class_exists('\PhpOffice\PhpWord\PhpWord')) {
                            throw new Exception('PHPWord library not available');
                        }

                        // Conversione del documento Word in PDF
                        dv_debug_log("Converting Word document to PDF for export");

                        // Prepara la directory temporanea
                        $upload_dir = wp_upload_dir();
                        $temp_dir = $upload_dir['basedir'] . '/document-viewer/temp';
                        if (!file_exists($temp_dir)) {
                            wp_mkdir_p($temp_dir);
                        }

                        // Crea un nome univoco per il PDF convertito
                        $temp_pdf_file = $temp_dir . '/' . uniqid('word_') . '.pdf';

                        // Carica il documento Word
                        $phpWord = \PhpOffice\PhpWord\IOFactory::load($uploaded_pdf['tmp_name']);

                        // Configura il rendering PDF
                        \PhpOffice\PhpWord\Settings::setPdfRendererName(\PhpOffice\PhpWord\Settings::PDF_RENDERER_MPDF);
                        \PhpOffice\PhpWord\Settings::setPdfRendererPath(plugin_dir_path(__FILE__) . 'vendor/mpdf/mpdf');

                        // Salva come PDF
                        $pdfWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'PDF');
                        $pdfWriter->save($temp_pdf_file);

                        dv_debug_log("Word document converted to PDF: $temp_pdf_file");

                        // Aggiungi una nuova pagina per il documento convertito
                        $mpdf->AddPage();
                          // Aggiungi titolo centrato per la nuova pagina
                        $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);

                        // Add extra space between title and document content
                        $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);

                        // Importa tutte le pagine del PDF convertito
                        if (file_exists($temp_pdf_file) && is_readable($temp_pdf_file)) {
                            $pagecount = $mpdf->SetSourceFile($temp_pdf_file);
                            dv_debug_log("Converted PDF added with $pagecount pages");

                            // Importa tutte le pagine del PDF convertito
                            for ($i = 1; $i <= $pagecount; $i++) {
                                $tplId = $mpdf->ImportPage($i);

                                // Get the imported page's size
                                $pageSize = $mpdf->getTemplateSize($tplId);
                                $pageWidth = $pageSize['width'];
                                $pageHeight = $pageSize['height'];

                                // Posiziona il documento in alto a sinistra invece di centrarlo
                                $x = 0;
                                $y = 0;

                                // Use the template at the original position
                                $mpdf->UseTemplate($tplId, $x, $y);

                                // Aggiungi una nuova pagina solo se non è l'ultima
                                if ($i < $pagecount) {
                                    $mpdf->AddPage();
                                }
                            }

                            // Elimina il file temporaneo
                            @unlink($temp_pdf_file);
                        } else {
                            throw new Exception('Error accessing converted PDF');
                        }

                        // Abbiamo già scritto tutto l'HTML e aggiunto il PDF convertito
                        $html = ''; // Azzera HTML per evitare di scriverlo nuovamente
                    } catch (Exception $e) {
                        dv_debug_log("Error processing Word document: " . $e->getMessage());

                        // Se la conversione fallisce, procedi con un riquadro con messaggio
                        $mpdf->AddPage();
                        $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);
                        $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);
                        $mpdf->WriteHTML('<div style="text-align:center; padding:20px; border:1px solid #ddd; background-color:#f8f8f8;">' .
                                        __('Il documento Word originale non può essere visualizzato: ', 'document-viewer-plugin') .
                                        esc_html($e->getMessage()) . '</div>', \Mpdf\HTMLParserMode::HTML_BODY);

                        // Reset HTML content
                        $html = '';
                    }
                }
                else if (strpos($uploaded_pdf['type'], 'image/') === 0) {
                    // Per le immagini, aggiungere una nuova pagina e mostrare l'immagine centrata
                    try {
                        // Scrivi prima il contenuto HTML esistente
                        $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);
                        $html_written_during_pdf_import = true;

                        // Aggiungi una nuova pagina per l'immagine
                        $mpdf->AddPage();
                          // Titolo della pagina
                        $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);

                        // Add extra space between title and document content
                        $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);

                        // Ottieni dimensioni originali dell'immagine
                        $img_info = getimagesize($uploaded_pdf['tmp_name']);
                        $img_width = $img_info[0];
                        $img_height = $img_info[1];

                        // Calcola dimensioni massime per il PDF (l'80% della pagina)
                        $max_width = $mpdf->w * 0.8;
                        $max_height = $mpdf->h * 0.6; // Lascia spazio per il titolo

                        // Calcola il rapporto di scala mantenendo le proporzioni
                        $scale = min($max_width / $img_width, $max_height / $img_height);
                        $new_width = $img_width * $scale;
                        $new_height = $img_height * $scale;

                        // Embed dell'immagine con styling per il centraggio e dimensionamento corretto
                        $image_data = file_get_contents($uploaded_pdf['tmp_name']);
                        $image_mime = mime_content_type($uploaded_pdf['tmp_name']);
                        $base64 = base64_encode($image_data);

                        // Tenta di convertire l'immagine in un formato vettoriale (SVG) se possibile
                        // Questo può migliorare la qualità nell'output PDF
                        $image_html = '<div style="text-align:center; width:100%; padding:20px; display:flex; justify-content:center; align-items:center;">' .
                                    '<img src="data:' . $image_mime . ';base64,' . $base64 . '" ' .
                                    'width="' . $new_width . '" height="' . $new_height . '" ' .
                                    'style="max-width:' . $new_width . 'px; margin:0 auto; display:block; object-fit:contain;" />' .
                                    '</div>';

                        $mpdf->WriteHTML($image_html, \Mpdf\HTMLParserMode::HTML_BODY);

                        // Reset HTML content since we've already written it
                        $html = '';
                    } catch (Exception $e) {
                        dv_debug_log("Error embedding image: " . $e->getMessage());
                          // Se l'incorporamento fallisce, aggiungi un messaggio di errore
                        $mpdf->AddPage();
                        $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);
                        $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);
                        $mpdf->WriteHTML('<div style="text-align:center; color:red; padding:20px;">' .
                                       __('Non è stato possibile incorporare l\'immagine: ', 'document-viewer-plugin') .
                                       esc_html($e->getMessage()) . '</div>', \Mpdf\HTMLParserMode::HTML_BODY);

                        $html = '';
                    }
                } else {
                    // Se non è un PDF o un'immagine, scrivi prima il contenuto HTML
                    $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);
                    $html_written_during_pdf_import = true;

                    // Aggiungi una nuova pagina per il messaggio informativo
                    $mpdf->AddPage();
                      // Titolo della pagina
                    $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);

                    // Add extra space between title and document content
                    $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);

                    // Messaggio informativo
                    $mpdf->WriteHTML('<div style="text-align:center; padding:50px;">' .
                                    __('Il documento originale (tipo: ' . esc_html($uploaded_pdf['type']) . ') non può essere visualizzato direttamente.', 'document-viewer-plugin') .
                                    '</div>', \Mpdf\HTMLParserMode::HTML_BODY);

                    // Reset HTML content
                    $html = '';
                }
            }

            // Add footer with page numbers
            $mpdf->SetHTMLFooter('<div style="text-align: center; font-size: 10pt; color: #7f8c8d;">' . __('Page {PAGENO} of {nb}', 'document-viewer-plugin') . '</div>');

            // Set metadata
            $mpdf->SetTitle($title);
            $mpdf->SetAuthor(get_bloginfo('name'));
            $mpdf->SetCreator('Document Viewer Plugin');

            // Write HTML to PDF if we haven't already written it when importing PDF
            if (!$html_written_during_pdf_import && !empty($html)) {
                $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);
            }

            // Check if it's a preview request
            if ($is_preview) {
                // Return base64 encoded PDF for preview
                $pdfData = $mpdf->Output('', 'S');
                wp_send_json_success([
                    'pdf_data' => base64_encode($pdfData)
                ]);
                return;
            }

            // Output PDF for download
            $filename = sanitize_file_name($title . ' - Analysis.pdf');

            if (headers_sent()) {
                wp_die(__('Headers already sent, cannot output PDF', 'document-viewer-plugin'));
            }

            // Clear all existing output buffers
            while (ob_get_level()) {
                ob_end_clean();
            }

            // Send appropriate headers
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            // Output the PDF directly
            echo $mpdf->Output($filename, 'S');
            exit;
        } catch (Exception $e) {
            dv_debug_log('PDF export error: ' . $e->getMessage());
            wp_send_json_error(['message' => 'Error exporting PDF: ' . $e->getMessage()]);
        }
    }

    // Check if PDF Parser is available
    private function check_pdf_parser() {
        return document_management()->check_pdf_parser();
    }

    // Extract content from PDF
    private function extract_pdf_content($file_path) {
        return document_management()->extract_pdf_content($file_path);
    }

    // Clean up extracted text for better analysis
    private function clean_extracted_text($text) {
        return document_management()->clean_extracted_text($text);
    }

    // Check if PHPWord is available
    private function check_phpword() {
        return document_management()->check_phpword();
    }

    // Check if mPDF is available
    public function check_mpdf() {
        // Load vendor libraries first
        $autoload_path = plugin_dir_path(__FILE__) . 'vendor/autoload.php';
        dv_debug_log('Checking autoload path: ' . $autoload_path, 'pdf');

        if (!file_exists($autoload_path)) {
            dv_debug_log('Autoload file not found: ' . $autoload_path, 'pdf');
            return false;
        }

        dv_debug_log('Loading autoloader...', 'pdf');
        require_once $autoload_path;

        // Check if mPDF class exists
        dv_debug_log('Checking if Mpdf\\Mpdf class exists...', 'pdf');
        if (!class_exists('\Mpdf\Mpdf')) {
            dv_debug_log('mPDF non disponibile: classe \Mpdf\Mpdf non trovata', 'pdf');

            // Additional debugging - check if the file exists manually
            $mpdf_file = plugin_dir_path(__FILE__) . 'vendor/mpdf/src/Mpdf.php';
            dv_debug_log('Checking mPDF file directly: ' . $mpdf_file . ' - exists: ' . (file_exists($mpdf_file) ? 'YES' : 'NO'), 'pdf');

            return false;
        }

        dv_debug_log('mPDF class found, attempting to create instance...', 'pdf');

        // Try to create an instance to verify it works
        try {
            $mpdf = new \Mpdf\Mpdf(['tempDir' => sys_get_temp_dir()]);
            dv_debug_log('mPDF disponibile e funzionante', 'pdf');
            return true;
        } catch (\Mpdf\MpdfException $e) {
            dv_debug_log('mPDF specific error: ' . $e->getMessage(), 'pdf');
            return false;
        } catch (\Exception $e) {
            dv_debug_log('mPDF non funzionante: ' . $e->getMessage(), 'pdf');
            return false;
        } catch (\Error $e) {
            dv_debug_log('mPDF fatal error: ' . $e->getMessage(), 'pdf');
            return false;
        }
    }

    // Extract content from DOCX
    private function extract_docx_content($file_path) {
        return document_management()->extract_docx_content($file_path);
    }

    // Extract text from PHPWord element
    private function extract_element_text($element) {
        return document_management()->extract_element_text($element);
    }

    /**
     * Estimate token count from text content
     *
     * @param string $text The text to analyze
     * @return int Estimated token count
     */
    private function estimate_token_count($text) {
        if (empty($text)) {
            return 0;
        }

        // Remove HTML tags and extra whitespace
        $clean_text = strip_tags($text);
        $clean_text = preg_replace('/\s+/', ' ', trim($clean_text));

        // Estimate tokens: approximately 4 characters per token for Italian/English text
        // This is a rough approximation - actual tokenization depends on the specific model
        return ceil(strlen($clean_text) / 4);
    }

    /**
     * Extract document text without analysis (AJAX handler)
     */
    public function extract_document_text() {
        dv_debug_log("=== INIZIO extract_document_text() ===");
        dv_debug_log("POST data: " . print_r($_POST, true));

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
            dv_debug_log("Nonce verification FAILED");
            wp_die('Security check failed');
        }

        dv_debug_log("Nonce verification SUCCESS");

        try {
            // Handle file upload - only extract content, don't analyze
            if (isset($_FILES['document_file'])) {
                $file = $_FILES['document_file'];
                dv_debug_log("Text extraction request for file: " . $file['name']);

                if ($file['error'] !== UPLOAD_ERR_OK) {
                    $error_message = $this->get_upload_error_message($file['error']);
                    throw new Exception($error_message);
                }

                // Verify temp file exists and is readable
                if (!is_readable($file['tmp_name'])) {
                    throw new Exception('Uploaded file is not readable.');
                }

                // Create a unique temporary file
                $temp_dest = wp_upload_dir()['basedir'] . '/temp_' . uniqid() . '_' . sanitize_file_name($file['name']);

                if (!move_uploaded_file($file['tmp_name'], $temp_dest)) {
                    throw new Exception('Failed to move uploaded file.');
                }

                // Get MIME type
                $mime_type = mime_content_type($temp_dest);
                dv_debug_log("File MIME type: " . $mime_type);

                $extracted_content = '';

                // Extract content based on file type
                switch ($mime_type) {
                    case 'application/pdf':
                        dv_debug_log("Starting PDF text extraction");
                        $extracted_content = document_management()->extract_pdf_content($temp_dest);
                        break;

                    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                    case 'application/msword':
                        dv_debug_log("Starting Word document text extraction");
                        $extracted_content = document_management()->extract_docx_content($temp_dest);
                        break;

                    case 'text/plain':
                        dv_debug_log("Starting plain text extraction");
                        $extracted_content = file_get_contents($temp_dest);
                        $extracted_content = $this->clean_extracted_text($extracted_content);
                        break;

                    default:
                        throw new Exception('Unsupported file type: ' . $mime_type);
                }

                // Cleanup
                if (file_exists($temp_dest)) {
                    unlink($temp_dest);
                }

                // Clean the extracted text
                $extracted_content = $this->clean_extracted_text($extracted_content);

                if (empty($extracted_content)) {
                    throw new Exception('No text could be extracted from the document.');
                }

                dv_debug_log("Text extraction successful. Content length: " . strlen($extracted_content));

                wp_send_json_success(array(
                    'extracted_content' => $extracted_content,
                    'content_length' => strlen($extracted_content),
                    'message' => 'Text extracted successfully'
                ));

            } else {
                throw new Exception('No file uploaded.');
            }

        } catch (Exception $e) {
            dv_debug_log("Text extraction error: " . $e->getMessage(), 'error');
            wp_send_json_error(array(
                'message' => $e->getMessage()
            ));
        }
    }

    // Process SVG content for PDF rendering
    private function process_svg_for_pdf($html) {
        // Convert SVG elements to a format that mPDF can handle better
        // mPDF has limited SVG support, so we'll try to convert simple SVGs to images or remove complex ones

        // Remove complex SVG elements that mPDF can't handle
        $html = preg_replace('/<svg[^>]*>.*?<\/svg>/is', '[SVG Content - Not supported in PDF]', $html);

        // Convert simple inline SVG icons to Unicode equivalents where possible
        $svg_replacements = [
            // Common icons
            '<svg[^>]*check[^>]*>.*?</svg>' => '✓',
            '<svg[^>]*cross[^>]*>.*?</svg>' => '✗',
            '<svg[^>]*arrow[^>]*>.*?</svg>' => '→',
            '<svg[^>]*star[^>]*>.*?</svg>' => '★',
            '<svg[^>]*heart[^>]*>.*?</svg>' => '♥',
        ];

        foreach ($svg_replacements as $pattern => $replacement) {
            $html = preg_replace('/' . preg_quote($pattern, '/') . '/i', $replacement, $html);
        }

        return $html;
    }

    /**
     * Clean up text for PDF export by removing markdown formatting characters
     * - Removes asterisks (*)
     * - Removes hashtags (#)
     * - Removes backslashes before apostrophes (\')
     *
     * @param string $text The text to clean
     * @return string The cleaned text
     */
    private function clean_text_for_pdf_export($text) {
        if (empty($text)) {
            return $text;
        }

        // Remove backslashes before apostrophes
        $text = str_replace('\\\'', '\'', $text);

        // Remove asterisks used for formatting
        $text = str_replace('*', '', $text);

        // Remove hashtags used for markdown headings
        $text = str_replace('#', '', $text);

        return $text;
    }

    // Get upload error message
    private function get_upload_error_message($error_code) {
        return document_management()->get_upload_error_message($error_code);
    }

    /**
     * Convert Word document to PDF for visualization
     */
    public function convert_word_to_pdf() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Verifica se l'utente è un subscriber non WordPress
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);

        // Verifica permessi: utenti WordPress con upload_files o utenti esterni autenticati
        if (!current_user_can('upload_files') && !$is_subscriber_logged_in) {
            wp_send_json_error(['message' => __('Permission denied.', 'document-viewer-plugin')]);
            return;
        }

        if (empty($_FILES['word_file']) || $_FILES['word_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(['message' => __('No Word file uploaded or upload error.', 'document-viewer-plugin')]);
            return;
        }

        $file = $_FILES['word_file'];
        $result = document_management()->convert_word_to_pdf($file);

        if ($result['success']) {
            wp_send_json_success([
                'pdf_url' => $result['pdf_url'],
                'pdf_content' => $result['pdf_content']
            ]);
        } else {
           wp_send_json_error(['message' => $result['message']]);
        }
    }

    public function get_shared_log() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Verifica che l'utente sia loggato
        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => __('Devi effettuare il login per visualizzare il log.', 'document-viewer-plugin')]);
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Permission denied.', 'document-viewer-plugin')]);
            return;
        }

        $context = isset($_POST['context']) ? sanitize_text_field($_POST['context']) : 'all';

        $log_content = dv_get_shared_log($context);
        wp_send_json_success(['content' => $log_content]);
    }

    /**
     * Gestisce il salvataggio dell'analisi del documento nel database
     */

    public function save_document_analysis() {
        // Verifica il nonce per la sicurezza
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Verifica se l'utente è un subscriber non WordPress
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);

        // Se l'utente non è loggato in WordPress e non è un subscriber, blocca l'accesso
        if (!is_user_logged_in() && !$is_subscriber_logged_in) {
            wp_send_json_error(['message' => __('Devi effettuare il login per salvare l\'analisi.', 'document-viewer-plugin')]);
            return;
        }

        dv_debug_log('Avvio salvataggio analisi documento');

        // Recupera i dati inviati
        $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : '';
        $query = isset($_POST['query']) ? sanitize_textarea_field($_POST['query']) : '';
        $analysis_results = isset($_POST['analysis_results']) ? $_POST['analysis_results'] : '';

        // Verifica la presenza dei dati necessari
        if (empty($title) || empty($analysis_results)) {
            dv_debug_log('Dati mancanti per il salvataggio. Titolo: ' . ($title ? 'presente' : 'mancante') . ', Analisi: ' . ($analysis_results ? 'presente' : 'mancante'));
            wp_send_json_error(['message' => __('Titolo e risultati dell\'analisi sono obbligatori.', 'document-viewer-plugin')]);
            return;
        }

        // Crea directory per i file se non esiste
        $upload_dir = wp_upload_dir();
        $document_dir = $upload_dir['basedir'] . '/document-viewer/saved';
        if (!file_exists($document_dir)) {
            wp_mkdir_p($document_dir);

            // Proteggi la directory
            $htaccess_file = $document_dir . '/.htaccess';
            if (!file_exists($htaccess_file)) {
                file_put_contents($htaccess_file, "# Protect Directory\nOptions -Indexes\n");
            }
        }

        // Percorsi dei file da salvare
        $logo_path = '';
        $document_path = '';

        // Gestisci upload del logo
        if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            $logo_file = $_FILES['logo'];
            $logo_name = sanitize_file_name($logo_file['name']);
            $logo_path_temp = $document_dir . '/' . uniqid('logo_') . '_' . $logo_name;

            // Controlla che sia un'immagine
            $logo_type = wp_check_filetype($logo_name);
            if (strpos($logo_type['type'], 'image/') === 0) {
                if (move_uploaded_file($logo_file['tmp_name'], $logo_path_temp)) {
                    $logo_path = $logo_path_temp;
                    dv_debug_log('Logo salvato con successo: ' . $logo_path);
                }
            } else {
                dv_debug_log('Il file del logo non è un\'immagine valida');
            }
        }

        // Gestisci upload del documento
        if (isset($_FILES['document']) && $_FILES['document']['error'] === UPLOAD_ERR_OK) {
            $document_file = $_FILES['document'];
            $document_name = sanitize_file_name($document_file['name']);
            $document_path_temp = $document_dir . '/' . uniqid('doc_') . '_' . $document_name;

            // Verifica il tipo di file
            $allowed_types = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png', 'image/gif'];

            if (in_array($document_file['type'], $allowed_types) || strpos($document_file['type'], 'image/') === 0) {
                if (move_uploaded_file($document_file['tmp_name'], $document_path_temp)) {
                    $document_path = $document_path_temp;
                    dv_debug_log('Documento salvato con successo: ' . $document_path);
                }
            } else {
                dv_debug_log('Tipo di documento non supportato: ' . $document_file['type']);
            }
        }

        // Salva i dati nel database
        global $wpdb;
        $table_name = $wpdb->prefix . 'document_analysis';

        // Verifica che la tabella esista
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            $this->create_document_analysis_table();
        }

        // Prepara i dati per l'inserimento
        $user_id = get_current_user_id();

        // Se l'utente è un sottoscrittore esterno (non WordPress)
        if ($user_id === 0 && $is_subscriber_logged_in) {
            // Ottieni i dati del sottoscrittore
            $subscriber_data = [];
            try {
                $subscriber_data = json_decode(base64_decode($_COOKIE['fa_subscriber_login']), true);
            } catch (Exception $e) {
                dv_debug_log('Errore nella decodifica dei dati del sottoscrittore: ' . $e->getMessage());
            }

            // Usa l'ID del sottoscrittore o un valore speciale
            $external_user_id = isset($subscriber_data['id']) ? intval($subscriber_data['id']) : 999999;

            // Assicurati che l'ID sia sempre positivo e non zero
            $user_id = max(1, $external_user_id);

            dv_debug_log('Utente sottoscrittore esterno rilevato, ID assegnato: ' . $user_id);
        }

        $data = [
            'user_id' => $user_id,
            'title' => $title,
            'query' => $query,
            'analysis_results' => $analysis_results,
            'created_at' => current_time('mysql')
        ];

        // Aggiungi percorso del logo se presente
        if (!empty($logo_path)) {
            $data['logo_path'] = $logo_path;
        }

        // Aggiungi percorso del documento se presente
        if (!empty($document_path)) {
            $data['document_path'] = $document_path;
        }

        // Debug dei dati prima dell'inserimento
        dv_debug_log('Preparazione inserimento dati nel DB: ' . print_r($data, true));

        // Inserisci i dati nel database
        $result = $wpdb->insert(
            $table_name,
            $data,
            ['%d', '%s', '%s', '%s', '%s', '%s', '%s']
        );

        if ($result === false) {
            dv_debug_log('Errore durante l\'inserimento nel database: ' . $wpdb->last_error);
            wp_send_json_error(['message' => __('Errore nel salvataggio dell\'analisi. Dettagli: ', 'document-viewer-plugin') . $wpdb->last_error]);
            return;
        }

        $analysis_id = $wpdb->insert_id;
        dv_debug_log('Analisi salvata con successo. ID: ' . $analysis_id);

        // Calcolo dei token utilizzati - utilizziamo il contenuto dell'analisi salvata
        $token_count = 0;
        if (!empty($analysis_results)) {
            // Stima il numero di token (circa 4 caratteri per token in italiano)
            $token_count = strlen(strip_tags($analysis_results)) / 4;
            dv_debug_log('Stima token utilizzati per l\'analisi: ' . $token_count);
        }

        // Attiva l'azione save_document_analysis per aggiornare le statistiche
        do_action('save_document_analysis', $analysis_id, $data, $token_count);

        wp_send_json_success([
            'message' => __('Analisi salvata con successo.', 'document-viewer-plugin'),
            'analysis_id' => $analysis_id
        ]);
    }

    public function create_document_analysis_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'document_analysis';
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            title VARCHAR(255) NOT NULL,
            logo_path VARCHAR(255),
            query TEXT NOT NULL,
            analysis_results LONGTEXT NOT NULL,
            document_path VARCHAR(255),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY (id),
            KEY user_id (user_id)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
    }

    /**
     * Initialize database tables using the database-setup.php functions
     *
     * This is called on plugin activation
     */
    public function initialize_database_tables() {
        // Include the database setup file
        require_once plugin_dir_path(__FILE__) . 'includes/database-setup.php';

        // Call the function to initialize tables
        wpcd_initialize_database_tables();

        // Log the operation
        dv_debug_log('Database tables initialized for non-WP users statistics');
    }

    /**
     * Initialize Planning & Simulation widget help content
     */
    public function initialize_planning_simulation_help() {
        if (class_exists('Planning_Simulation_Widget')) {
            $widget = new Planning_Simulation_Widget();
            if (method_exists($widget, 'initialize_help_content')) {
                $widget->initialize_help_content();
            }
        }
    }

    /**
     * Register Planning & Simulation test page (debug mode only)
     */
    public function register_planning_simulation_test_page() {
        // Only register if we're in admin and debug mode is enabled
        if (is_admin() && current_user_can('manage_options')) {
            add_action('admin_menu', function() {
                add_submenu_page(
                    null, // No parent menu (hidden)
                    'Planning & Simulation Test',
                    'Planning & Simulation Test',
                    'manage_options',
                    'planning-simulation-test',
                    array($this, 'render_planning_simulation_test_page')
                );
            });
        }
    }

    /**
     * Render Planning & Simulation test page
     */
    public function render_planning_simulation_test_page() {
        // Include the test content safely
        include plugin_dir_path(__FILE__) . 'test-planning-simulation.php';
    }

    /**
     * Force creation of formatted documents table
     *
     * This can be called manually to fix missing table issues
     */
    public function force_create_formatted_documents_table() {
        // Include the database setup file
        require_once plugin_dir_path(__FILE__) . 'includes/database-setup.php';

        // Call the function to create the table
        wpcd_create_formatted_documents_table();

        // Log the operation
        dv_debug_log('Formatted documents table created manually');
    }

    // Funzione per recuperare le richieste predefinite dal database
    public function get_preset_queries() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'document_preset_queries';

        // Verifica se la tabella esiste
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            // Se la tabella non esiste, crea alcune richieste predefinite di esempio
            return [
                ['id' => 1, 'title' => 'Analisi finanziaria completa', 'query_text' => 'Analizza questo documento finanziario e fornisci una panoramica completa includendo punti di forza, debolezze, opportunità e rischi. Evidenzia i principali indicatori finanziari.'],
                ['id' => 2, 'title' => 'Sintesi per investitori', 'query_text' => 'Crea una sintesi di questo documento ottimizzata per potenziali investitori, evidenziando opportunità di investimento, rendimenti previsti e livelli di rischio.'],
                ['id' => 3, 'title' => 'Valutazione rischi', 'query_text' => 'Identifica e analizza tutti i potenziali rischi menzionati in questo documento, classificandoli per probabilità e impatto potenziale.'],
                ['id' => 4, 'title' => 'Conformità normativa', 'query_text' => 'Valuta la conformità di questo documento alle normative finanziarie attuali, evidenziando eventuali aree problematiche o che richiedono ulteriore attenzione.']
            ];
        }

        // Recupera le richieste predefinite dal database
        $queries = $wpdb->get_results("SELECT * FROM $table_name ORDER BY title ASC", ARRAY_A);

        // Se non ci sono richieste nel database, restituisci array vuoto
        if (empty($queries)) {
            return [];
        }

        return $queries;
    }

    // Funzione per creare la tabella delle richieste predefinite
    public function create_preset_queries_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'document_preset_queries';
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            query_text TEXT NOT NULL,
            created_by BIGINT(20) UNSIGNED NOT NULL DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);

        // Inserisci richieste predefinite solo se la tabella è vuota
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");

        if ($count == 0) {
            $preset_queries = [
                [
                    'title' => 'Analisi finanziaria completa',
                    'query_text' => 'Analizza questo documento finanziario e fornisci una panoramica completa includendo punti di forza, debolezze, opportunità e rischi. Evidenzia i principali indicatori finanziari.'
                ],
                [
                    'title' => 'Sintesi per investitori',
                    'query_text' => 'Crea una sintesi di questo documento ottimizzata per potenziali investitori, evidenziando opportunità di investimento, rendimenti previsti e livelli di rischio.'
                ],
                [
                    'title' => 'Valutazione rischi',
                    'query_text' => 'Identifica e analizza tutti i potenziali rischi menzionati in questo documento, classificandoli per probabilità e impatto potenziale.'
                ],
                [
                    'title' => 'Conformità normativa',
                    'query_text' => 'Valuta la conformità di questo documento alle normative finanziarie attuali, evidenziando eventuali aree problematiche o che richiedono ulteriore attenzione.'
                ]
            ];
            
            foreach ($preset_queries as $query) {
                $wpdb->insert(
                    $table_name,
                    [
                        'title' => $query['title'],
                        'query_text' => $query['query_text'],
                        'created_by' => get_current_user_id() ?: 1
                    ],
                    ['%s', '%s', '%d']
                );
            }
        }
    }





    /**
     * Extract analysis result from API response
     *
     * @param array $data The decoded JSON response from the API
     * @return string The extracted analysis content
     */
    private function extract_analysis_result($data) {
        // Log the response structure for debugging
        dv_debug_log('Extracting analysis result from API response structure', 'analysis');

        // Check for the standard OpenAI/OpenRouter response format
        if (isset($data['choices'][0]['message']['content'])) {
            $content = $data['choices'][0]['message']['content'];
            dv_debug_log('Analysis content extracted from choices[0].message.content', 'analysis');
            return trim($content);
        }

        // Check for alternative response formats
        if (isset($data['response'])) {
            dv_debug_log('Analysis content extracted from response field', 'analysis');
            return trim($data['response']);
        }

        if (isset($data['content'])) {
            dv_debug_log('Analysis content extracted from content field', 'analysis');
            return trim($data['content']);
        }

        if (isset($data['text'])) {
            dv_debug_log('Analysis content extracted from text field', 'analysis');
            return trim($data['text']);
        }

        // If we can't find the content in expected places, log the structure
        dv_debug_log('Could not extract analysis content. Response structure: ' . print_r($data, true), 'analysis');

        return '';
    }

    /**
     * Report Formatter Widget AJAX Methods
     */

    /**
     * Load analyses for Report Formatter Widget
     */
    public function rf_load_analyses() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'document_analysis';

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        $analyses = $wpdb->get_results($wpdb->prepare(
            "SELECT id, title, created_at, analysis_results
             FROM $table_name
             WHERE user_id = %d
             ORDER BY created_at DESC
             LIMIT 50",
            $user_id
        ));

        // Format data for frontend
        $formatted_analyses = array();
        foreach ($analyses as $analysis) {
            $formatted_analyses[] = array(
                'id' => $analysis->id,
                'title' => $analysis->title,
                'created_at' => date('d/m/Y H:i', strtotime($analysis->created_at)),
                'preview' => substr(strip_tags($analysis->analysis_results), 0, 100) . '...'
            );
        }

        wp_send_json_success($formatted_analyses);
    }

    /**
     * Load formatted documents for Report Formatter Widget
     */
    public function rf_load_documents() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        global $wpdb;
        $table_name = 'wpcd_formatted_documents';

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        $documents = $wpdb->get_results($wpdb->prepare(
            "SELECT id, title, theme_type, status, created_at, updated_at
             FROM $table_name
             WHERE user_id = %d
             ORDER BY updated_at DESC
             LIMIT 50",
            $user_id
        ));

        // Format data for frontend
        $formatted_documents = array();
        foreach ($documents as $document) {
            $formatted_documents[] = array(
                'id' => $document->id,
                'title' => $document->title,
                'theme_type' => $document->theme_type,
                'status' => $document->status,
                'created_at' => date('d/m/Y H:i', strtotime($document->created_at)),
                'updated_at' => date('d/m/Y H:i', strtotime($document->updated_at))
            );
        }

        wp_send_json_success($formatted_documents);
    }

    /**
     * Save formatted document
     */
    public function rf_save_document() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        // Rimuovi gli slash aggiunti automaticamente da WordPress
        $title = isset($_POST['title']) ? stripslashes(sanitize_text_field($_POST['title'])) : '';
        $content = isset($_POST['content']) ? stripslashes(wp_kses_post($_POST['content'])) : '';
        $theme = isset($_POST['theme']) ? sanitize_text_field($_POST['theme']) : 'professional_gray';
        $metadata = isset($_POST['metadata']) ? stripslashes(sanitize_text_field($_POST['metadata'])) : '';
        $document_id = isset($_POST['document_id']) ? intval($_POST['document_id']) : null;

        if (empty($title) || empty($content)) {
            wp_send_json_error(['message' => 'Title and content are required']);
            return;
        }

        global $wpdb;
        $table_name = 'wpcd_formatted_documents';

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        $data = array(
            'user_id' => $user_id,
            'title' => $title,
            'content' => $content,
            'theme_type' => $theme,
            'document_metadata' => $metadata,
            'status' => 'draft'
        );

        if ($document_id) {
            // Update existing document
            $result = $wpdb->update(
                $table_name,
                $data,
                array('id' => $document_id, 'user_id' => $user_id),
                array('%d', '%s', '%s', '%s', '%s', '%s'),
                array('%d', '%d')
            );

            if ($result !== false) {
                wp_send_json_success(array(
                    'id' => $document_id,
                    'message' => 'Document updated successfully'
                ));
            } else {
                wp_send_json_error(['message' => 'Failed to update document']);
            }
        } else {
            // Create new document
            $result = $wpdb->insert($table_name, $data, array('%d', '%s', '%s', '%s', '%s', '%s'));

            if ($result !== false) {
                $new_id = $wpdb->insert_id;
                wp_send_json_success(array(
                    'id' => $new_id,
                    'message' => 'Document saved successfully'
                ));
            } else {
                wp_send_json_error(['message' => 'Failed to save document']);
            }
        }
    }

    /**
     * Load specific formatted document
     */
    public function rf_load_document() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        $document_id = isset($_POST['document_id']) ? intval($_POST['document_id']) : 0;

        if (!$document_id) {
            wp_send_json_error(['message' => 'Document ID required']);
            return;
        }

        global $wpdb;
        $table_name = 'wpcd_formatted_documents';

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        $document = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND user_id = %d",
            $document_id,
            $user_id
        ));

        if (!$document) {
            wp_send_json_error(['message' => 'Document not found']);
            return;
        }

        wp_send_json_success(array(
            'id' => $document->id,
            'title' => stripslashes($document->title),
            'content' => stripslashes($document->content),
            'theme_type' => $document->theme_type,
            'metadata' => stripslashes($document->document_metadata),
            'status' => $document->status
        ));
    }

    /**
     * Delete formatted document
     */
    public function rf_delete_document() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        $document_id = isset($_POST['document_id']) ? intval($_POST['document_id']) : 0;

        if (!$document_id) {
            wp_send_json_error(['message' => 'Document ID required']);
            return;
        }

        global $wpdb;
        $table_name = 'wpcd_formatted_documents';

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        $result = $wpdb->delete(
            $table_name,
            array('id' => $document_id, 'user_id' => $user_id),
            array('%d', '%d')
        );

        if ($result !== false) {
            wp_send_json_success(['message' => 'Document deleted successfully']);
        } else {
            wp_send_json_error(['message' => 'Failed to delete document']);
        }
    }

    /**
     * Export formatted document as PDF
     */
    public function rf_export_pdf() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            $this->export_error('Authentication required');
            return;
        }

        $document_id = isset($_POST['document_id']) ? intval($_POST['document_id']) : 0;
        $export_metadata = isset($_POST['export_metadata']) ? json_decode(stripslashes($_POST['export_metadata']), true) : array();

        if (!$document_id) {
            $this->export_error('Document ID required');
            return;
        }

        // Load document
        global $wpdb;
        $table_name = 'wpcd_formatted_documents';

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        $document = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND user_id = %d",
            $document_id,
            $user_id
        ));

        if (!$document) {
            $this->export_error('Document not found');
            return;
        }

        // Check if mPDF is available
        if (!$this->check_mpdf()) {
            $this->export_error('PDF generation not available');
            return;
        }

        try {
            // Include themes class if not already loaded
            if (!class_exists('Report_Formatter_Themes')) {
                require_once plugin_dir_path(__FILE__) . 'includes/class-report-formatter-themes.php';
            }

            // Create mPDF instance
            $mpdf = new \Mpdf\Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
                'margin_left' => 15,
                'margin_right' => 15,
                'margin_top' => 16,
                'margin_bottom' => 16,
                'margin_header' => 9,
                'margin_footer' => 9
            ]);

            // Apply theme-based styling and content
            $themes_manager = Report_Formatter_Themes::get_instance();

            // Prepare template options
            $template_options = array(
                'company_name' => isset($export_metadata['company_name']) ? $export_metadata['company_name'] : 'Nome Azienda',
                'document_title' => $document->title,
                'author' => isset($export_metadata['author']) ? $export_metadata['author'] : 'Autore',
                'date' => date('d/m/Y'),
                'creation_date' => date('d/m/Y H:i'),
                'document_id' => $document->id,
                'user_id' => $user_id,
                'exclude_footer' => false,
                'exclude_header' => false
            );

            // Use enhanced CSS that preserves editor formatting
            $enhanced_css = $this->get_enhanced_pdf_css();
            $mpdf->WriteHTML($enhanced_css, \Mpdf\HTMLParserMode::HEADER_CSS);

            // Use combined content (main + analysis) with exact editor formatting
            $combined_content = $this->get_combined_content_for_export($document, $export_metadata);
            $content_for_pdf = $this->prepare_content_for_pdf_exact_match($combined_content);
            $mpdf->WriteHTML($content_for_pdf, \Mpdf\HTMLParserMode::HTML_BODY);

            // Generate filename
            $filename = sanitize_file_name($document->title) . '_' . date('Y-m-d') . '.pdf';

            // Output PDF
            $mpdf->Output($filename, 'D');
            exit; // Important: exit after download

        } catch (Exception $e) {
            $this->export_error('PDF generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Export formatted document as Word (Enhanced with Balance Analyzer method)
     */
    public function rf_export_word() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            $this->export_error('Authentication required');
            return;
        }

        $document_id = isset($_POST['document_id']) ? intval($_POST['document_id']) : 0;
        $export_metadata = isset($_POST['export_metadata']) ? json_decode(stripslashes($_POST['export_metadata']), true) : array();

        if (!$document_id) {
            $this->export_error('Document ID required');
            return;
        }

        // Load document
        global $wpdb;
        $table_name = 'wpcd_formatted_documents';

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        $document = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND user_id = %d",
            $document_id,
            $user_id
        ));

        if (!$document) {
            $this->export_error('Document not found');
            return;
        }

        try {
            // Log export attempt
            dv_debug_log('Starting Word export for document ID: ' . $document_id);

            // Include themes class if not already loaded
            if (!class_exists('Report_Formatter_Themes')) {
                require_once plugin_dir_path(__FILE__) . 'includes/class-report-formatter-themes.php';
            }

            // Check if PHPWord is available
            if (!class_exists('\PhpOffice\PhpWord\PhpWord')) {
                dv_debug_log('PHPWord library not available');
                $this->export_error('PHPWord library not available');
                return;
            }

            // Log document content for debugging
            dv_debug_log('Document content length: ' . strlen($document->content));
            dv_debug_log('Document theme: ' . $document->theme_type);

            // Create PHPWord instance
            $phpWord = new \PhpOffice\PhpWord\PhpWord();

            // Add section
            $section = $phpWord->addSection();

            // Apply theme to content
            $themes_manager = Report_Formatter_Themes::get_instance();

            // Prepare template options
            $template_options = array(
                'company_name' => isset($export_metadata['company_name']) ? $export_metadata['company_name'] : 'Nome Azienda',
                'document_title' => $document->title,
                'author' => isset($export_metadata['author']) ? $export_metadata['author'] : 'Autore',
                'date' => date('d/m/Y'),
                'creation_date' => date('d/m/Y H:i'),
                'document_id' => $document->id,
                'user_id' => $user_id,
                'exclude_footer' => true, // Footer non supportato in Word export
                'exclude_header' => true  // Header non supportato in Word export
            );

            // Get themed content (simplified for Word)
            $themed_content = $themes_manager->apply_theme($document->theme_type, $document->content, $template_options);

            // Add title with company info
            $section->addTitle($document->title, 1);

            if (isset($export_metadata['company_name']) && !empty($export_metadata['company_name'])) {
                $section->addText($export_metadata['company_name'], array('bold' => true, 'size' => 14));
            }

            if (isset($export_metadata['author']) && !empty($export_metadata['author'])) {
                $section->addText('Autore: ' . $export_metadata['author'], array('size' => 12));
            }

            $section->addText('Data: ' . date('d/m/Y'), array('size' => 12));
            $section->addTextBreak(2);

            // Use ONLY final document content for Word export (exclude analysis content)
            $content_to_export = !empty($export_metadata['main_content']) ? $export_metadata['main_content'] : $document->content;

            // Prepare data for enhanced Word export
            $analysis_data = array(
                'title' => $document->title,
                'content' => $content_to_export,
                'schema' => $document->theme_type,
                'document' => $document->title,
                'timestamp' => current_time('mysql'),
                'company_name' => isset($export_metadata['company_name']) ? $export_metadata['company_name'] : '',
                'author' => isset($export_metadata['author']) ? $export_metadata['author'] : ''
            );

            // Use the enhanced Word export method
            $this->export_word_document_enhanced($analysis_data);

        } catch (Exception $e) {
            dv_debug_log('Word export error: ' . $e->getMessage());
            $this->export_error('Error generating Word document: ' . $e->getMessage());
        }
    }

    /**
     * Enhanced Word export using Balance Analyzer method
     */
    private function export_word_document_enhanced($analysis_data) {
        try {
            // Set headers for Word document download
            $filename = sanitize_file_name($analysis_data['title']) . '_' . date('Y-m-d_H-i-s') . '.doc';

            header('Content-Type: application/msword');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Cache-Control: max-age=0');
            header('Pragma: public');
            header('Expires: 0');

            // Create HTML content that Word can open (using Balance Analyzer method)
            $html_content = $this->generateWordHTMLContentForReportFormatter($analysis_data);

            // Output the HTML content
            echo $html_content;
            exit;

        } catch (Exception $e) {
            dv_debug_log('Enhanced Word export error: ' . $e->getMessage());
            wp_die(__('Error generating Word document: ', 'document-viewer-plugin') . $e->getMessage());
        }
    }

    /**
     * Generate enhanced HTML content for Report Formatter Word export
     */
    private function generateWordHTMLContentForReportFormatter($analysis_data) {
        // Preserve HTML formatting instead of stripping it
        $content = $analysis_data['content'];

        // Clean and enhance content for Word compatibility while preserving exact editor formatting
        $content = $this->enhanceContentForWordExactMatch($content);

        // Create HTML document that Word can open with enhanced styling
        $html = '<!DOCTYPE html>';
        $html .= '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">';
        $html .= '<head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<meta name="ProgId" content="Word.Document">';
        $html .= '<meta name="Generator" content="Microsoft Word">';
        $html .= '<meta name="Originator" content="Microsoft Word">';
        $html .= '<title>' . htmlspecialchars($analysis_data['title']) . '</title>';
        $html .= '<style>';
        $html .= $this->getWordCompatibleCSSForReportFormatter();
        $html .= '</style>';
        $html .= '</head>';
        $html .= '<body>';

        // Title
        $html .= '<h1>' . htmlspecialchars($analysis_data['title']) . '</h1>';

        // Meta information
        $html .= '<div class="meta">';
        if (!empty($analysis_data['company_name'])) {
            $html .= '<p><strong>Azienda:</strong> ' . htmlspecialchars($analysis_data['company_name']) . '</p>';
        }
        if (!empty($analysis_data['author'])) {
            $html .= '<p><strong>Autore:</strong> ' . htmlspecialchars($analysis_data['author']) . '</p>';
        }
        $html .= '<p><strong>Tema:</strong> ' . htmlspecialchars($analysis_data['schema']) . '</p>';
        $html .= '<p><strong>Data Creazione:</strong> ' . date('d/m/Y H:i', strtotime($analysis_data['timestamp'])) . '</p>';
        $html .= '</div>';

        // Content with preserved formatting
        $html .= '<div class="content">' . $content . '</div>';

        $html .= '</body>';
        $html .= '</html>';

        return $html;
    }

    /**
     * Enhance content for Word export while preserving all formatting
     */
    private function enhanceContentForWordWithFormatting($content) {
        // Remove UI elements but preserve all text formatting
        $content = preg_replace('/<div[^>]*class="[^"]*logo-resize-handles[^"]*"[^>]*>.*?<\/div>/s', '', $content);
        $content = preg_replace('/<div[^>]*class="[^"]*resize-handle[^"]*"[^>]*><\/div>/g', '', $content);

        // Ensure proper encoding
        $content = mb_convert_encoding($content, 'UTF-8', 'auto');

        // Fix logo paths for Word
        $content = preg_replace_callback(
            '/<img[^>]*src="([^"]+)"[^>]*class="[^"]*rf-logo[^"]*"[^>]*>/i',
            function($matches) {
                $logo_url = $matches[1];

                // Convert relative URLs to absolute
                if (!preg_match('/^https?:\/\//', $logo_url)) {
                    $logo_url = home_url($logo_url);
                }

                return '<img src="' . esc_url($logo_url) . '" alt="Logo" style="max-width: 200px; max-height: 100px; object-fit: contain; background: transparent;">';
            },
            $content
        );

        return $content;
    }

    /**
     * Enhance content for Word export with exact editor match
     */
    private function enhanceContentForWordExactMatch($content) {
        // Remove ONLY UI elements, preserve ALL text formatting exactly as in editor
        $content = preg_replace('/<div[^>]*class="[^"]*logo-resize-handles[^"]*"[^>]*>.*?<\/div>/s', '', $content);
        $content = preg_replace('/<div[^>]*class="[^"]*resize-handle[^"]*"[^>]*><\/div>/g', '', $content);
        $content = preg_replace('/<div[^>]*class="[^"]*toolbar[^"]*"[^>]*>.*?<\/div>/s', '', $content);

        // Ensure proper encoding while preserving all formatting
        $content = mb_convert_encoding($content, 'UTF-8', 'auto');

        // Fix logo paths for Word while preserving exact styling and dimensions
        $content = preg_replace_callback(
            '/<img[^>]*src="([^"]+)"[^>]*class="[^"]*rf-logo[^"]*"[^>]*>/i',
            function($matches) {
                $full_match = $matches[0];
                $logo_url = $matches[1];

                // Convert relative URLs to absolute
                if (!preg_match('/^https?:\/\//', $logo_url)) {
                    $logo_url = home_url($logo_url);
                }

                // Replace only the src attribute, preserve ALL other attributes and styling
                return preg_replace('/src="[^"]*"/', 'src="' . esc_url($logo_url) . '"', $full_match);
            },
            $content
        );

        return $content;
    }

    /**
     * Get enhanced Word-compatible CSS that exactly matches editor formatting
     */
    private function getWordCompatibleCSSForReportFormatter() {
        return '
            body {
                font-family: inherit;
                font-size: inherit;
                line-height: inherit;
                margin: 1in;
                color: inherit;
                background: transparent;
            }

            /* Preserve ALL formatting exactly as in editor */
            * {
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                font-style: inherit !important;
                text-decoration: inherit !important;
                text-align: inherit !important;
                background-color: inherit !important;
                margin: inherit !important;
                padding: inherit !important;
                border: inherit !important;
                line-height: inherit !important;
                width: inherit !important;
                height: inherit !important;
            }

            /* Preserve headings exactly */
            h1, h2, h3, h4, h5, h6 {
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                margin: inherit !important;
                text-align: inherit !important;
            }

            .meta {
                font-size: 10pt;
                color: #666;
                margin-bottom: 20pt;
                border-bottom: 1pt solid #ccc;
                padding-bottom: 10pt;
            }

            /* Preserve paragraphs exactly */
            p {
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                font-style: inherit !important;
                text-decoration: inherit !important;
                text-align: inherit !important;
                margin: inherit !important;
                padding: inherit !important;
                line-height: inherit !important;
            }

            /* Preserve text formatting exactly */
            strong, b {
                font-weight: inherit !important;
            }

            em, i {
                font-style: inherit !important;
            }

            u {
                text-decoration: inherit !important;
            }

            /* Preserve table formatting exactly */
            table {
                border-collapse: inherit !important;
                width: inherit !important;
                margin: inherit !important;
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                border: inherit !important;
            }

            th, td {
                border: inherit !important;
                padding: inherit !important;
                text-align: inherit !important;
                vertical-align: inherit !important;
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                background-color: inherit !important;
            }

            /* Preserve list formatting exactly */
            ul, ol {
                margin: inherit !important;
                padding: inherit !important;
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
            }

            li {
                margin: inherit !important;
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
            }

            /* Logo formatting - preserve exact editor appearance */
            .rf-logo {
                width: inherit !important;
                height: inherit !important;
                max-width: inherit !important;
                max-height: inherit !important;
                object-fit: inherit !important;
                background: transparent !important;
                border: none !important;
            }

            .rf-logo-container {
                text-align: inherit !important;
                margin: inherit !important;
                padding: inherit !important;
                background: transparent !important;
                border: none !important;
            }

            .content {
                margin-top: 20pt;
            }

            /* Preserve ALL inline styles exactly */
            [style] {
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                font-style: inherit !important;
                text-decoration: inherit !important;
                text-align: inherit !important;
                background-color: inherit !important;
                margin: inherit !important;
                padding: inherit !important;
                border: inherit !important;
                width: inherit !important;
                height: inherit !important;
            }

            /* Hide only UI elements */
            .logo-resize-handles,
            .resize-handle {
                display: none !important;
            }
        ';
    }

    /**
     * Delete analysis from database
     */
    public function rf_delete_analysis() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        $analysis_id = isset($_POST['analysis_id']) ? intval($_POST['analysis_id']) : 0;

        if (!$analysis_id) {
            wp_send_json_error(['message' => 'Analysis ID required']);
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'document_analysis';

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        // Debug logging
        dv_debug_log("Attempting to delete analysis ID: $analysis_id for user ID: $user_id");
        dv_debug_log("Using table: $table_name");

        // Verify the analysis belongs to the current user
        $analysis = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND user_id = %d",
            $analysis_id,
            $user_id
        ));

        // Debug the query result
        if ($wpdb->last_error) {
            dv_debug_log("Database error in analysis deletion: " . $wpdb->last_error);
        }

        if (!$analysis) {
            dv_debug_log("Analysis not found or access denied. Analysis ID: $analysis_id, User ID: $user_id");
            wp_send_json_error(['message' => 'Analysis not found or access denied']);
            return;
        }

        dv_debug_log("Analysis found, proceeding with deletion: " . json_encode($analysis));

        // Delete the analysis
        $result = $wpdb->delete(
            $table_name,
            array(
                'id' => $analysis_id,
                'user_id' => $user_id
            ),
            array('%d', '%d')
        );

        // Debug the deletion result
        dv_debug_log("Delete operation result: " . ($result !== false ? 'Success' : 'Failed'));
        if ($wpdb->last_error) {
            dv_debug_log("Delete operation error: " . $wpdb->last_error);
        }

        if ($result !== false) {
            dv_debug_log("Analysis deleted successfully. Rows affected: $result");
            wp_send_json_success(['message' => 'Analysis deleted successfully', 'rows_affected' => $result]);
        } else {
            dv_debug_log("Failed to delete analysis. Last error: " . $wpdb->last_error);
            wp_send_json_error(['message' => 'Failed to delete analysis: ' . $wpdb->last_error]);
        }
    }

    /**
     * Fallback Word export using simple text
     */
    private function export_word_fallback($document, $export_metadata = array()) {
        try {
            dv_debug_log('Attempting fallback Word export');

            // Create simple PHPWord document
            $phpWord = new \PhpOffice\PhpWord\PhpWord();
            $section = $phpWord->addSection();

            // Add basic document info
            $section->addTitle($document->title, 1);

            if (isset($export_metadata['company_name']) && !empty($export_metadata['company_name'])) {
                $section->addText($export_metadata['company_name'], array('bold' => true, 'size' => 14));
            }

            if (isset($export_metadata['author']) && !empty($export_metadata['author'])) {
                $section->addText('Autore: ' . $export_metadata['author'], array('size' => 12));
            }

            $section->addText('Data: ' . date('d/m/Y'), array('size' => 12));
            $section->addTextBreak(2);

            // Add content as plain text
            $plain_content = strip_tags($document->content);
            $plain_content = html_entity_decode($plain_content, ENT_QUOTES | ENT_HTML5, 'UTF-8');

            // Split into paragraphs
            $paragraphs = explode("\n", $plain_content);
            foreach ($paragraphs as $paragraph) {
                $paragraph = trim($paragraph);
                if (!empty($paragraph)) {
                    $section->addText($paragraph);
                }
            }

            // Generate filename
            $filename = sanitize_file_name($document->title) . '_fallback_' . date('Y-m-d') . '.docx';

            // Save to temporary file
            $temp_file = tempnam(sys_get_temp_dir(), 'rf_export_fallback_');
            $writer = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
            $writer->save($temp_file);

            // Output file
            header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . filesize($temp_file));

            readfile($temp_file);
            unlink($temp_file);

            dv_debug_log('Fallback Word export completed successfully');
            exit;

        } catch (Exception $e) {
            dv_debug_log('Fallback Word export also failed: ' . $e->getMessage());
            $this->export_error('Word export failed: ' . $e->getMessage());
        }
    }

    /**
     * Get editing CSS for PDF export (instead of theme CSS)
     */
    private function get_editing_css() {
        return "
        body {
            font-family: 'Times New Roman', serif;
            font-size: 14pt;
            line-height: 1.6;
            color: #212529;
            margin: 0;
            padding: 20px;
        }
        h1 {
            font-size: 24pt;
            color: #007cba;
            margin: 20px 0 12px 0;
            font-weight: bold;
        }
        h2 {
            font-size: 20pt;
            color: #007cba;
            margin: 20px 0 12px 0;
            font-weight: bold;
        }
        h3 {
            font-size: 18pt;
            color: #495057;
            margin: 20px 0 12px 0;
            font-weight: bold;
        }
        h4 {
            font-size: 16pt;
            color: #495057;
            margin: 20px 0 12px 0;
            font-weight: bold;
        }
        p {
            margin: 0 0 12px 0;
        }
        ul, ol {
            margin: 12px 0;
            padding-left: 30px;
        }
        li {
            margin: 4px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        img {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
        }
        a {
            color: #007cba;
            text-decoration: underline;
        }
        hr {
            border: none;
            height: 2px;
            background: #dee2e6;
            margin: 20px 0;
        }
        blockquote {
            border-left: 4px solid #007cba;
            padding: 10px 15px;
            margin: 15px 0;
            background: #f8f9fa;
            font-style: italic;
        }
        ";
    }

    /**
     * Get combined content for export (main + analysis)
     */
    private function get_combined_content_for_export($document, $export_metadata) {
        $main_content = $document->content;
        $analysis_content = '';

        // Check if analysis content is provided in metadata
        if (isset($export_metadata['analysis_content']) && !empty($export_metadata['analysis_content'])) {
            $analysis_content = $export_metadata['analysis_content'];
        }

        // Combine content
        $combined_content = $main_content;
        if (!empty($analysis_content) && trim($analysis_content) !== '' && trim($analysis_content) !== '<p><br></p>') {
            $separator = (!empty($main_content) && trim($main_content) !== '' && trim($main_content) !== '<p><br></p>')
                ? '<hr><h2>📊 Contenuto Analisi</h2>'
                : '<h2>📊 Contenuto Analisi</h2>';
            $combined_content = $main_content . $separator . $analysis_content;
        }

        return $combined_content;
    }

    /**
     * Prepare content for PDF export (clean and format)
     */
    private function prepare_content_for_pdf($content) {
        // Clean content for PDF
        $content = wp_kses_post($content);

        // Ensure proper encoding
        $content = mb_convert_encoding($content, 'UTF-8', 'auto');

        // Wrap in proper HTML structure
        return '<div class="pdf-content">' . $content . '</div>';
    }

    /**
     * Prepare content for PDF export with proper logo embedding
     */
    private function prepare_content_for_pdf_with_logos($content) {
        // Clean content for PDF
        $content = wp_kses_post($content);

        // Ensure proper encoding
        $content = mb_convert_encoding($content, 'UTF-8', 'auto');

        // Fix logo embedding for PDF
        $content = $this->embed_logos_for_pdf($content);

        // Wrap in proper HTML structure
        return '<div class="pdf-content">' . $content . '</div>';
    }

    /**
     * Embed logos properly in PDF content
     */
    private function embed_logos_for_pdf($content) {
        // Find all logo containers and img tags
        $content = preg_replace_callback(
            '/<div[^>]*class="[^"]*rf-logo-container[^"]*"[^>]*>.*?<img[^>]*src="([^"]+)"[^>]*>.*?<\/div>/s',
            function($matches) {
                $logo_url = $matches[1];

                // Convert relative URLs to absolute
                if (!preg_match('/^https?:\/\//', $logo_url)) {
                    $logo_url = home_url($logo_url);
                }

                // Create clean img tag for PDF
                return '<div style="text-align: center; margin: 20px 0;">
                    <img src="' . esc_url($logo_url) . '" alt="Logo" style="max-width: 200px; max-height: 100px; object-fit: contain;">
                </div>';
            },
            $content
        );

        // Also handle standalone logo images
        $content = preg_replace_callback(
            '/<img[^>]*class="[^"]*rf-logo[^"]*"[^>]*src="([^"]+)"[^>]*>/i',
            function($matches) {
                $logo_url = $matches[1];

                // Convert relative URLs to absolute
                if (!preg_match('/^https?:\/\//', $logo_url)) {
                    $logo_url = home_url($logo_url);
                }

                return '<img src="' . esc_url($logo_url) . '" alt="Logo" style="max-width: 200px; max-height: 100px; object-fit: contain;">';
            },
            $content
        );

        return $content;
    }

    /**
     * Prepare content for PDF export with exact editor match
     */
    private function prepare_content_for_pdf_exact_match($content) {
        // Preserve the exact content as displayed in editor
        // Only remove UI elements that shouldn't appear in PDF

        // Remove resize handles and UI elements
        $content = preg_replace('/<div[^>]*class="[^"]*logo-resize-handles[^"]*"[^>]*>.*?<\/div>/s', '', $content);
        $content = preg_replace('/<div[^>]*class="[^"]*resize-handle[^"]*"[^>]*><\/div>/g', '', $content);
        $content = preg_replace('/<div[^>]*class="[^"]*toolbar[^"]*"[^>]*>.*?<\/div>/s', '', $content);

        // Fix logo paths for PDF while preserving exact styling
        $content = preg_replace_callback(
            '/<img[^>]*src="([^"]+)"[^>]*class="[^"]*rf-logo[^"]*"[^>]*>/i',
            function($matches) {
                $full_match = $matches[0];
                $logo_url = $matches[1];

                // Convert relative URLs to absolute
                if (!preg_match('/^https?:\/\//', $logo_url)) {
                    $logo_url = home_url($logo_url);
                }

                // Replace only the src attribute, preserve all other attributes and styling
                return preg_replace('/src="[^"]*"/', 'src="' . esc_url($logo_url) . '"', $full_match);
            },
            $content
        );

        // Ensure proper encoding while preserving all formatting
        $content = mb_convert_encoding($content, 'UTF-8', 'auto');

        // Wrap in proper HTML structure
        return '<div class="pdf-content-exact">' . $content . '</div>';
    }

    /**
     * Get enhanced CSS for PDF export that exactly matches editor formatting
     */
    private function get_enhanced_pdf_css() {
        return '
            body {
                font-family: inherit;
                font-size: inherit;
                line-height: inherit;
                color: inherit;
                margin: 0;
                padding: 0;
                background: transparent;
            }

            /* Preserve ALL text formatting exactly as in editor */
            * {
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                font-style: inherit !important;
                text-decoration: inherit !important;
                text-align: inherit !important;
                background-color: inherit !important;
                margin: inherit !important;
                padding: inherit !important;
                border: inherit !important;
                line-height: inherit !important;
            }

            /* Preserve headings exactly */
            h1, h2, h3, h4, h5, h6 {
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                margin: inherit !important;
                text-align: inherit !important;
            }

            /* Preserve paragraphs exactly */
            p {
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                font-style: inherit !important;
                text-decoration: inherit !important;
                text-align: inherit !important;
                margin: inherit !important;
                padding: inherit !important;
                line-height: inherit !important;
            }

            /* Preserve table formatting exactly */
            table {
                border-collapse: inherit !important;
                width: inherit !important;
                margin: inherit !important;
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                border: inherit !important;
            }

            th, td {
                border: inherit !important;
                padding: inherit !important;
                text-align: inherit !important;
                vertical-align: inherit !important;
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                background-color: inherit !important;
            }

            /* Preserve list formatting exactly */
            ul, ol {
                margin: inherit !important;
                padding: inherit !important;
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
            }

            li {
                margin: inherit !important;
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
            }

            /* Preserve text formatting exactly */
            strong, b {
                font-weight: inherit !important;
            }

            em, i {
                font-style: inherit !important;
            }

            u {
                text-decoration: inherit !important;
            }

            /* Logo formatting - preserve exact editor appearance */
            .rf-logo {
                width: inherit !important;
                height: inherit !important;
                max-width: inherit !important;
                max-height: inherit !important;
                object-fit: inherit !important;
                background: transparent !important;
                border: none !important;
            }

            .rf-logo-container {
                text-align: inherit !important;
                margin: inherit !important;
                padding: inherit !important;
                background: transparent !important;
                border: none !important;
            }

            /* Hide only UI elements, preserve all content formatting */
            .logo-resize-handles,
            .resize-handle {
                display: none !important;
            }

            /* Ensure all inline styles are preserved */
            [style] {
                color: inherit !important;
                font-size: inherit !important;
                font-family: inherit !important;
                font-weight: inherit !important;
                font-style: inherit !important;
                text-decoration: inherit !important;
                text-align: inherit !important;
                background-color: inherit !important;
                margin: inherit !important;
                padding: inherit !important;
                border: inherit !important;
                width: inherit !important;
                height: inherit !important;
            }
        ';
    }

    /**
     * Get theme-specific CSS for PDF export (legacy method)
     */
    private function get_theme_css($theme) {
        $css = "
        body { font-family: Arial, sans-serif; font-size: 12pt; line-height: 1.6; }
        h1 { font-size: 18pt; margin-bottom: 20px; }
        h2 { font-size: 16pt; margin-bottom: 15px; }
        h3 { font-size: 14pt; margin-bottom: 10px; }
        p { margin-bottom: 10px; }
        ";

        switch ($theme) {
            case 'blue':
                $css .= "
                h1, h2, h3 { color: #007bff; }
                body { background-color: #f8f9fa; }
                ";
                break;
            case 'orange':
                $css .= "
                h1, h2, h3 { color: #fd7e14; }
                body { background-color: #fff3e0; }
                ";
                break;
            default: // gray
                $css .= "
                h1, h2, h3 { color: #6c757d; }
                body { background-color: #f8f9fa; }
                ";
                break;
        }

        return $css;
    }

    /**
     * Handle export errors by showing user-friendly error page
     */
    private function export_error($message) {
        // Clear any output buffers
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Set content type to HTML
        header('Content-Type: text/html; charset=utf-8');

        // Output error page
        echo '<!DOCTYPE html>
        <html>
        <head>
            <title>Errore Esportazione</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .error-container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto; }
                .error-icon { font-size: 48px; color: #dc3545; text-align: center; margin-bottom: 20px; }
                .error-title { color: #dc3545; font-size: 24px; margin-bottom: 15px; text-align: center; }
                .error-message { color: #666; font-size: 16px; line-height: 1.5; margin-bottom: 25px; text-align: center; }
                .error-actions { text-align: center; }
                .btn { background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 0 10px; }
                .btn:hover { background: #005a87; }
                .btn-secondary { background: #6c757d; }
                .btn-secondary:hover { background: #5a6268; }
            </style>
        </head>
        <body>
            <div class="error-container">
                <div class="error-icon">❌</div>
                <h1 class="error-title">Errore durante l\'esportazione</h1>
                <p class="error-message">' . esc_html($message) . '</p>
                <div class="error-actions">
                    <a href="javascript:history.back()" class="btn btn-secondary">← Torna Indietro</a>
                    <a href="javascript:window.close()" class="btn">Chiudi Finestra</a>
                </div>
            </div>
        </body>
        </html>';

        exit;
    }

    /**
     * Clean HTML content for Word export
     */
    private function clean_html_for_word($html) {
        // Log original content for debugging
        dv_debug_log('Original HTML for Word export: ' . substr($html, 0, 500));

        // First, decode any HTML entities
        $html = html_entity_decode($html, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Preserve important formatting attributes
        $html = preg_replace('/<([a-zA-Z0-9]+)[^>]*style="([^"]*)"[^>]*>/', '<$1 style="$2">', $html);

        // Convert complex structures to simple ones but preserve formatting
        $replacements = [
            // Remove problematic tags completely
            '/<script[^>]*>.*?<\/script>/is' => '',
            '/<style[^>]*>.*?<\/style>/is' => '',
            '/<link[^>]*>/i' => '',
            '/<meta[^>]*>/i' => '',

            // Convert divs to paragraphs but preserve style
            '/<div([^>]*style="[^"]*"[^>]*)>/' => '<p$1>',
            '/<div[^>]*>/' => '<p>',
            '/<\/div>/' => '</p>',

            // Keep spans with style, remove others
            '/<span[^>]*style="[^"]*"[^>]*>/' => '$0', // Keep styled spans
            '/<span[^>]*>/' => '', // Remove unstyled spans
            '/<\/span>/' => '',

            // Fix self-closing tags
            '/<br[^>]*>/' => '<br/>',
            '/<hr[^>]*>/' => '<hr/>',

            // Remove images but keep alt text if present
            '/<img[^>]*alt="([^"]*)"[^>]*>/' => '[$1]',
            '/<img[^>]*>/' => '',

            // Clean up multiple spaces but preserve single spaces
            '/\s{2,}/' => ' ',
        ];

        foreach ($replacements as $pattern => $replacement) {
            $html = preg_replace($pattern, $replacement, $html);
        }

        // Ensure all tags are properly closed
        $html = $this->fix_unclosed_tags($html);

        // Remove empty paragraphs and clean up
        $html = preg_replace('/<p>\s*<\/p>/', '', $html);
        $html = preg_replace('/<p>\s*<br\/>\s*<\/p>/', '<p><br/></p>', $html);

        // Final cleanup
        $html = trim($html);

        // If still empty or problematic, provide fallback
        if (empty($html) || strlen($html) < 10) {
            $html = '<p>Contenuto del documento</p>';
        }

        // Log cleaned content for debugging
        dv_debug_log('Cleaned HTML for Word export: ' . substr($html, 0, 500));

        return $html;
    }

    /**
     * Minimal cleaning for Word export - preserves most formatting
     */
    private function minimal_clean_for_word($html) {
        // Log original content for debugging
        dv_debug_log('Minimal clean input: ' . substr($html, 0, 300));

        // Use DOMDocument for proper HTML parsing and cleaning
        $dom = new DOMDocument('1.0', 'UTF-8');
        $dom->preserveWhiteSpace = false;
        $dom->formatOutput = false;

        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);

        // Wrap content in a container to ensure valid XML
        $wrapped_html = '<?xml version="1.0" encoding="UTF-8"?><root>' . $html . '</root>';

        if ($dom->loadHTML($wrapped_html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD)) {
            // Remove problematic elements
            $this->remove_elements_by_tag($dom, ['script', 'style', 'meta', 'link']);

            // Fix self-closing tags
            $this->fix_self_closing_tags($dom);

            // Remove problematic attributes but keep style
            $this->clean_attributes($dom);

            // Get cleaned HTML
            $body = $dom->getElementsByTagName('root')->item(0);
            $cleaned_html = '';

            if ($body) {
                foreach ($body->childNodes as $child) {
                    $cleaned_html .= $dom->saveHTML($child);
                }
            }

            // Final cleanup
            $cleaned_html = html_entity_decode($cleaned_html, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            $cleaned_html = trim($cleaned_html);

            dv_debug_log('Minimal clean output: ' . substr($cleaned_html, 0, 300));

            return $cleaned_html;
        }

        // Fallback to regex cleaning if DOM parsing fails
        return $this->regex_clean_for_word($html);
    }

    /**
     * Remove elements by tag name from DOM
     */
    private function remove_elements_by_tag($dom, $tags) {
        foreach ($tags as $tag) {
            $elements = $dom->getElementsByTagName($tag);
            $to_remove = [];

            foreach ($elements as $element) {
                $to_remove[] = $element;
            }

            foreach ($to_remove as $element) {
                if ($element->parentNode) {
                    $element->parentNode->removeChild($element);
                }
            }
        }
    }

    /**
     * Fix self-closing tags for XML compatibility
     */
    private function fix_self_closing_tags($dom) {
        $self_closing = ['br', 'hr', 'img', 'input'];

        foreach ($self_closing as $tag) {
            $elements = $dom->getElementsByTagName($tag);
            foreach ($elements as $element) {
                // Ensure proper self-closing format
                if (!$element->hasChildNodes()) {
                    $element->appendChild($dom->createTextNode(''));
                }
            }
        }
    }

    /**
     * Clean attributes but preserve important ones
     */
    private function clean_attributes($dom) {
        $xpath = new DOMXPath($dom);
        $all_elements = $xpath->query('//*');

        $allowed_attributes = ['style', 'href', 'src', 'alt', 'title'];

        foreach ($all_elements as $element) {
            $attributes_to_remove = [];

            if ($element->attributes) {
                foreach ($element->attributes as $attr) {
                    if (!in_array($attr->name, $allowed_attributes)) {
                        $attributes_to_remove[] = $attr->name;
                    }
                }

                foreach ($attributes_to_remove as $attr_name) {
                    $element->removeAttribute($attr_name);
                }
            }
        }
    }

    /**
     * Ultimate HTML cleaner for PHPWord - removes all problematic elements
     */
    private function ultimate_clean_for_word($html) {
        dv_debug_log('Ultimate clean input: ' . substr($html, 0, 200));

        // Step 1: Remove all problematic tags completely
        $html = preg_replace('/<(script|style|meta|link|head|title)[^>]*>.*?<\/\1>/is', '', $html);
        $html = preg_replace('/<(script|style|meta|link|head|title)[^>]*>/i', '', $html);

        // Step 2: Convert complex structures to simple ones
        $html = preg_replace('/<div([^>]*)>/i', '<p$1>', $html);
        $html = preg_replace('/<\/div>/i', '</p>', $html);

        // Step 3: Remove all attributes except basic ones
        $html = preg_replace('/<([a-zA-Z0-9]+)[^>]*style="([^"]*)"[^>]*>/i', '<$1 style="$2">', $html);
        $html = preg_replace('/<([a-zA-Z0-9]+)[^>]*>/i', '<$1>', $html);

        // Step 4: Fix self-closing tags
        $html = preg_replace('/<(br|hr|img|input)([^>]*)>/i', '<$1$2/>', $html);

        // Step 5: Remove empty paragraphs and fix nesting
        $html = preg_replace('/<p[^>]*>\s*<\/p>/i', '', $html);
        $html = preg_replace('/<p[^>]*>\s*<br[^>]*>\s*<\/p>/i', '', $html);

        // Step 6: Ensure proper encoding
        $html = html_entity_decode($html, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Step 7: Final cleanup
        $html = preg_replace('/\s+/', ' ', $html);
        $html = preg_replace('/>\s+</', '><', $html);
        $html = trim($html);

        dv_debug_log('Ultimate clean output: ' . substr($html, 0, 200));

        return $html;
    }

    /**
     * Fallback regex cleaning method
     */
    private function regex_clean_for_word($html) {
        $minimal_replacements = [
            // Remove script and style tags
            '/<script[^>]*>.*?<\/script>/is' => '',
            '/<style[^>]*>.*?<\/style>/is' => '',
            '/<meta[^>]*>/i' => '',
            '/<link[^>]*>/i' => '',

            // Fix self-closing tags for XML compatibility
            '/<br\s*\/?>/i' => '<br/>',
            '/<hr\s*\/?>/i' => '<hr/>',
            '/<img([^>]*)\s*\/?>/i' => '<img$1/>',

            // Remove problematic attributes but keep style
            '/\s(class|id|data-[^=]*)="[^"]*"/i' => '',

            // Clean up excessive whitespace
            '/\s{2,}/' => ' ',
            '/>\s+</' => '><',
        ];

        foreach ($minimal_replacements as $pattern => $replacement) {
            $html = preg_replace($pattern, $replacement, $html);
        }

        // Ensure proper encoding
        $html = html_entity_decode($html, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        return trim($html);
    }

    /**
     * Fix unclosed HTML tags
     */
    private function fix_unclosed_tags($html) {
        // List of tags that need to be closed
        $tags_to_close = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'strong', 'em', 'b', 'i', 'u', 'ul', 'ol', 'li'];

        foreach ($tags_to_close as $tag) {
            // Count opening and closing tags
            $open_count = preg_match_all("/<$tag>/i", $html);
            $close_count = preg_match_all("/<\/$tag>/i", $html);

            // Add missing closing tags
            if ($open_count > $close_count) {
                $missing = $open_count - $close_count;
                for ($i = 0; $i < $missing; $i++) {
                    $html .= "</$tag>";
                }
            }
        }

        return $html;
    }

    /**
     * Validate HTML for Word export
     */
    private function validate_html_for_word($html) {
        // Check if HTML is not empty
        if (empty($html) || strlen(trim($html)) < 3) {
            return false;
        }

        // Try to load HTML with DOMDocument to check for validity
        $dom = new DOMDocument();
        $dom->loadHTML('<?xml encoding="UTF-8">' . $html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);

        // Check for basic structure
        if ($dom->documentElement === null) {
            return false;
        }

        // Check for problematic patterns that cause PHPWord issues
        $problematic_patterns = [
            '/<[^>]*[^\/]>.*?<\/[^>]*>.*?<[^>]*[^\/]>/', // Nested unclosed tags
            '/<br[^>]*>[^<]*<\/p>/', // BR inside P tags
            '/<div[^>]*>[^<]*<\/p>/', // Mismatched div/p tags
        ];

        foreach ($problematic_patterns as $pattern) {
            if (preg_match($pattern, $html)) {
                dv_debug_log('HTML validation failed: problematic pattern found');
                return false;
            }
        }

        return true;
    }

    /**
     * Get analysis content for Report Formatter Widget
     */
    public function rf_get_analysis_content() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        $analysis_id = isset($_POST['analysis_id']) ? intval($_POST['analysis_id']) : 0;

        if (!$analysis_id) {
            wp_send_json_error(['message' => 'Analysis ID required']);
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'document_analysis';

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        $analysis = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND user_id = %d",
            $analysis_id,
            $user_id
        ));

        if (!$analysis) {
            wp_send_json_error(['message' => 'Analysis not found']);
            return;
        }

        wp_send_json_success(array(
            'id' => $analysis->id,
            'title' => $analysis->title,
            'content' => $analysis->analysis_results,
            'query' => $analysis->query,
            'created_at' => $analysis->created_at
        ));
    }

    /**
     * Get theme preview for Report Formatter Widget
     */
    public function rf_get_theme_preview() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        $theme_id = isset($_POST['theme_id']) ? sanitize_text_field($_POST['theme_id']) : '';

        if (empty($theme_id)) {
            wp_send_json_error(['message' => 'Theme ID required']);
            return;
        }

        // Include themes class if not already loaded
        if (!class_exists('Report_Formatter_Themes')) {
            require_once plugin_dir_path(__FILE__) . 'includes/class-report-formatter-themes.php';
        }

        $themes_manager = Report_Formatter_Themes::get_instance();
        $preview = $themes_manager->generate_theme_preview($theme_id);

        if ($preview) {
            wp_send_json_success(array(
                'preview' => $preview,
                'theme_id' => $theme_id
            ));
        } else {
            wp_send_json_error(['message' => 'Theme not found']);
        }
    }

    /**
     * Create formatted documents table (emergency endpoint)
     */
    public function rf_create_table() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica che sia un admin
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        global $wpdb;
        $table_name = 'wpcd_formatted_documents';

        // Verifica se la tabella esiste già
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if ($table_exists) {
            wp_send_json_success([
                'message' => 'Table already exists',
                'table_name' => $table_name,
                'action' => 'none'
            ]);
            return;
        }

        try {
            // Crea la tabella usando il metodo dedicato
            $this->force_create_formatted_documents_table();

            // Verifica se la creazione è riuscita
            $table_created = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

            if ($table_created) {
                wp_send_json_success([
                    'message' => 'Table created successfully',
                    'table_name' => $table_name,
                    'action' => 'created'
                ]);
            } else {
                wp_send_json_error([
                    'message' => 'Table creation failed',
                    'mysql_error' => $wpdb->last_error
                ]);
            }

        } catch (Exception $e) {
            wp_send_json_error([
                'message' => 'Exception during table creation',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Upload logo for Report Formatter Widget
     */
    public function rf_upload_logo() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        // Verifica che sia stato caricato un file
        if (!isset($_FILES['logo']) || $_FILES['logo']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(['message' => 'No file uploaded or upload error']);
            return;
        }

        $file = $_FILES['logo'];

        // Verifica tipo file
        $allowed_types = array('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp');
        if (!in_array($file['type'], $allowed_types)) {
            wp_send_json_error(['message' => 'Invalid file type. Only images are allowed.']);
            return;
        }

        // Verifica dimensione file (max 2MB)
        if ($file['size'] > 2 * 1024 * 1024) {
            wp_send_json_error(['message' => 'File too large. Maximum size is 2MB.']);
            return;
        }

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        // Crea directory upload se non esiste
        $upload_dir = wp_upload_dir();
        $logo_dir = $upload_dir['basedir'] . '/report-formatter-logos';
        if (!file_exists($logo_dir)) {
            wp_mkdir_p($logo_dir);
        }

        // Genera nome file unico
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'logo_' . $user_id . '_' . time() . '.' . $file_extension;
        $file_path = $logo_dir . '/' . $filename;

        // Sposta file caricato
        if (!move_uploaded_file($file['tmp_name'], $file_path)) {
            wp_send_json_error(['message' => 'Failed to save uploaded file']);
            return;
        }

        // Salva informazioni logo nel database
        global $wpdb;
        $table_name = 'wpcd_widget_settings';

        // Rimuovi logo precedente se esiste
        $old_logo = $wpdb->get_row($wpdb->prepare(
            "SELECT setting_value FROM $table_name WHERE user_id = %d AND widget_type = 'report_formatter' AND setting_key = 'logo'",
            $user_id
        ));

        if ($old_logo) {
            $old_logo_data = json_decode($old_logo->setting_value, true);
            if (isset($old_logo_data['file_path']) && file_exists($old_logo_data['file_path'])) {
                unlink($old_logo_data['file_path']);
            }
        }

        // Salva nuovo logo
        $logo_data = array(
            'filename' => $filename,
            'file_path' => $file_path,
            'file_url' => $upload_dir['baseurl'] . '/report-formatter-logos/' . $filename,
            'file_size' => $file['size'],
            'file_type' => $file['type'],
            'uploaded_at' => current_time('mysql')
        );

        $result = $wpdb->replace(
            $table_name,
            array(
                'user_id' => $user_id,
                'widget_type' => 'report_formatter',
                'setting_key' => 'logo',
                'setting_value' => json_encode($logo_data),
                'setting_metadata' => json_encode(array('original_name' => $file['name']))
            ),
            array('%d', '%s', '%s', '%s', '%s')
        );

        if ($result !== false) {
            wp_send_json_success(array(
                'message' => 'Logo uploaded successfully',
                'logo_data' => $logo_data
            ));
        } else {
            wp_send_json_error(['message' => 'Failed to save logo information']);
        }
    }

    /**
     * Remove logo for Report Formatter Widget
     */
    public function rf_remove_logo() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        global $wpdb;
        $table_name = 'wpcd_widget_settings';

        // Ottieni logo corrente
        $logo = $wpdb->get_row($wpdb->prepare(
            "SELECT setting_value FROM $table_name WHERE user_id = %d AND widget_type = 'report_formatter' AND setting_key = 'logo'",
            $user_id
        ));

        if ($logo) {
            $logo_data = json_decode($logo->setting_value, true);

            // Rimuovi file fisico
            if (isset($logo_data['file_path']) && file_exists($logo_data['file_path'])) {
                unlink($logo_data['file_path']);
            }

            // Rimuovi record dal database
            $wpdb->delete(
                $table_name,
                array(
                    'user_id' => $user_id,
                    'widget_type' => 'report_formatter',
                    'setting_key' => 'logo'
                ),
                array('%d', '%s', '%s')
            );

            wp_send_json_success(['message' => 'Logo removed successfully']);
        } else {
            wp_send_json_error(['message' => 'No logo found']);
        }
    }

    /**
     * Set default logo for Report Formatter Widget
     */
    public function rf_set_default_logo() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        global $wpdb;
        $table_name = 'wpcd_widget_settings';

        // Aggiorna flag default
        $result = $wpdb->update(
            $table_name,
            array('is_default' => 1),
            array(
                'user_id' => $user_id,
                'widget_type' => 'report_formatter',
                'setting_key' => 'logo'
            ),
            array('%d'),
            array('%d', '%s', '%s')
        );

        if ($result !== false) {
            wp_send_json_success(['message' => 'Logo set as default successfully']);
        } else {
            wp_send_json_error(['message' => 'Failed to set logo as default']);
        }
    }

    /**
     * Get logo for Report Formatter Widget
     */
    public function rf_get_logo() {
        check_ajax_referer('report_formatter_nonce', 'nonce');

        // Verifica autenticazione
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            wp_send_json_error(['message' => 'Authentication required']);
            return;
        }

        // Get current user info
        $user_info = $this->get_current_user_info();
        $user_id = $user_info['id'];

        global $wpdb;
        $table_name = 'wpcd_widget_settings';

        // Ottieni logo corrente
        $logo = $wpdb->get_row($wpdb->prepare(
            "SELECT setting_value, is_default FROM $table_name WHERE user_id = %d AND widget_type = 'report_formatter' AND setting_key = 'logo'",
            $user_id
        ));

        if ($logo) {
            $logo_data = json_decode($logo->setting_value, true);
            $logo_data['is_default'] = (bool)$logo->is_default;

            // Verifica che il file esista ancora
            if (isset($logo_data['file_path']) && !file_exists($logo_data['file_path'])) {
                // File non esiste più, rimuovi record
                $wpdb->delete(
                    $table_name,
                    array(
                        'user_id' => $user_id,
                        'widget_type' => 'report_formatter',
                        'setting_key' => 'logo'
                    ),
                    array('%d', '%s', '%s')
                );

                wp_send_json_success(['logo_data' => null]);
            } else {
                wp_send_json_success(['logo_data' => $logo_data]);
            }
        } else {
            wp_send_json_success(['logo_data' => null]);
        }
    }

    /**
     * Analyze spreadsheet data with AI
     */
    public function analyze_spreadsheet_data() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'spreadsheet_analysis_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        try {
            $analysis_data = json_decode(stripslashes($_POST['analysis_data']), true);

            if (!$analysis_data || !isset($analysis_data['query']) || !isset($analysis_data['data'])) {
                wp_send_json_error('Dati di analisi mancanti');
                return;
            }

            $query = sanitize_textarea_field($analysis_data['query']);
            $data = $analysis_data['data'];
            $selection_info = $analysis_data['selection_info'] ?? [];

            // Prepara il prompt per l'AI
            $data_text = $this->format_spreadsheet_data_for_ai($data);
            $full_prompt = "Analizza i seguenti dati di foglio elettronico:\n\n" . $data_text . "\n\nDomanda: " . $query . "\n\nFornisci un'analisi dettagliata e professionale.";

            // Chiama l'API AI (riusa la logica esistente)
            $ai_response = $this->call_spreadsheet_ai_api($full_prompt);

            if ($ai_response && isset($ai_response['choices'][0]['message']['content'])) {
                $analysis_result = $ai_response['choices'][0]['message']['content'];

                // Salva l'analisi nel database per tracking
                $this->save_spreadsheet_analysis($query, $data_text, $analysis_result);

                wp_send_json_success($analysis_result);
            } else {
                wp_send_json_error('Errore nella risposta dell\'AI');
            }

        } catch (Exception $e) {
            dv_debug_log('Spreadsheet analysis error: ' . $e->getMessage());
            wp_send_json_error('Errore durante l\'analisi: ' . $e->getMessage());
        }
    }

    /**
     * Format spreadsheet data for AI analysis
     */
    private function format_spreadsheet_data_for_ai($data) {
        $formatted = "Dati del foglio elettronico:\n";

        foreach ($data as $row_index => $row) {
            $formatted .= "Riga " . ($row_index + 1) . ": ";
            $formatted .= implode(" | ", array_map(function($cell) {
                return empty($cell) ? "[vuoto]" : $cell;
            }, $row));
            $formatted .= "\n";
        }

        return $formatted;
    }

    /**
     * Save spreadsheet analysis to database
     */
    private function save_spreadsheet_analysis($query, $data, $result) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'spreadsheet_analyses';

        // Create table if not exists
        $this->create_spreadsheet_analyses_table();

        $wpdb->insert(
            $table_name,
            array(
                'query' => $query,
                'data' => $data,
                'result' => $result,
                'user_id' => get_current_user_id(),
                'created_at' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%d', '%s')
        );
    }

    /**
     * Create spreadsheet analyses table
     */
    private function create_spreadsheet_analyses_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'spreadsheet_analyses';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            query text NOT NULL,
            data longtext NOT NULL,
            result longtext NOT NULL,
            user_id int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Call AI API for spreadsheet analysis
     */
    private function call_spreadsheet_ai_api($prompt) {
        // Get API settings
        $api_key = get_option('document_viewer_api_key');
        $api_endpoint = get_option('document_viewer_api_endpoint');
        $api_model = get_option('document_viewer_model');

        if (empty($api_key) || empty($api_endpoint) || empty($api_model)) {
            dv_debug_log('API settings not configured for spreadsheet analysis');
            return false;
        }

        $api_url = rtrim($api_endpoint, '/') . '/chat/completions';

        // Prepare request data
        $request_data = array(
            'model' => $api_model,
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => 'Sei un analista finanziario esperto. Analizza i dati forniti e fornisci insights dettagliati, professionali e actionable.'
                ),
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => 2000,
            'temperature' => 0.7
        );

        // Headers
        $headers = array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
            'HTTP-Referer' => site_url(),
            'X-Title' => 'Spreadsheet Analysis Widget',
            'Accept' => 'application/json'
        );

        // Make request
        $response = wp_remote_post($api_url, array(
            'headers' => $headers,
            'body' => json_encode($request_data),
            'timeout' => 60,
            'sslverify' => false
        ));

        if (is_wp_error($response)) {
            dv_debug_log('Spreadsheet AI API error: ' . $response->get_error_message());
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            dv_debug_log('Spreadsheet AI API JSON decode error: ' . json_last_error_msg());
            return false;
        }

        return $data;
    }

    /**
     * Export spreadsheet analysis to PDF
     */
    public function export_spreadsheet_analysis_pdf() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'spreadsheet_analysis_nonce')) {
            dv_debug_log('Spreadsheet PDF export: Invalid nonce');
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check if mPDF is available
        if (!$this->check_mpdf()) {
            dv_debug_log('Spreadsheet PDF export: mPDF not available');
            wp_send_json_error('PDF generation not available. Please ensure mPDF library is installed.');
            return;
        }

        try {
            dv_debug_log('Spreadsheet PDF export: Starting export process');

            $export_data = json_decode(stripslashes($_POST['export_data']), true);

            if (!$export_data || !isset($export_data['analysis'])) {
                dv_debug_log('Spreadsheet PDF export: Missing export data');
                wp_send_json_error('Dati di export mancanti');
                return;
            }

            $analysis = $export_data['analysis'];
            $selection_range = $export_data['selection_range'] ?? '';
            $selected_cells = $export_data['selected_cells'] ?? null;

            dv_debug_log('Spreadsheet PDF export: Data validated, generating PDF');

            // Genera PDF con celle selezionate
            $this->generate_spreadsheet_analysis_pdf($analysis, $selection_range, $selected_cells);

        } catch (Exception $e) {
            dv_debug_log('Spreadsheet PDF export error: ' . $e->getMessage());
            wp_send_json_error('Errore durante l\'export PDF: ' . $e->getMessage());
        } catch (Error $e) {
            dv_debug_log('Spreadsheet PDF export fatal error: ' . $e->getMessage());
            wp_send_json_error('Errore fatale durante l\'export PDF. Verificare che tutte le librerie siano installate correttamente.');
        }
    }

    /**
     * Generate PDF for spreadsheet analysis
     */
    private function generate_spreadsheet_analysis_pdf($analysis, $selection_range, $selected_cells = null) {
        try {
            // Load vendor libraries with error handling
            $autoload_path = plugin_dir_path(__FILE__) . 'vendor/autoload.php';
            if (!file_exists($autoload_path)) {
                throw new Exception('Autoload file not found. Please ensure vendor libraries are installed.');
            }

            require_once $autoload_path;

            // Check if mPDF class exists
            if (!class_exists('\Mpdf\Mpdf')) {
                throw new Exception('mPDF library not found. Please install the mPDF library.');
            }

            $mpdf = new \Mpdf\Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
                'margin_left' => 20,
                'margin_right' => 20,
                'margin_top' => 20,
                'margin_bottom' => 20,
                'tempDir' => sys_get_temp_dir()
            ]);

        // CSS per il PDF
        $css = "
        body { font-family: 'DejaVu Sans', sans-serif; font-size: 12pt; line-height: 1.6; }
        h1 { color: #007cba; font-size: 18pt; margin-bottom: 20px; }
        h2 { color: #007cba; font-size: 16pt; margin-bottom: 15px; }
        .header { border-bottom: 2px solid #007cba; padding-bottom: 10px; margin-bottom: 20px; }
        .selected-cells { background: #f0f8ff; padding: 15px; border: 1px solid #007cba; margin-bottom: 20px; border-radius: 5px; }
        .selected-cells table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .selected-cells th, .selected-cells td { border: 1px solid #ccc; padding: 5px; text-align: center; }
        .selected-cells th { background: #f8f9fa; font-weight: bold; }
        .selection-info { background: #f8f9fa; padding: 15px; border-left: 4px solid #007cba; margin-bottom: 20px; }
        .analysis-content { margin-top: 20px; }
        ";

        $mpdf->WriteHTML($css, \Mpdf\HTMLParserMode::HEADER_CSS);

        // Contenuto HTML
        $html = '<div class="header">';
        $html .= '<h1>📊 Analisi Foglio Elettronico</h1>';
        $html .= '<p><strong>Data:</strong> ' . date('d/m/Y H:i') . '</p>';
        if ($selection_range) {
            $html .= '<p><strong>Selezione:</strong> ' . esc_html($selection_range) . '</p>';
        }
        $html .= '</div>';

        // Sezione celle selezionate
        if ($selected_cells && isset($selected_cells['table_html'])) {
            $html .= '<div class="selected-cells">';
            $html .= '<h2>📋 Dati Selezionati</h2>';
            $html .= '<p><strong>Range:</strong> ' . esc_html($selected_cells['range']) . ' (' . $selected_cells['cell_count'] . ' celle)</p>';
            $html .= $selected_cells['table_html']; // HTML già sicuro dal JavaScript
            $html .= '</div>';
        }

        if (isset($analysis['query'])) {
            $html .= '<div class="selection-info">';
            $html .= '<h2>🔍 Query di Analisi</h2>';
            $html .= '<p>' . nl2br(esc_html($analysis['query'])) . '</p>';
            $html .= '</div>';
        }

        $html .= '<div class="analysis-content">';
        $html .= '<h2>📈 Risultati Analisi</h2>';
        // Usa la stessa formattazione del campo analisi (HTML formattato)
        $formatted_result = $this->format_analysis_text_for_pdf($analysis['result']);
        $html .= '<div>' . $formatted_result . '</div>';
        $html .= '</div>';

            $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);

            // Set PDF metadata
            $mpdf->SetTitle('Analisi Foglio Elettronico');
            $mpdf->SetAuthor('Financial Advisor Plugin');
            $mpdf->SetCreator('WordPress Financial Advisor Plugin');

            // Output PDF
            $filename = 'analisi-foglio-elettronico-' . date('Y-m-d-H-i-s') . '.pdf';

            // Set headers for download
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Cache-Control: private, max-age=0, must-revalidate');
            header('Pragma: public');

            $mpdf->Output($filename, 'D'); // 'D' = Download

            // Log successful generation
            dv_debug_log('Spreadsheet PDF generated successfully: ' . $filename);
            exit;

        } catch (\Mpdf\MpdfException $e) {
            dv_debug_log('mPDF specific error in spreadsheet analysis: ' . $e->getMessage());
            throw new Exception('Errore mPDF: ' . $e->getMessage());
        } catch (Exception $e) {
            dv_debug_log('PDF generation error in spreadsheet analysis: ' . $e->getMessage());
            throw new Exception('Errore nella generazione del PDF: ' . $e->getMessage());
        } catch (Error $e) {
            dv_debug_log('PHP Fatal error in PDF generation: ' . $e->getMessage());
            throw new Exception('Errore fatale nella generazione del PDF. Verificare che tutte le librerie siano installate correttamente.');
        }
    }

    /**
     * Parse Excel file to get sheet names and basic info
     */
    public function parse_excel_sheets() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'spreadsheet_analysis_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check if file was uploaded
        if (!isset($_FILES['excel_file']) || $_FILES['excel_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error('Nessun file caricato o errore durante l\'upload');
            return;
        }

        $uploaded_file = $_FILES['excel_file'];

        // Validate file extension first (more reliable than MIME type for Excel files)
        $file_extension = strtolower(pathinfo($uploaded_file['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['xlsx', 'xls', 'xlsm'];

        if (!in_array($file_extension, $allowed_extensions)) {
            wp_send_json_error('Estensione file non supportata. Usa file Excel (.xlsx, .xls, .xlsm)');
            return;
        }

        // Validate file type (secondary check, more lenient for Excel files)
        $allowed_types = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.ms-excel-sheet.macroEnabled.12', // .xlsm (official)
            'application/vnd.ms-excel.sheet.macroEnabled.12', // .xlsm (alternative)
            'application/vnd.ms-excel', // .xls
            'application/octet-stream', // Sometimes Excel files are detected as this
            'application/zip' // XLSM files are sometimes detected as ZIP
        ];

        // For Excel files, prioritize extension validation over MIME type
        if (!in_array($uploaded_file['type'], $allowed_types)) {
            dv_debug_log('Excel file MIME type not in allowed list: ' . $uploaded_file['type'] . ' but extension is valid: ' . $file_extension);
            // Continue processing since extension is valid
        }

        // Validate file size (max 100MB)
        $max_size = 100 * 1024 * 1024; // 100MB
        if ($uploaded_file['size'] > $max_size) {
            wp_send_json_error('File troppo grande. Dimensione massima: 100MB');
            return;
        }

        try {
            // Load PhpSpreadsheet with custom cache implementation
            require_once plugin_dir_path(__FILE__) . 'vendor/autoload.php';
            require_once plugin_dir_path(__FILE__) . 'includes/SimpleCache.php';

            // Set custom cache implementation to avoid PSR SimpleCache dependency issues
            try {
                \PhpOffice\PhpSpreadsheet\Settings::setCache(new \FinancialAdvisor\SimpleCache());
            } catch (Exception $e) {
                // Fallback: try to disable cache entirely
                dv_debug_log('PhpSpreadsheet custom cache setting failed, trying null: ' . $e->getMessage());
                try {
                    \PhpOffice\PhpSpreadsheet\Settings::setCache(null);
                } catch (Exception $e2) {
                    dv_debug_log('PhpSpreadsheet cache null setting also failed: ' . $e2->getMessage());
                }
            }

            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($uploaded_file['tmp_name']);
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($uploaded_file['tmp_name']);

            $sheets = [];
            $worksheetNames = $spreadsheet->getSheetNames();

            foreach ($worksheetNames as $index => $sheetName) {
                $worksheet = $spreadsheet->getSheet($index);
                $highestRow = $worksheet->getHighestRow();
                $highestColumn = $worksheet->getHighestColumn();

                $sheets[] = [
                    'name' => $sheetName,
                    'index' => $index,
                    'rows' => $highestRow,
                    'columns' => $highestColumn
                ];
            }

            wp_send_json_success([
                'sheets' => $sheets,
                'filename' => $uploaded_file['name']
            ]);

        } catch (Exception $e) {
            dv_debug_log('Excel parsing error: ' . $e->getMessage());
            wp_send_json_error('Errore durante l\'analisi del file Excel: ' . $e->getMessage());
        }
    }

    /**
     * Load specific Excel sheet data
     */
    public function load_excel_sheet() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'spreadsheet_analysis_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check if file was uploaded
        if (!isset($_FILES['excel_file']) || $_FILES['excel_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error('Nessun file caricato o errore durante l\'upload');
            return;
        }

        // Get sheet index
        $sheet_index = isset($_POST['sheet_index']) ? intval($_POST['sheet_index']) : 0;

        $uploaded_file = $_FILES['excel_file'];

        try {
            // Load PhpSpreadsheet with custom cache implementation
            require_once plugin_dir_path(__FILE__) . 'vendor/autoload.php';
            require_once plugin_dir_path(__FILE__) . 'includes/SimpleCache.php';

            // Set custom cache implementation to avoid PSR SimpleCache dependency issues
            try {
                \PhpOffice\PhpSpreadsheet\Settings::setCache(new \FinancialAdvisor\SimpleCache());
            } catch (Exception $e) {
                // Fallback: try to disable cache entirely
                dv_debug_log('PhpSpreadsheet custom cache setting failed, trying null: ' . $e->getMessage());
                try {
                    \PhpOffice\PhpSpreadsheet\Settings::setCache(null);
                } catch (Exception $e2) {
                    dv_debug_log('PhpSpreadsheet cache null setting also failed: ' . $e2->getMessage());
                }
            }

            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($uploaded_file['tmp_name']);
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($uploaded_file['tmp_name']);

            // Get the specified worksheet
            $worksheet = $spreadsheet->getSheet($sheet_index);
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();

            // Convert column letter to number for iteration
            $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

            // Limit to reasonable size for web display (max 60 rows, 30 columns)
            $maxRows = min($highestRow, 60);
            $maxCols = min($highestColumnIndex, 30);

            $data = [];

            // Extract data from worksheet with formatting
            for ($row = 1; $row <= $maxRows; $row++) {
                $rowData = [];
                for ($col = 1; $col <= $maxCols; $col++) {
                    $cellCoordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col) . $row;
                    $cell = $worksheet->getCell($cellCoordinate);
                    $cellValue = $cell->getCalculatedValue();

                    // Get cell formatting
                    $style = $cell->getStyle();
                    $formatting = [];

                    // Get number format
                    $numberFormat = $style->getNumberFormat()->getFormatCode();
                    if ($numberFormat && $numberFormat !== 'General') {
                        $formatting['numberFormat'] = $numberFormat;
                    }

                    // Get alignment
                    $alignment = $style->getAlignment()->getHorizontal();
                    if ($alignment && $alignment !== 'general') {
                        $formatting['align'] = $alignment;
                    }

                    // Get background color
                    $fill = $style->getFill();
                    if ($fill->getFillType() !== 'none') {
                        $bgColor = $fill->getStartColor()->getRGB();
                        if ($bgColor && $bgColor !== '000000') {
                            $formatting['backgroundColor'] = '#' . $bgColor;
                        }
                    }

                    // Get font color
                    $font = $style->getFont();
                    $fontColor = $font->getColor()->getRGB();
                    if ($fontColor && $fontColor !== '000000') {
                        $formatting['color'] = '#' . $fontColor;
                    }

                    // Get font weight
                    if ($font->getBold()) {
                        $formatting['fontWeight'] = 'bold';
                    }

                    // Convert to string and handle different data types
                    if ($cellValue === null) {
                        $cellValue = '';
                    } elseif (is_bool($cellValue)) {
                        $cellValue = $cellValue ? 'TRUE' : 'FALSE';
                    } elseif (is_numeric($cellValue)) {
                        // Preserve original formatting for numbers
                        if (isset($formatting['numberFormat'])) {
                            $cellValue = $cell->getFormattedValue();
                        } else {
                            $cellValue = is_float($cellValue) ? number_format($cellValue, 2, '.', '') : (string)$cellValue;
                        }
                        $formatting['type'] = 'number';
                    } else {
                        $cellValue = (string)$cellValue;
                        $formatting['type'] = 'text';
                    }

                    // Create cell data object with value and formatting
                    $cellData = [
                        'value' => $cellValue,
                        'formatting' => $formatting
                    ];

                    $rowData[] = $cellData;
                }
                $data[] = $rowData;
            }

            wp_send_json_success([
                'data' => $data,
                'rows' => $maxRows,
                'columns' => $maxCols,
                'sheet_name' => $worksheet->getTitle()
            ]);

        } catch (Exception $e) {
            dv_debug_log('Excel sheet loading error: ' . $e->getMessage());
            wp_send_json_error('Errore durante il caricamento del foglio: ' . $e->getMessage());
        }
    }

    /**
     * Format analysis text for PDF (same as frontend)
     */
    private function format_analysis_text_for_pdf($text) {
        // Converti markdown-like in HTML (stesso algoritmo del frontend)
        $formatted = $text;

        // Titoli principali (## Titolo)
        $formatted = preg_replace('/^## (.+)$/m', '<h2>$1</h2>', $formatted);

        // Titoli secondari (### Titolo)
        $formatted = preg_replace('/^### (.+)$/m', '<h3>$1</h3>', $formatted);

        // Titoli terziari (#### Titolo)
        $formatted = preg_replace('/^#### (.+)$/m', '<h4>$1</h4>', $formatted);

        // Grassetto (**testo**)
        $formatted = preg_replace('/\*\*(.+?)\*\*/', '<strong>$1</strong>', $formatted);

        // Corsivo (*testo*)
        $formatted = preg_replace('/\*(.+?)\*/', '<em>$1</em>', $formatted);

        // Liste puntate (- item)
        $formatted = preg_replace('/^- (.+)$/m', '<li>$1</li>', $formatted);

        // Avvolgi liste consecutive in <ul>
        $formatted = preg_replace('/(<li>.*<\/li>)/s', '<ul>$1</ul>', $formatted);

        // Liste numerate (1. item)
        $formatted = preg_replace('/^\d+\. (.+)$/m', '<li>$1</li>', $formatted);

        // Paragrafi (righe vuote)
        $formatted = preg_replace('/\n\n/', '</p><p>', $formatted);
        $formatted = '<p>' . $formatted . '</p>';

        // Pulisci paragrafi vuoti
        $formatted = preg_replace('/<p><\/p>/', '', $formatted);
        $formatted = preg_replace('/<p>\s*<\/p>/', '', $formatted);

        return $formatted;
    }

    /**
     * Check AI connection status
     */
    public function check_ai_status() {
        // Verify nonce (accept both spreadsheet and balance sheet nonces)
        if (isset($_POST['nonce'])) {
            $valid_nonce = wp_verify_nonce($_POST['nonce'], 'spreadsheet_analysis_nonce') ||
                          wp_verify_nonce($_POST['nonce'], 'balance_sheet_analysis_nonce');
            if (!$valid_nonce) {
                wp_send_json_error('Invalid nonce');
                return;
            }
        }

        try {
            // Get API settings
            $api_key = get_option('document_viewer_api_key');
            $api_endpoint = get_option('document_viewer_api_endpoint');
            $api_model = get_option('document_viewer_model');

            // Check if settings are configured
            if (empty($api_key)) {
                wp_send_json_error('API key non configurata. Verifica le impostazioni del plugin.');
                return;
            }

            if (empty($api_endpoint)) {
                wp_send_json_error('Endpoint API non configurato. Verifica le impostazioni del plugin.');
                return;
            }

            if (empty($api_model)) {
                wp_send_json_error('Modello AI non configurato. Verifica le impostazioni del plugin.');
                return;
            }

            // Semplice conferma collegamento AI
            wp_send_json_success([
                'message' => 'AI connesso e operativo',
                'status' => 'connected'
            ]);

        } catch (Exception $e) {
            dv_debug_log('AI status check error: ' . $e->getMessage());
            wp_send_json_error('Errore durante la verifica: ' . $e->getMessage());
        }
    }

    /**
     * NUOVA FUNZIONALITÀ: Handler AJAX per recuperare analisi salvate
     */
    public function handle_get_saved_analyses() {
        // Verifica nonce per sicurezza
        if (!wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
            wp_send_json_error('Nonce verification failed');
            return;
        }

        try {
            global $wpdb;

            // Ottieni informazioni utente corrente
            $user_info = $this->get_current_user_info();
            if (!$user_info) {
                wp_send_json_error('Utente non autenticato');
                return;
            }

            $user_id = $user_info['id'];

            // Query per recuperare le analisi salvate dell'utente
            $table_name = $wpdb->prefix . 'document_analysis';

            $query = $wpdb->prepare("
                SELECT
                    id,
                    title,
                    analysis_results as analysis_content,
                    0 as token_count,
                    created_at,
                    document_path as document_name
                FROM {$table_name}
                WHERE user_id = %d
                AND analysis_results IS NOT NULL
                AND analysis_results != ''
                ORDER BY created_at DESC
                LIMIT 50
            ", $user_id);

            $analyses = $wpdb->get_results($query, ARRAY_A);

            if ($wpdb->last_error) {
                error_log('Errore database get_saved_analyses: ' . $wpdb->last_error);
                wp_send_json_error('Errore nel recupero delle analisi dal database');
                return;
            }

            // Processa i risultati per l'interfaccia
            $processed_analyses = array();
            foreach ($analyses as $analysis) {
                $processed_analyses[] = array(
                    'id' => $analysis['id'],
                    'title' => $analysis['title'] ?: 'Analisi senza titolo',
                    'analysis_content' => $analysis['analysis_content'],
                    'token_count' => $analysis['token_count'] ?: 0,
                    'created_at' => $analysis['created_at'],
                    'document_name' => $analysis['document_name'] ? basename($analysis['document_name']) : 'Documento non specificato'
                );
            }

            wp_send_json_success($processed_analyses);

        } catch (Exception $e) {
            error_log('Errore handle_get_saved_analyses: ' . $e->getMessage());
            wp_send_json_error('Errore durante il recupero delle analisi salvate: ' . $e->getMessage());
        }
    }

    /**
     * ====================================================================
     * WORD ANALYSIS WIDGET AJAX HANDLERS
     * ====================================================================
     */

    /**
     * Extract content from Word document (AJAX handler)
     */
    public function extract_word_content() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        // Check if file was uploaded
        if (!isset($_FILES['word_file']) || $_FILES['word_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(['message' => 'Nessun file caricato o errore durante l\'upload']);
            return;
        }

        $uploaded_file = $_FILES['word_file'];

        // Validate file extension
        $file_extension = strtolower(pathinfo($uploaded_file['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['doc', 'docx'];

        if (!in_array($file_extension, $allowed_extensions)) {
            wp_send_json_error(['message' => 'Estensione file non supportata. Usa file Word (.doc, .docx)']);
            return;
        }

        // Validate file type
        $allowed_types = [
            'application/msword', // .doc
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
            'application/octet-stream' // Sometimes Word files are detected as this
        ];

        if (!in_array($uploaded_file['type'], $allowed_types)) {
            dv_debug_log('Word file MIME type not in allowed list: ' . $uploaded_file['type'] . ' but extension is valid: ' . $file_extension);
            // Continue processing since extension is valid
        }

        // Validate file size (max 10MB)
        $max_size = 10 * 1024 * 1024; // 10MB
        if ($uploaded_file['size'] > $max_size) {
            wp_send_json_error(['message' => 'File troppo grande. Dimensione massima: 10MB']);
            return;
        }

        try {
            // Create temporary file path
            $upload_dir = wp_upload_dir();
            $temp_dir = $upload_dir['basedir'] . '/document-viewer';
            
            if (!file_exists($temp_dir)) {
                wp_mkdir_p($temp_dir);
            }

            $temp_filename = uniqid('word_') . '.' . $file_extension;
            $temp_path = $temp_dir . '/' . $temp_filename;

            // Move uploaded file to temporary location
            if (!move_uploaded_file($uploaded_file['tmp_name'], $temp_path)) {
                throw new Exception('Impossibile salvare il file temporaneo');
            }

            // Extract content using document management class
            if (!class_exists('Document_Management')) {
                require_once plugin_dir_path(__FILE__) . 'includes/class-document-management.php';
            }

            $document_manager = new Document_Management();
            
            // Use the appropriate extraction method based on file type
            if ($file_extension === 'docx') {
                $extracted_content = $document_manager->extract_docx_content($temp_path);
            } else {
                // For .doc files, we'll try to convert or use a fallback
                // For now, we'll use a basic text extraction approach
                $extracted_content = $this->extract_doc_content($temp_path);
            }

            // Clean up temporary file
            if (file_exists($temp_path)) {
                unlink($temp_path);
            }

            // Convert plain text to basic HTML
            $html_content = $this->convert_text_to_html($extracted_content);

            wp_send_json_success([
                'content' => $html_content,
                'raw_content' => $extracted_content,
                'message' => 'Contenuto estratto con successo'
            ]);

        } catch (Exception $e) {
            dv_debug_log('Word content extraction error: ' . $e->getMessage());
            
            // Clean up temporary file if it exists
            if (isset($temp_path) && file_exists($temp_path)) {
                unlink($temp_path);
            }
            
            wp_send_json_error(['message' => 'Errore durante l\'estrazione: ' . $e->getMessage()]);
        }
    }

    /**
     * Analyze Word document content with AI (AJAX handler)
     */
    public function analyze_word_content() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            $analysis_data = json_decode(stripslashes($_POST['analysis_data']), true);

            if (!$analysis_data || !isset($analysis_data['query']) || !isset($analysis_data['content'])) {
                wp_send_json_error(['message' => 'Dati di analisi mancanti']);
                return;
            }

            $query = sanitize_textarea_field($analysis_data['query']);
            $content = sanitize_textarea_field($analysis_data['content']);
            $document_name = sanitize_text_field($analysis_data['document_name'] ?? 'Documento');

            // Prepare AI prompt
            $full_prompt = "Analizza il seguente documento:\n\n" . 
                          "DOCUMENTO: " . $document_name . "\n\n" .
                          "CONTENUTO:\n" . $content . "\n\n" .
                          "DOMANDA: " . $query . "\n\n" .
                          "Fornisci un'analisi dettagliata e professionale del documento in base alla domanda posta.";

            // Call AI API (reuse existing logic)
            $ai_response = $this->call_word_ai_api($full_prompt);

            if ($ai_response && isset($ai_response['choices'][0]['message']['content'])) {
                $analysis_result = $ai_response['choices'][0]['message']['content'];

                // Save analysis to database for tracking
                $this->save_word_analysis($query, $content, $analysis_result, $document_name);

                wp_send_json_success($analysis_result);
            } else {
                wp_send_json_error(['message' => 'Errore nella risposta dell\'AI']);
            }

        } catch (Exception $e) {
            dv_debug_log('Word analysis error: ' . $e->getMessage());
            wp_send_json_error(['message' => 'Errore durante l\'analisi: ' . $e->getMessage()]);
        }
    }

    /**
     * Export Word analysis to PDF (AJAX handler)
     */
    public function export_word_analysis_pdf() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_nonce')) {
            dv_debug_log('Word PDF export: Invalid nonce');
            http_response_code(403);
            die('Invalid nonce');
        }

        // Check if mPDF is available
        if (!$this->check_mpdf()) {
            dv_debug_log('Word PDF export: mPDF not available');
            http_response_code(500);
            die('PDF generation not available. Please ensure mPDF library is installed.');
        }

        try {
            dv_debug_log('Word PDF export: Starting export process');

            $analysis_data = json_decode(stripslashes($_POST['analysis_data']), true);

            if (!$analysis_data || !isset($analysis_data['content'])) {
                dv_debug_log('Word PDF export: Missing analysis data');
                http_response_code(400);
                die('Dati di export mancanti');
            }

            $title = $analysis_data['title'] ?? 'Analisi Documento Word';
            $content = $analysis_data['content'];
            $document = $analysis_data['document'] ?? 'Documento';
            $timestamp = $analysis_data['timestamp'] ?? date('Y-m-d H:i:s');

            dv_debug_log('Word PDF export: Data validated, generating PDF');

            // Generate PDF
            $this->generate_word_analysis_pdf($title, $content, $document, $timestamp);

        } catch (Exception $e) {
            dv_debug_log('Word PDF export error: ' . $e->getMessage());
            http_response_code(500);
            die('Errore durante l\'export PDF: ' . $e->getMessage());
        } catch (Error $e) {
            dv_debug_log('Word PDF export fatal error: ' . $e->getMessage());
            http_response_code(500);
            die('Errore fatale durante l\'export PDF. Verificare che tutte le librerie siano installate correttamente.');
        }
    }

    /**
     * ====================================================================
     * WORD ANALYSIS HELPER METHODS
     * ====================================================================
     */

    /**
     * Convert plain text to basic HTML
     */
    private function convert_text_to_html($text) {
        // Convert line breaks to HTML
        $html = nl2br(htmlspecialchars($text, ENT_QUOTES, 'UTF-8'));
        
        // Convert paragraphs (double line breaks)
        $html = preg_replace('/(<br\s*\/?>\s*){2,}/', '</p><p>', $html);
        
        // Wrap in paragraphs
        $html = '<p>' . $html . '</p>';
        
        // Clean up empty paragraphs
        $html = preg_replace('/<p><\/p>/', '', $html);
        $html = preg_replace('/<p>\s*<\/p>/', '', $html);
        
        return $html;
    }

    /**
     * Extract content from older .doc files (fallback method)
     */
    private function extract_doc_content($file_path) {
        // Basic text extraction for .doc files
        // This is a simplified approach for older Word formats
        try {
            $content = file_get_contents($file_path);
            
            // Remove binary content and try to extract readable text
            $content = preg_replace('/[^\x09\x0A\x0D\x20-\x7E\x80-\xFF]/', '', $content);
            
            // Clean up the text
            $content = preg_replace('/\s+/', ' ', $content);
            $content = trim($content);
            
            if (empty($content)) {
                throw new Exception('Impossibile estrarre testo dal file .doc. Prova a convertirlo in formato .docx');
            }
            
            return $content;
            
        } catch (Exception $e) {
            dv_debug_log('DOC extraction error: ' . $e->getMessage());
            throw new Exception('Errore nell\'estrazione del contenuto dal file .doc: ' . $e->getMessage());
        }
    }

    /**
     * Call AI API for Word document analysis
     */
    private function call_word_ai_api($prompt) {
        // Get API settings
        $api_key = get_option('document_viewer_api_key');
        $api_endpoint = get_option('document_viewer_api_endpoint');
        $api_model = get_option('document_viewer_model');

        if (empty($api_key) || empty($api_endpoint) || empty($api_model)) {
            dv_debug_log('API settings not configured for word analysis');
            return false;
        }

        $api_url = rtrim($api_endpoint, '/') . '/chat/completions';

        // Prepare request data
        $request_data = array(
            'model' => $api_model,
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => 'Sei un analista di documenti esperto. Analizza i documenti forniti e fornisci insights dettagliati, professionali e actionable in lingua italiana.'
                ),
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => 2000,
            'temperature' => 0.7
        );

        // Headers
        $headers = array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
            'HTTP-Referer' => site_url(),
            'X-Title' => 'Word Analysis Widget',
            'Accept' => 'application/json'
        );

        // Make request
        $response = wp_remote_post($api_url, array(
            'headers' => $headers,
            'body' => json_encode($request_data),
            'timeout' => 60,
            'sslverify' => false
        ));

        if (is_wp_error($response)) {
            dv_debug_log('Word AI API error: ' . $response->get_error_message());
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            dv_debug_log('Word AI API JSON decode error: ' . json_last_error_msg());
            return false;
        }

        return $data;
    }

    /**
     * Save Word analysis to database
     */
    private function save_word_analysis($query, $content, $analysis_result, $document_name) {
        try {
            global $wpdb;

            // Get current user info
            $user_info = $this->get_current_user_info();
            $user_id = $user_info['id'] ?? 0;

            // Prepare data for database
            $table_name = $wpdb->prefix . 'document_analysis';
            
            $data = [
                'user_id' => $user_id,
                'title' => 'Analisi: ' . substr($query, 0, 50) . '...',
                'analysis_results' => $analysis_result,
                'document_path' => $document_name,
                'created_at' => current_time('mysql'),
                'widget_type' => 'word_analysis'
            ];

            $wpdb->insert($table_name, $data);

            if ($wpdb->last_error) {
                dv_debug_log('Database error saving Word analysis: ' . $wpdb->last_error);
            } else {
                dv_debug_log('Word analysis saved successfully with ID: ' . $wpdb->insert_id);
            }

        } catch (Exception $e) {
            dv_debug_log('Error saving Word analysis: ' . $e->getMessage());
        }
    }

    /**
     * Generate PDF for Word analysis
     */
    private function generate_word_analysis_pdf($title, $content, $document, $timestamp) {
        try {
            // Load vendor libraries
            $autoload_path = plugin_dir_path(__FILE__) . 'vendor/autoload.php';
            if (!file_exists($autoload_path)) {
                throw new Exception('Autoload file not found. Please ensure vendor libraries are installed.');
            }

            require_once $autoload_path;

            // Create mPDF instance
            $mpdf = new \Mpdf\Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
                'margin_left' => 20,
                'margin_right' => 20,
                'margin_top' => 30,
                'margin_bottom' => 25,
                'margin_header' => 10,
                'margin_footer' => 10
            ]);

            // Set document properties
            $mpdf->SetTitle($title);
            $mpdf->SetAuthor('Financial Advisor Plugin');
            $mpdf->SetCreator('Word Analysis Widget');

            // Format content for PDF
            $formatted_content = $this->format_analysis_text_for_pdf($content);

            // Create PDF content
            $html = '
            <style>
                body { font-family: Arial, sans-serif; font-size: 11pt; line-height: 1.6; }
                h1 { color: #2c3e50; font-size: 18pt; margin-bottom: 20px; }
                h2 { color: #34495e; font-size: 14pt; margin-top: 20px; margin-bottom: 10px; }
                h3 { color: #34495e; font-size: 12pt; margin-top: 15px; margin-bottom: 8px; }
                h4 { color: #34495e; font-size: 11pt; margin-top: 12px; margin-bottom: 6px; }
                p { margin-bottom: 10px; text-align: justify; }
                ul, ol { margin-bottom: 10px; padding-left: 20px; }
                li { margin-bottom: 5px; }
                strong { font-weight: bold; }
                em { font-style: italic; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #3498db; padding-bottom: 15px; }
                .meta { background-color: #f8f9fa; padding: 15px; margin-bottom: 20px; border-left: 4px solid #3498db; }
                .content { margin-top: 20px; }
                .footer { margin-top: 30px; text-align: center; font-size: 9pt; color: #7f8c8d; }
            </style>
            
            <div class="header">
                <h1>' . htmlspecialchars($title) . '</h1>
            </div>
            
            <div class="meta">
                <strong>Documento:</strong> ' . htmlspecialchars($document) . '<br>
                <strong>Data Analisi:</strong> ' . date('d/m/Y H:i', strtotime($timestamp)) . '<br>
                <strong>Generato da:</strong> Word Analysis Widget
            </div>
            
            <div class="content">
                ' . $formatted_content . '
            </div>
            
            <div class="footer">
                <p>Analisi generata automaticamente dal sistema Financial Advisor</p>
            </div>
            ';

            // Add content to PDF
            $mpdf->WriteHTML($html);

            // Set headers for download
            $filename = 'analisi_word_' . date('Y-m-d_H-i-s') . '.pdf';
            
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Cache-Control: private, max-age=0, must-revalidate');
            header('Pragma: public');

            // Output PDF
            $mpdf->Output($filename, 'D');

        } catch (Exception $e) {
            dv_debug_log('Word PDF generation error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * ====================================================================
     * END WORD ANALYSIS WIDGET METHODS
     * ====================================================================
     */

    /**
     * ====================================================================
     * WORD ANALYSIS ADVANCED WIDGET METHODS
     * ====================================================================
     */

    /**
     * Process document upload for Word Analysis Advanced (AJAX handler)
     */
    public function word_advanced_process_document() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            // Check if file was uploaded
            if (!isset($_FILES['document_file']) || $_FILES['document_file']['error'] !== UPLOAD_ERR_OK) {
                wp_send_json_error(['message' => 'Nessun file caricato o errore durante l\'upload']);
                return;
            }

            // Get widget instance (create static instance for processing)
            if (!class_exists('Word_Analysis_Advanced_Widget')) {
                require_once plugin_dir_path(__FILE__) . 'includes/widgets/word-analysis-advanced-widget.php';
            }

            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->process_document_upload($_FILES['document_file']);

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced document processing error: ' . $e->getMessage());
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Analyze document with AI for Word Analysis Advanced (AJAX handler)
     */
    public function word_advanced_analyze_document() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            $analysis_data = json_decode(stripslashes($_POST['analysis_data']), true);

            if (!$analysis_data || !isset($analysis_data['query']) || !isset($analysis_data['document_data'])) {
                wp_send_json_error(['message' => 'Dati di analisi mancanti']);
                return;
            }

            // Get widget instance
            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->analyze_document(
                $analysis_data['document_data'],
                $analysis_data['query'],
                $analysis_data['selected_content'] ?? null
            );

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced document analysis error: ' . $e->getMessage());
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Get smart suggestions for Word Analysis Advanced (AJAX handler)
     */
    public function word_advanced_get_suggestions() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            $document_data = json_decode(stripslashes($_POST['document_data']), true);

            if (!$document_data) {
                wp_send_json_error(['message' => 'Dati documento mancanti']);
                return;
            }

            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->get_smart_suggestions($document_data);

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced suggestions error: ' . $e->getMessage());
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Add annotation for Word Analysis Advanced (AJAX handler)
     */
    public function word_advanced_add_annotation() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            $document_id = sanitize_text_field($_POST['document_id']);
            $annotation_data = json_decode(stripslashes($_POST['annotation_data']), true);

            if (!$document_id || !$annotation_data) {
                wp_send_json_error(['message' => 'Dati annotazione mancanti']);
                return;
            }

            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->add_annotation($document_id, $annotation_data);

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced add annotation error: ' . $e->getMessage());
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Get annotations for Enhanced Document Analysis (AJAX handler)
     */
    public function word_advanced_get_annotations() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            $document_id = sanitize_text_field($_POST['document_id']);
            $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : null;

            if (!$document_id) {
                wp_send_json_error(['message' => 'ID documento mancante']);
                return;
            }

            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->get_annotations($document_id, $user_id);

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced get annotations error: ' . $e->getMessage());
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Update annotation for Enhanced Document Analysis (AJAX handler)
     */
    public function word_advanced_update_annotation() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            $annotation_id = intval($_POST['annotation_id']);
            $annotation_data = json_decode(stripslashes($_POST['annotation_data']), true);

            if (!$annotation_id || !$annotation_data) {
                wp_send_json_error(['message' => 'Dati annotazione mancanti']);
                return;
            }

            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->update_annotation($annotation_id, $annotation_data);

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced update annotation error: ' . $e->getMessage());
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Delete annotation for Enhanced Document Analysis (AJAX handler)
     */
    public function word_advanced_delete_annotation() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            $annotation_id = intval($_POST['annotation_id']);

            if (!$annotation_id) {
                wp_send_json_error(['message' => 'ID annotazione mancante']);
                return;
            }

            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->delete_annotation($annotation_id);

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced delete annotation error: ' . $e->getMessage());
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Add highlight for Enhanced Document Analysis (AJAX handler)
     */
    public function word_advanced_add_highlight() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            $document_id = sanitize_text_field($_POST['document_id']);
            $highlight_data = json_decode(stripslashes($_POST['highlight_data']), true);

            if (!$document_id || !$highlight_data) {
                wp_send_json_error(['message' => 'Dati evidenziazione mancanti']);
                return;
            }

            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->add_highlight($document_id, $highlight_data);

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced add highlight error: ' . $e->getMessage());
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Export document with annotations for Enhanced Document Analysis (AJAX handler)
     */
    public function word_advanced_export_document() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            dv_debug_log('Enhanced export: Invalid nonce');
            http_response_code(403);
            die('Invalid nonce');
        }

        try {
            $document_id = sanitize_text_field($_POST['document_id']);

            if (!$document_id) {
                http_response_code(400);
                die('ID documento mancante');
            }

            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->export_with_annotations($document_id);

            // If export returns a file path, serve the file
            if (isset($result['export_path']) && file_exists($result['export_path'])) {
                $filename = basename($result['export_path']);
                
                header('Content-Type: application/pdf');
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                header('Cache-Control: private, max-age=0, must-revalidate');
                header('Pragma: public');
                
                readfile($result['export_path']);
                
                // Clean up temp file
                unlink($result['export_path']);
                exit;
            }

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced export error: ' . $e->getMessage());
            http_response_code(500);
            die('Errore durante l\'export: ' . $e->getMessage());
        }
    }

    /**
     * Search annotations for Enhanced Document Analysis (AJAX handler)
     */
    public function word_advanced_search_annotations() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            $document_id = sanitize_text_field($_POST['document_id']);
            $search_term = sanitize_text_field($_POST['search_term']);
            $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : null;

            if (!$document_id || !$search_term) {
                wp_send_json_error(['message' => 'Parametri di ricerca mancanti']);
                return;
            }

            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->search_annotations($document_id, $search_term, $user_id);

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced search annotations error: ' . $e->getMessage());
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Get annotation statistics for Enhanced Document Analysis (AJAX handler)
     */
    public function word_advanced_get_stats() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            $document_id = sanitize_text_field($_POST['document_id']);
            $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : null;

            if (!$document_id) {
                wp_send_json_error(['message' => 'ID documento mancante']);
                return;
            }

            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->get_annotation_stats($document_id, $user_id);

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced get stats error: ' . $e->getMessage());
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * Cleanup temporary files for Enhanced Document Analysis (AJAX handler)
     */
    public function word_advanced_cleanup_files() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'word_analysis_advanced_nonce')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        try {
            $widget = new Word_Analysis_Advanced_Widget();
            $result = $widget->cleanup_temp_files();

            wp_send_json_success($result);

        } catch (Exception $e) {
            dv_debug_log('Enhanced cleanup error: ' . $e->getMessage());
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }

    /**
     * ====================================================================
     * END WORD ANALYSIS ADVANCED WIDGET METHODS
     * ====================================================================
     */

    /**
     * Analyze balance sheet document (AJAX handler)
     */
    public function analyze_balance_sheet() {
        dv_debug_log("=== INIZIO analyze_balance_sheet() ===");

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'balance_sheet_analysis_nonce')) {
            dv_debug_log("Nonce verification failed for balance sheet analysis");
            wp_send_json_error(array('message' => __('Security check failed.', 'document-viewer-plugin')));
            return;
        }

        // Get parameters
        $text = isset($_POST['text']) ? sanitize_textarea_field($_POST['text']) : '';
        $query = isset($_POST['query']) ? sanitize_textarea_field($_POST['query']) : '';
        $schema_id = isset($_POST['schema_id']) ? intval($_POST['schema_id']) : 0;

        if (empty($text) || empty($query)) {
            wp_send_json_error(array('message' => __('Text and query are required.', 'document-viewer-plugin')));
            return;
        }

        dv_debug_log("Balance sheet analysis request - Text length: " . strlen($text) . ", Query length: " . strlen($query) . ", Schema ID: " . $schema_id);

        try {
            // Use the existing Document_Analyzer class
            require_once plugin_dir_path(__FILE__) . 'includes/class-document-analyzer.php';

            $document_analyzer = new Document_Analyzer();
            $result = $document_analyzer->analyze_text($text, $query);

            if ($result['success']) {
                dv_debug_log("Balance sheet analysis completed successfully");
                wp_send_json_success(array('result' => $result['response']));
            } else {
                dv_debug_log("Balance sheet analysis failed: " . $result['error']);
                wp_send_json_error(array('message' => $result['error']));
            }
        } catch (Exception $e) {
            dv_debug_log("Exception in balance sheet analysis: " . $e->getMessage());
            wp_send_json_error(array('message' => __('Analysis failed due to an error.', 'document-viewer-plugin')));
        }
    }

    /**
     * Save balance sheet analysis (AJAX handler)
     */
    public function save_balance_sheet_analysis() {
        dv_debug_log("=== INIZIO save_balance_sheet_analysis() ===");

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'balance_sheet_analysis_nonce')) {
            dv_debug_log("Nonce verification failed for save balance sheet analysis");
            wp_send_json_error(array('message' => __('Security check failed.', 'document-viewer-plugin')));
            return;
        }

        // Get parameters
        $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : '';
        $query = isset($_POST['query']) ? wp_kses_post($_POST['query']) : ''; // Preserve formatting
        $analysis_results = isset($_POST['analysis_results']) ? $_POST['analysis_results'] : ''; // Keep raw text from AI
        $schema_id = isset($_POST['schema_id']) ? intval($_POST['schema_id']) : 0;
        $document_name = isset($_POST['document_name']) ? sanitize_text_field($_POST['document_name']) : '';

        // Debug: Log what we're about to save
        dv_debug_log("Saving analysis - Title: " . $title);
        dv_debug_log("Analysis results length: " . strlen($analysis_results));
        dv_debug_log("Analysis results preview: " . substr($analysis_results, 0, 200) . "...");

        if (empty($title) || empty($analysis_results)) {
            wp_send_json_error(array('message' => __('Title and analysis results are required.', 'document-viewer-plugin')));
            return;
        }

        try {
            // Get current user info
            $user_info = $this->get_current_user_info();
            $user_id = $user_info['id'];

            // Sanitize analysis results while preserving formatting
            $sanitized_analysis = wp_kses($analysis_results, array(
                'p' => array(),
                'br' => array(),
                'strong' => array(),
                'b' => array(),
                'em' => array(),
                'i' => array(),
                'h1' => array(),
                'h2' => array(),
                'h3' => array(),
                'h4' => array(),
                'ul' => array(),
                'ol' => array(),
                'li' => array(),
                'table' => array('class' => array()),
                'thead' => array(),
                'tbody' => array(),
                'tr' => array(),
                'th' => array(),
                'td' => array(),
                'div' => array('class' => array()),
                'span' => array('class' => array())
            ));

            // Save to document_analysis table (existing table)
            global $wpdb;
            $table_name = $wpdb->prefix . 'document_analysis';

            $result = $wpdb->insert(
                $table_name,
                array(
                    'user_id' => $user_id,
                    'title' => $title,
                    'query' => $query,
                    'analysis_results' => $sanitized_analysis,
                    'document_path' => $document_name,
                    'created_at' => current_time('mysql')
                ),
                array('%d', '%s', '%s', '%s', '%s', '%s')
            );

            if ($result !== false) {
                $analysis_id = $wpdb->insert_id;
                dv_debug_log("Balance sheet analysis saved successfully with ID: " . $analysis_id);
                wp_send_json_success(array(
                    'message' => __('Analysis saved successfully.', 'document-viewer-plugin'),
                    'analysis_id' => $analysis_id
                ));
            } else {
                dv_debug_log("Failed to save balance sheet analysis: " . $wpdb->last_error);
                wp_send_json_error(array('message' => __('Failed to save analysis.', 'document-viewer-plugin')));
            }
        } catch (Exception $e) {
            dv_debug_log("Exception in save balance sheet analysis: " . $e->getMessage());
            wp_send_json_error(array('message' => __('Save failed due to an error.', 'document-viewer-plugin')));
        }
    }

    /**
     * Handle save balance sheet schema (AJAX handler)
     */
    public function handle_save_balance_sheet_schema() {
        require_once plugin_dir_path(__FILE__) . 'includes/class-balance-sheet-schemas-admin.php';
        $schemas_admin = new Balance_Sheet_Schemas_Admin();
        $schemas_admin->save_schema();
    }

    /**
     * Handle delete balance sheet schema (AJAX handler)
     */
    public function handle_delete_balance_sheet_schema() {
        require_once plugin_dir_path(__FILE__) . 'includes/class-balance-sheet-schemas-admin.php';
        $schemas_admin = new Balance_Sheet_Schemas_Admin();
        $schemas_admin->delete_schema();
    }

    /**
     * Handle get balance sheet schema (AJAX handler)
     */
    public function handle_get_balance_sheet_schema() {
        require_once plugin_dir_path(__FILE__) . 'includes/class-balance-sheet-schemas-admin.php';
        $schemas_admin = new Balance_Sheet_Schemas_Admin();
        $schemas_admin->get_schema();
    }

    /**
     * Export Balance Sheet Analysis to PDF
     */
    public function export_balance_sheet_analysis_pdf() {
        check_ajax_referer('balance_sheet_analysis_nonce', 'nonce');

        if (!$this->check_mpdf()) {
            wp_die(__('mPDF library not available.', 'document-viewer-plugin'));
        }

        $analysis_data = isset($_POST['analysis_data']) ? json_decode(stripslashes($_POST['analysis_data']), true) : null;

        if (!$analysis_data) {
            wp_die(__('No analysis data provided.', 'document-viewer-plugin'));
        }

        try {
            $mpdf = new \Mpdf\Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
                'margin_left' => 15,
                'margin_right' => 15,
                'margin_top' => 20,
                'margin_bottom' => 20,
                'margin_header' => 10,
                'margin_footer' => 10
            ]);

            // Set document properties
            $mpdf->SetTitle($analysis_data['title']);
            $mpdf->SetAuthor('Balance Sheet Analyzer');
            $mpdf->SetCreator('Financial Advisor Plugin');

            // CSS for styling
            $css = "
            body { font-family: Arial, sans-serif; font-size: 12pt; line-height: 1.6; }
            h1 { color: #2c3e50; font-size: 18pt; margin-bottom: 20px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
            h2 { color: #34495e; font-size: 16pt; margin-top: 20px; margin-bottom: 15px; }
            h3 { color: #34495e; font-size: 14pt; margin-top: 15px; margin-bottom: 10px; }
            .bsa-results-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
            .bsa-results-table th, .bsa-results-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .bsa-results-table th { background-color: #f2f2f2; font-weight: bold; }
            .meta-info { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            .meta-info strong { color: #2c3e50; }
            ";

            $mpdf->WriteHTML($css, \Mpdf\HTMLParserMode::HEADER_CSS);

            // HTML content
            $html = '<h1>' . esc_html($analysis_data['title']) . '</h1>';

            $html .= '<div class="meta-info">';
            $html .= '<p><strong>Schema:</strong> ' . esc_html($analysis_data['schema']) . '</p>';
            $html .= '<p><strong>Documento:</strong> ' . esc_html($analysis_data['document']) . '</p>';
            $html .= '<p><strong>Data Analisi:</strong> ' . date('d/m/Y H:i', strtotime($analysis_data['timestamp'])) . '</p>';
            $html .= '</div>';

            $html .= '<div class="analysis-content">' . $analysis_data['content'] . '</div>';

            $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);

            // Output PDF
            $filename = 'analisi-bilancio-' . date('Y-m-d-H-i-s') . '.pdf';
            $mpdf->Output($filename, \Mpdf\Output\Destination::DOWNLOAD);

        } catch (Exception $e) {
            dv_debug_log('PDF export error: ' . $e->getMessage());
            wp_die(__('Error generating PDF: ', 'document-viewer-plugin') . $e->getMessage());
        }
    }

    /**
     * Export Balance Sheet Analysis to Word (Simple text method)
     */
    public function export_balance_sheet_analysis_word() {
        check_ajax_referer('balance_sheet_analysis_nonce', 'nonce');

        $analysis_data = isset($_POST['analysis_data']) ? json_decode(stripslashes($_POST['analysis_data']), true) : null;

        if (!$analysis_data) {
            wp_die(__('No analysis data provided.', 'document-viewer-plugin'));
        }

        try {
            // Generate filename
            $filename = 'analisi-bilancio-' . date('Y-m-d-H-i-s') . '.doc';

            // Clean output buffer
            if (ob_get_level()) {
                ob_end_clean();
            }

            // Set headers for Word download (HTML format that Word can open)
            header('Content-Type: application/msword');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Cache-Control: max-age=0');
            header('Pragma: public');
            header('Expires: 0');

            // Create HTML content that Word can open
            $html_content = $this->generateWordHTMLContent($analysis_data);

            // Output the HTML content
            echo $html_content;
            exit;

        } catch (Exception $e) {
            dv_debug_log('Word export error: ' . $e->getMessage());
            wp_die(__('Error generating Word document: ', 'document-viewer-plugin') . $e->getMessage());
        }
    }

    /**
     * Generate enhanced HTML content that Word can open with proper formatting
     */
    private function generateWordHTMLContent($analysis_data) {
        // Preserve HTML formatting instead of stripping it
        $content = $analysis_data['content'];

        // Clean and enhance content for Word compatibility
        $content = $this->enhanceContentForWord($content);

        // Create HTML document that Word can open with enhanced styling
        $html = '<!DOCTYPE html>';
        $html .= '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">';
        $html .= '<head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<meta name="ProgId" content="Word.Document">';
        $html .= '<meta name="Generator" content="Microsoft Word">';
        $html .= '<meta name="Originator" content="Microsoft Word">';
        $html .= '<title>' . htmlspecialchars($analysis_data['title']) . '</title>';
        $html .= '<style>';
        $html .= $this->getWordCompatibleCSS();
        $html .= '</style>';
        $html .= '</head>';
        $html .= '<body>';

        // Title
        $html .= '<h1>' . htmlspecialchars($analysis_data['title']) . '</h1>';

        // Meta information
        $html .= '<div class="meta">';
        $html .= '<p><strong>Schema:</strong> ' . htmlspecialchars($analysis_data['schema']) . '</p>';
        $html .= '<p><strong>Documento:</strong> ' . htmlspecialchars($analysis_data['document']) . '</p>';
        $html .= '<p><strong>Data Analisi:</strong> ' . date('d/m/Y H:i', strtotime($analysis_data['timestamp'])) . '</p>';
        $html .= '</div>';

        // Content with preserved formatting
        $html .= '<div class="content">' . $content . '</div>';

        $html .= '</body>';
        $html .= '</html>';

        return $html;
    }

    /**
     * Enhance content for Word compatibility while preserving formatting
     */
    private function enhanceContentForWord($content) {
        // Convert mathematical formulas to Word-compatible format
        $content = $this->convertMathFormulasForWord($content);

        // Enhance table formatting for Word
        $content = $this->enhanceTablesForWord($content);

        // Clean up any problematic HTML while preserving structure
        $content = $this->cleanHtmlForWordCompatibility($content);

        return $content;
    }

    /**
     * Get Word-compatible CSS styles
     */
    private function getWordCompatibleCSS() {
        return '
            body {
                font-family: "Times New Roman", serif;
                font-size: 12pt;
                line-height: 1.5;
                margin: 1in;
                color: #000;
            }
            h1 {
                font-size: 18pt;
                font-weight: bold;
                margin-bottom: 20pt;
                color: #000;
            }
            .meta {
                font-size: 10pt;
                color: #666;
                margin-bottom: 20pt;
                border-bottom: 1pt solid #ccc;
                padding-bottom: 10pt;
            }
            p {
                margin-bottom: 12pt;
                text-align: justify;
            }
            strong {
                font-weight: bold;
                color: #000;
            }
            em {
                font-style: italic;
                color: #333;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin: 12pt 0;
                border: 1pt solid #000;
            }
            th, td {
                border: 1pt solid #000;
                padding: 6pt;
                text-align: left;
                vertical-align: top;
            }
            th {
                background-color: #f0f0f0;
                font-weight: bold;
            }
            .math-formula {
                background-color: #f8f9fa;
                border: 1pt solid #ccc;
                padding: 8pt;
                margin: 8pt 0;
                text-align: center;
                font-family: "Times New Roman", serif;
            }
            .fraction {
                display: inline-block;
                text-align: center;
                vertical-align: middle;
            }
            .numerator, .denominator {
                display: block;
                font-size: 11pt;
            }
            .fraction-line {
                border-top: 1pt solid #000;
                margin: 2pt 0;
            }
            .content {
                margin-top: 20pt;
            }
        ';
    }

    /**
     * Convert mathematical formulas to Word-compatible format
     */
    private function convertMathFormulasForWord($content) {
        // Convert math-formula divs to Word-compatible format
        $content = preg_replace_callback(
            '/<div class="math-formula">(.*?)<\/div>/s',
            function($matches) {
                $formula = $matches[1];
                // Convert fractions to Word-compatible format
                $formula = preg_replace(
                    '/<span class="fraction"><span class="numerator">(.*?)<\/span><span class="fraction-line">.*?<\/span><span class="denominator">(.*?)<\/span><\/span>/',
                    '<span class="fraction"><span class="numerator">$1</span><span class="fraction-line">___</span><span class="denominator">$2</span></span>',
                    $formula
                );
                return '<div class="math-formula">' . $formula . '</div>';
            },
            $content
        );

        // Convert inline math to simpler format
        $content = preg_replace(
            '/<span class="math-inline">(.*?)<\/span>/',
            '<em>$1</em>',
            $content
        );

        return $content;
    }

    /**
     * Enhance table formatting for Word
     */
    private function enhanceTablesForWord($content) {
        // Ensure tables have proper Word-compatible structure
        $content = preg_replace_callback(
            '/<table[^>]*>(.*?)<\/table>/s',
            function($matches) {
                $tableContent = $matches[1];

                // Ensure proper table structure
                if (strpos($tableContent, '<thead>') === false && strpos($tableContent, '<th>') !== false) {
                    // Wrap first row with th elements in thead
                    $tableContent = preg_replace(
                        '/(<tr[^>]*>.*?<\/th>.*?<\/tr>)/',
                        '<thead>$1</thead>',
                        $tableContent,
                        1
                    );
                }

                // Wrap remaining rows in tbody if not already wrapped
                if (strpos($tableContent, '<tbody>') === false) {
                    $tableContent = preg_replace(
                        '/(<\/thead>)(.*)/',
                        '$1<tbody>$2</tbody>',
                        $tableContent
                    );
                }

                return '<table>' . $tableContent . '</table>';
            },
            $content
        );

        return $content;
    }

    /**
     * Clean HTML for Word compatibility while preserving structure
     */
    private function cleanHtmlForWordCompatibility($content) {
        // Remove problematic CSS classes and attributes that Word doesn't understand
        $content = preg_replace('/class="[^"]*bsa-[^"]*"/', '', $content);

        // Convert div elements to p elements where appropriate
        $content = preg_replace('/<div([^>]*)>(.*?)<\/div>/s', '<p$1>$2</p>', $content);

        // Clean up multiple consecutive br tags
        $content = preg_replace('/<br\s*\/?>\s*<br\s*\/?>/i', '</p><p>', $content);

        // Ensure proper paragraph structure
        $content = preg_replace('/(<\/p>\s*)<p>/', '$1<p>', $content);

        // Remove empty paragraphs
        $content = preg_replace('/<p[^>]*>\s*<\/p>/', '', $content);

        // Convert remaining br tags to proper line breaks
        $content = preg_replace('/<br\s*\/?>/i', '<br/>', $content);

        return $content;
    }

    /**
     * Generate RTF content for Word export with proper UTF-8 encoding
     */
    private function generateRTFContent($analysis_data) {
        // Convert HTML content to plain text
        $content = $analysis_data['content'];
        $content = preg_replace('/<br\s*\/?>/i', "\n", $content);
        $content = preg_replace('/<\/p>/i', "\n\n", $content);
        $content = preg_replace('/<[^>]+>/', '', $content);
        $content = html_entity_decode($content, ENT_QUOTES, 'UTF-8');
        $content = trim($content);

        // Create RTF document with UTF-8 support
        $rtf = "{\rtf1\ansi\ansicpg1252\deff0\deflang1040 {\fonttbl {\f0\froman\fcharset0 Times New Roman;}}";
        $rtf .= "{\colortbl ;\red0\green0\blue0;}";

        // Title
        $rtf .= "\f0\fs28\b " . $this->escapeRTFUTF8($analysis_data['title']) . "\b0\par\par";

        // Meta information
        $rtf .= "\fs20 Schema: " . $this->escapeRTFUTF8($analysis_data['schema']) . "\par";
        $rtf .= "Documento: " . $this->escapeRTFUTF8($analysis_data['document']) . "\par";
        $rtf .= "Data Analisi: " . date('d/m/Y H:i', strtotime($analysis_data['timestamp'])) . "\par\par";

        // Content
        $rtf .= "\fs22 " . $this->escapeRTFUTF8($content) . "\par";

        $rtf .= "}";

        return $rtf;
    }

    /**
     * Escape text for RTF format with UTF-8 support
     */
    private function escapeRTFUTF8($text) {
        // Convert to Windows-1252 encoding for better RTF compatibility
        $text = mb_convert_encoding($text, 'Windows-1252', 'UTF-8');

        // Escape RTF special characters
        $text = str_replace('\\', '\\\\', $text);
        $text = str_replace('{', '\{', $text);
        $text = str_replace('}', '\}', $text);

        // Convert newlines to RTF paragraph breaks
        $text = str_replace("\n", '\par ', $text);

        // Handle special characters that might not convert properly
        $text = str_replace('€', '\u8364?', $text); // Euro symbol
        $text = str_replace('à', '\u224?', $text);
        $text = str_replace('è', '\u232?', $text);
        $text = str_replace('é', '\u233?', $text);
        $text = str_replace('ì', '\u236?', $text);
        $text = str_replace('ò', '\u242?', $text);
        $text = str_replace('ù', '\u249?', $text);

        return $text;
    }

    /**
     * Convert HTML content to Word format preserving structure
     */
    private function convertHtmlToWord($section, $htmlContent) {
        try {
            // Sanitize HTML content
            $content = $this->sanitizeHtmlForWord($htmlContent);

            // Handle tables first
            if (preg_match_all('/<table[^>]*>(.*?)<\/table>/s', $content, $tableMatches)) {
                foreach ($tableMatches[0] as $tableHtml) {
                    // Add table to Word
                    $this->addTableToWord($section, $tableHtml);
                    // Remove table from content
                    $content = str_replace($tableHtml, '[TABLE_PROCESSED]', $content);
                }
            }

            // Process remaining content
            $content = str_replace('[TABLE_PROCESSED]', '', $content);

            // Convert HTML to text preserving structure
            $content = preg_replace('/<br\s*\/?>/i', "\n", $content);
            $content = preg_replace('/<strong[^>]*>(.*?)<\/strong>/i', '$1', $content);
            $content = preg_replace('/<em[^>]*>(.*?)<\/em>/i', '$1', $content);
            $content = preg_replace('/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i', "\n$1\n", $content);

            // Remove remaining HTML tags
            $content = strip_tags($content);
            $content = html_entity_decode($content, ENT_QUOTES, 'UTF-8');

            // Clean up content
            $content = preg_replace('/\s+/', ' ', $content); // Multiple spaces to single
            $content = preg_replace('/\n\s*\n/', "\n", $content); // Multiple newlines to single

            // Split by lines and add to Word
            $lines = explode("\n", $content);
            $contentStyle = array('size' => 12);

            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    // Ensure line is not too long
                    if (strlen($line) > 1000) {
                        $line = substr($line, 0, 1000) . '...';
                    }
                    $section->addText($line, $contentStyle);
                } else {
                    $section->addTextBreak(1);
                }
            }
        } catch (Exception $e) {
            dv_debug_log('Error converting HTML to Word: ' . $e->getMessage());
            // Fallback: add simple text
            $fallbackText = strip_tags($htmlContent);
            $fallbackText = html_entity_decode($fallbackText, ENT_QUOTES, 'UTF-8');
            $section->addText($fallbackText, array('size' => 12));
        }
    }

    /**
     * Sanitize HTML content for Word processing
     */
    private function sanitizeHtmlForWord($htmlContent) {
        // Remove problematic elements
        $content = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $htmlContent);
        $content = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $content);
        $content = preg_replace('/<link[^>]*>/i', '', $content);

        // Remove div wrappers but preserve content
        $content = preg_replace('/<div[^>]*>(.*?)<\/div>/s', '$1', $content);

        // Clean up malformed HTML
        $content = preg_replace('/<([^>]+)>([^<]*)<\/\1>/', '<$1>$2</$1>', $content);

        return $content;
    }

    /**
     * Add HTML table to Word document
     */
    private function addTableToWord($section, $tableHtml) {
        // Extract table rows
        preg_match_all('/<tr[^>]*>(.*?)<\/tr>/s', $tableHtml, $rowMatches);

        if (empty($rowMatches[1])) {
            return;
        }

        $rows = $rowMatches[1];
        $tableData = array();

        foreach ($rows as $rowHtml) {
            // Extract cells (th or td)
            preg_match_all('/<t[hd][^>]*>(.*?)<\/t[hd]>/s', $rowHtml, $cellMatches);
            if (!empty($cellMatches[1])) {
                $rowData = array();
                foreach ($cellMatches[1] as $cellHtml) {
                    $cellText = strip_tags($cellHtml);
                    $cellText = html_entity_decode($cellText, ENT_QUOTES, 'UTF-8');
                    $rowData[] = trim($cellText);
                }
                $tableData[] = $rowData;
            }
        }

        if (!empty($tableData)) {
            // Calculate column count from first row
            $maxCols = 0;
            foreach ($tableData as $rowData) {
                $maxCols = max($maxCols, count($rowData));
            }

            if ($maxCols > 0) {
                $table = $section->addTable(array(
                    'borderSize' => 6,
                    'borderColor' => '999999',
                    'cellMargin' => 80
                ));

                // Calculate column width in twips (1/20th of a point)
                $totalWidth = 9000; // Total table width in twips
                $colWidth = intval($totalWidth / $maxCols);

                foreach ($tableData as $rowIndex => $rowData) {
                    $table->addRow();

                    // Ensure all rows have the same number of cells
                    for ($i = 0; $i < $maxCols; $i++) {
                        $cellData = isset($rowData[$i]) ? $rowData[$i] : '';
                        $cellData = trim($cellData);

                        // Ensure cell data is not empty
                        if (empty($cellData)) {
                            $cellData = ' '; // Add space for empty cells
                        }

                        $cellStyle = $rowIndex === 0 ? array('bold' => true, 'size' => 11) : array('size' => 10);

                        $cell = $table->addCell($colWidth);
                        $cell->addText($cellData, $cellStyle);
                    }
                }

                $section->addTextBreak(1);
            }
        }
    }

    // ...existing code...
}

new Document_Viewer_Plugin();

// Inizializza il plugin
$document_viewer_plugin = new Document_Viewer_Plugin();

/**
 * Initialize Widget Help System components in the correct order
 * This ensures proper loading and prevents circular dependencies
 */
function init_widget_help_system() {
    // 1. Run migration first if needed
    if (class_exists('Widget_Help_Migration')) {
        // Check if we need to run the migration
        $migration_status = get_option('widget_help_migration_status', array());
        if (empty($migration_status['completed'])) {
            // Run automatic migration for option keys
            Widget_Help_Migration::migrate_option_keys();
        }
    }
    
    // 2. Initialize Widget Help Manager (new system)
    if (class_exists('Widget_Help_Manager')) {
        Widget_Help_Manager::get_instance();
    }
    
    // 3. Widget Help System is already initialized globally
    // No additional action needed as it's handled in its constructor
}

// Hook the initialization to WordPress init
add_action('init', 'init_widget_help_system', 15);

/**
 * Get current user information regardless of user type (WordPress or external)
 *
 * @return array User information with keys: id, type, data
 */
function fa_get_current_user_info() {
    global $document_viewer_plugin;
    return $document_viewer_plugin->get_current_user_info();
}

/**
 * Update user statistics in a transactional manner
 *
 * @param int $user_id User ID
 * @param string $user_type User type (wordpress|subscriber)
 * @param array $stats_update Statistics to update
 * @return bool Success or failure
 */
function fa_update_user_stats_transaction($user_id, $user_type, $stats_update) {
    global $document_viewer_plugin;
    return $document_viewer_plugin->update_user_stats_transaction($user_id, $user_type, $stats_update);
}

// Initialize Document Stats
$document_stats = new Document_Stats();

// Initialize Document Stats
$document_stats = new Document_Stats();

// Initialize the Help Line functionality
require_once plugin_dir_path(__FILE__) . 'includes/class-help-line.php';
help_line();

// AGGIUNGI la registrazione dell'handler AJAX dopo la definizione della classe
add_action('wp_ajax_ocr_log', 'handle_ocr_log');

/**
 * Handle OCR log messages from JavaScript
 */
function handle_ocr_log() {
    check_ajax_referer('document_viewer_nonce', 'nonce');

    $message = isset($_POST['message']) ? sanitize_text_field($_POST['message']) : '';
    $context = isset($_POST['context']) ? sanitize_text_field($_POST['context']) : 'ocr';
    $extracted_text = isset($_POST['extracted_text']) ? $_POST['extracted_text'] : '';

    // Log the message
    dv_debug_log($message, $context);

    // If we have extracted text, save it to session for potential debugging
    if (!empty($extracted_text)) {
        if (!session_id() && !headers_sent()) {
            session_start();
        }
        $_SESSION['ocr_extracted_text'] = $extracted_text;
        dv_debug_log('Testo OCR salvato in sessione (' . strlen($extracted_text) . ' caratteri)', $context);
    }

    wp_send_json_success(['message' => 'Log saved']);
}




// Includi la classe per il caricamento degli script
require_once plugin_dir_path(__FILE__) . 'includes/class-enqueue-scripts.php';

/**
 * Invalidate user statistics cache
 *
 * @param int $user_id User ID
 * @param string $user_type User type (wordpress|subscriber)
 * @return bool Success or failure
 */
function fa_invalidate_user_stats_cache($user_id, $user_type) {
    // Per ora implementiamo una versione semplice che non utilizza una cache reale
    // In futuro, se viene implementata una cache, questo metodo dovrà essere aggiornato
    dv_debug_log("Invalidazione cache statistiche per utente: $user_id (tipo: $user_type)");
    return true;
}
