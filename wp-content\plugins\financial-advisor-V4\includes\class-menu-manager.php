<?php
/**
 * Menu Manager
 *
 * Gestione centralizzata di tutti i menu di WordPress per Financial Advisor
 *
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Financial_Advisor_Menu_Manager {
    /**
     * Istanza singleton
     * @var Financial_Advisor_Menu_Manager
     */
    private static $instance = null;

    /**
     * Slug principale del menu
     * @var string
     */
    public $main_menu_slug = 'document-viewer-settings';

    /**
     * Constructor
     */
    private function __construct() {
        // Definisci la costante per indicare che il Menu Manager è attivo
        if (!defined('FA_MENU_MANAGER_ACTIVE')) {
            define('FA_MENU_MANAGER_ACTIVE', true);
        }
        
        // Rimuovi i vecchi hook di menu prima di aggiungerne di nuovi
        remove_action('admin_menu', array('Document_Viewer_Settings', 'admin_menu'));
        remove_action('admin_menu', array('Financial_Academy_Manager', 'add_admin_menu'));

        // Aggiungi l'azione per il menu
        add_action('admin_menu', array($this, 'register_all_menus'));

        // Gestisci i redirect
        add_action('init', array($this, 'handle_menu_redirects'));

        // Load Office Add-in Security Tabs functionality
        $this->load_security_tabs();
    }

    /**
     * Load the Office Add-in Security Tabs functionality
     */
    private function load_security_tabs() {
        $security_tabs_file = plugin_dir_path(__FILE__) . 'class-office-addin-security-tabs.php';
        if (file_exists($security_tabs_file)) {
            require_once $security_tabs_file;
            // The class instantiates itself at the end of the file
        }
    }

    /**
     * Ottieni l'istanza singleton della classe
     *
     * @return Financial_Advisor_Menu_Manager
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * Registra tutti i menu e sottomenu del plugin
     */
    public function register_all_menus() {
        // 1. Menu principale Financial Advisor
        add_menu_page(
            __('Financial Advisor Settings', 'document-viewer-plugin'),
            __('Financial Advisor', 'document-viewer-plugin'),
            'manage_options',
            $this->main_menu_slug,
            array($this, 'render_settings_page'),
            'dashicons-media-document',
            10
        );

        // 2. Sottomenu Impostazioni (punta alla stessa pagina del menu principale)
        add_submenu_page(
            $this->main_menu_slug,
            __('Financial Advisor Settings', 'document-viewer-plugin'),
            __('Settings', 'document-viewer-plugin'),
            'manage_options',
            $this->main_menu_slug,
            array($this, 'render_settings_page')
        );

        // 3. Sottomenu Financial Academy
        add_submenu_page(
            $this->main_menu_slug,
            __('Financial Academy Questions', 'document-viewer-plugin'),
            __('Financial Academy', 'document-viewer-plugin'),
            'manage_options',
            'financial-academy-manager',
            array($this, 'render_academy_page')
        );

        // 4. Sottomenu Office Add-in
        add_submenu_page(
            $this->main_menu_slug,
            __('Office Add-in', 'document-viewer-plugin'),
            __('Office Add-in', 'document-viewer-plugin'),
            'manage_options',
            'office-addin-manager',
            array($this, 'render_office_addin_page')
        );

        // 5. Sottomenu Payment Gateway Configuration
        add_submenu_page(
            $this->main_menu_slug,
            __('Payment Gateway Configuration', 'document-viewer-plugin'),
            __('Payment Gateways', 'document-viewer-plugin'),
            'manage_options',
            'payment-gateway-config',
            array($this, 'render_payment_gateway_page')
        );

        // 6. Sottomenu Help System Widget (parent corretto)
        add_submenu_page(
            $this->main_menu_slug, // deve essere il menu principale centralizzato
            __('Widget Help System', 'document-viewer-plugin'),
            __('Help System', 'document-viewer-plugin'),
            'manage_options',
            'widget-help-admin', // slug unico per il sottomenu
            array($this, 'render_widget_help_admin_page')
        );

        // 7. Sottomenu Schemi di Bilancio
        add_submenu_page(
            $this->main_menu_slug,
            __('Schemi di Bilancio', 'document-viewer-plugin'),
            __('Schemi di Bilancio', 'document-viewer-plugin'),
            'manage_options',
            'balance-sheet-schemas',
            array($this, 'render_balance_sheet_schemas_page')
        );

        // 8. Sottomenu What-If Query Management
        add_submenu_page(
            $this->main_menu_slug,
            __('What-If Query Management', 'document-viewer-plugin'),
            __('What-If Query Management', 'document-viewer-plugin'),
            'manage_options',
            'whatif-query-management',
            array($this, 'render_whatif_queries_page')
        );
        // Note: Gateway Testing is now integrated within the Payment Gateway Configuration page
    }
    /**
     * Renderizza la pagina di amministrazione del sistema di help widget
     */
    public function render_widget_help_admin_page() {
        // Usa il sistema di controllo accessi FA per verificare l'accesso admin
        if (!apply_filters('fa_user_can_access', false, 'admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'document-viewer-plugin'));
        }

        // Ottieni l'istanza del sistema help tramite la funzione helper
        $widget_help_system = widget_help_system();
        
        // Verifica che il sistema help sia disponibile
        if (!$widget_help_system || !class_exists('Widget_Help_System')) {
            echo '<div class="wrap">';
            echo '<h1>' . __('Widget Help System Error', 'document-viewer-plugin') . '</h1>';
            echo '<p>' . __('Il sistema di help non è stato inizializzato correttamente. Verifica che il file class-widget-help-system.php sia caricato.', 'document-viewer-plugin') . '</p>';
            echo '<div class="notice notice-info"><p><strong>Debug info:</strong><br>';
            echo 'Function widget_help_system() exists: ' . (function_exists('widget_help_system') ? 'Yes' : 'No') . '<br>';
            echo 'Class Widget_Help_System exists: ' . (class_exists('Widget_Help_System') ? 'Yes' : 'No') . '<br>';
            echo 'Widget Help System instance: ' . ($widget_help_system ? 'Available' : 'Not available') . '<br>';
            echo '</p></div>';
            echo '</div>';
            return;
        }

        // Use the new simplified admin interface
        if (class_exists('Widget_Help_Admin_Simple')) {
            // Pass the help system instance to the simplified admin
            $help_admin = new Widget_Help_Admin_Simple($widget_help_system);
            if (method_exists($help_admin, 'admin_page')) {
                // Debug: verify available widgets
                $widgets = $widget_help_system->get_all_widgets();
                if (DOCUMENT_VIEWER_DEBUG) {
                    error_log('Widget Help System Debug: Found ' . count($widgets) . ' widgets');
                    error_log('Widgets: ' . print_r(array_keys($widgets), true));
                }
                
                $help_admin->admin_page();
                return;
            }
        }
        
        // Fallback to old admin if new one is not available
        if (class_exists('Widget_Help_Admin')) {
            $help_admin = new Widget_Help_Admin($widget_help_system);
            if (method_exists($help_admin, 'admin_page')) {
                $help_admin->admin_page();
                return;
            }
        }

        // Fallback se la classe non è disponibile
        echo '<div class="wrap">';
        echo '<h1>' . __('Widget Help System', 'document-viewer-plugin') . '</h1>';
        echo '<p>' . __('La pagina di amministrazione del sistema di help non è disponibile. Assicurati che la classe Widget_Help_Admin sia caricata.', 'document-viewer-plugin') . '</p>';
        echo '</div>';
    }
    /**
     * Gestisce i reindirizzamenti per gli URL diretti
     */
    public function handle_menu_redirects() {
        // Verifica se l'URL corrente è /wp-admin/financial-academy-manager (accesso diretto)
        $current_url = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';

        // Solo se l'URL è diretto, reindirizza alla pagina appropriata
        // Per ora non fa niente, poiché vogliamo che /wp-admin/financial-academy-manager funzioni normalmente

        // Il problema precedente era causato dal tentativo di reindirizzare tutti gli accessi a financial-academy-manager
        // Ora gestiamo correttamente i sottomenu attraverso la registrazione centralizzata
    }

    /**
     * Renderizza la pagina delle impostazioni
     *
     * Delega la visualizzazione alla classe Document_Viewer_Settings
     */
    public function render_settings_page() {
        // Usa il sistema di controllo accessi FA per verificare l'accesso admin
        if (!apply_filters('fa_user_can_access', false, 'admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // Se esiste la classe Document_Viewer_Settings e ha il metodo settings_page
        if (class_exists('Document_Viewer_Settings')) {
            $settings = new Document_Viewer_Settings();
            if (method_exists($settings, 'settings_page')) {
                $settings->settings_page();
                return;
            }
        }

        // Fallback se la classe Document_Viewer_Settings non è disponibile
        echo '<div class="wrap">';
        echo '<h1>' . __('Financial Advisor Settings', 'document-viewer-plugin') . '</h1>';
        echo '<p>' . __('Settings page content not available.', 'document-viewer-plugin') . '</p>';
        echo '</div>';
    }

    /**
     * Renderizza la pagina Financial Academy
     *
     * Delega la visualizzazione alla classe Financial_Academy_Manager
     */
    public function render_academy_page() {
        // Usa il sistema di controllo accessi FA per verificare l'accesso admin
        if (!apply_filters('fa_user_can_access', false, 'admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // Se esiste la classe Financial_Academy_Manager e ha il metodo render_admin_page
        if (class_exists('Financial_Academy_Manager')) {
            $academy_manager = financial_academy_manager();
            if (method_exists($academy_manager, 'render_admin_page')) {
                $academy_manager->render_admin_page();
                return;
            }
        }

        // Fallback se la classe Financial_Academy_Manager non è disponibile
        echo '<div class="wrap">';
        echo '<h1>' . __('Financial Academy', 'document-viewer-plugin') . '</h1>';
        echo '<p>' . __('Financial Academy page content not available.', 'document-viewer-plugin') . '</p>';
        echo '</div>';
    }

    /**
     * Renderizza la pagina Schemi di Bilancio
     *
     * Gestisce la visualizzazione e la gestione degli schemi di riclassificazione bilancio
     */
    public function render_balance_sheet_schemas_page() {
        // Usa il sistema di controllo accessi FA per verificare l'accesso admin
        if (!apply_filters('fa_user_can_access', false, 'admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'document-viewer-plugin'));
        }

        // Include la classe per la gestione degli schemi di bilancio
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-balance-sheet-schemas-admin.php';

        // Se esiste la classe Balance_Sheet_Schemas_Admin
        if (class_exists('Balance_Sheet_Schemas_Admin')) {
            $schemas_admin = new Balance_Sheet_Schemas_Admin();
            if (method_exists($schemas_admin, 'render_admin_page')) {
                $schemas_admin->render_admin_page();
                return;
            }
        }

        // Fallback se la classe non è disponibile
        echo '<div class="wrap">';
        echo '<h1>' . __('Schemi di Bilancio', 'document-viewer-plugin') . '</h1>';
        echo '<p>' . __('La pagina di gestione degli schemi di bilancio non è disponibile.', 'document-viewer-plugin') . '</p>';
        echo '</div>';
    }

    /**
     * Renderizza la pagina What-If Query Management
     *
     * Gestisce la visualizzazione e la gestione delle query what-if per il Planning & Simulation widget
     */
    public function render_whatif_queries_page() {
        // Usa il sistema di controllo accessi FA per verificare l'accesso admin
        if (!apply_filters('fa_user_can_access', false, 'admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'document-viewer-plugin'));
        }

        // Include la classe per la gestione delle query what-if
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-whatif-queries-admin.php';

        // Se esiste la classe WhatIf_Queries_Admin
        if (class_exists('WhatIf_Queries_Admin')) {
            $whatif_admin = WhatIf_Queries_Admin::get_instance();
            if (method_exists($whatif_admin, 'render_admin_page')) {
                $whatif_admin->render_admin_page();
                return;
            }
        }

        // Fallback se la classe non è disponibile
        echo '<div class="wrap">';
        echo '<h1>' . __('What-If Query Management', 'document-viewer-plugin') . '</h1>';
        echo '<p>' . __('La pagina di gestione delle query what-if non è disponibile.', 'document-viewer-plugin') . '</p>';
        echo '</div>';
    }

    /**
     * Renderizza la pagina Office Add-in
     *
     * Gestisce la visualizzazione e la configurazione dell'Office Add-in
     */
    public function render_office_addin_page() {
        // Usa il sistema di controllo accessi FA per verificare l'accesso admin
        if (!apply_filters('fa_user_can_access', false, 'admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'document-viewer-plugin'));
        }

        // Carica gli script e gli stili necessari per la pagina
        wp_enqueue_script('office-addin-preview-script');
        wp_enqueue_style('office-addin-preview-style');

        // Localizza lo script con i dati necessari per AJAX
        wp_localize_script(
            'office-addin-preview-script',
            'office_addin_preview_params',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('office_addin_general'),
                'settings_nonce' => wp_create_nonce('office_addin_get_settings'),
                'queries_nonce' => wp_create_nonce('office_addin_get_queries')
            )
        );

        // Carica gli script e gli stili per l'editor avanzato
        wp_enqueue_editor();
        wp_enqueue_media();

        // Carica gli script e gli stili per il color picker
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');

        // Carica gli script e gli stili per l'editor avanzato dell'Office Add-in
        wp_enqueue_script('office-addin-editor-script');
        wp_enqueue_style('office-addin-editor-style');

        // Salva le impostazioni se il form è stato inviato
        if (isset($_POST['office_addin_content']) && check_admin_referer('office_addin_settings', 'office_addin_nonce')) {
            // Salva il contenuto HTML dell'add-in senza filtrare il contenuto
            // Utilizziamo stripslashes per rimuovere eventuali caratteri di escape aggiunti automaticamente
            $content = stripslashes($_POST['office_addin_content']);
            update_option('office_addin_content', $content);

            // Salva il CSS dell'add-in
            if (isset($_POST['office_addin_css'])) {
                $css = stripslashes($_POST['office_addin_css']);
                update_option('office_addin_css', $css);
            }

            // Salva l'URL dell'add-in
            if (isset($_POST['office_addin_url'])) {
                update_option('office_addin_url', esc_url_raw($_POST['office_addin_url']));
            }

            echo '<div class="notice notice-success is-dismissible"><p>' . __('Office Add-in settings saved.', 'document-viewer-plugin') . '</p></div>';
        }

        // Ottieni il contenuto HTML dell'add-in
        $office_addin_content = get_option('office_addin_content');

        // Se il contenuto è vuoto, carica il contenuto predefinito
        if (empty($office_addin_content)) {
            // Prova a ottenere il contenuto predefinito dalla classe Document_Viewer_Settings
            if (class_exists('Document_Viewer_Settings')) {
                $settings = new Document_Viewer_Settings();
                if (method_exists($settings, 'get_default_office_addin_content')) {
                    try {
                        $office_addin_content = $settings->get_default_office_addin_content();
                    } catch (Exception $e) {
                        // In caso di errore, usa un contenuto HTML di base
                        $office_addin_content = $this->get_fallback_office_addin_content();
                    }
                } else {
                    // Se il metodo non esiste, usa un contenuto HTML di base
                    $office_addin_content = $this->get_fallback_office_addin_content();
                }
            } else {
                // Se la classe non esiste, usa un contenuto HTML di base
                $office_addin_content = $this->get_fallback_office_addin_content();
            }
        }

        // Renderizza la pagina
        ?>
        <div class="wrap">
            <h1><?php _e('Office Add-in Manager', 'document-viewer-plugin'); ?></h1>

            <div class="office-addin-description">
                <p><?php _e('Configure the HTML content for the Excel add-in interface. The add-in will automatically use the API settings from the Primary API tab.', 'document-viewer-plugin'); ?></p>
            </div>

            <div class="office-addin-tabs">
                <div class="nav-tab-wrapper">
                    <a href="#content-tab" class="nav-tab nav-tab-active"><?php _e('Content', 'document-viewer-plugin'); ?></a>
                    <a href="#preview-tab" class="nav-tab"><?php _e('Live Preview', 'document-viewer-plugin'); ?></a>
                    <a href="#manifest-tab" class="nav-tab"><?php _e('Manifest', 'document-viewer-plugin'); ?></a>
                    <a href="#documentation-tab" class="nav-tab"><?php _e('Documentation', 'document-viewer-plugin'); ?></a>
                    <a href="#security-overview-tab" class="nav-tab"><?php _e('Security Overview', 'document-viewer-plugin'); ?></a>
                    <a href="#security-protection-tab" class="nav-tab"><?php _e('Security Protection', 'document-viewer-plugin'); ?></a>
                    <a href="#security-monitoring-tab" class="nav-tab"><?php _e('Security Monitoring', 'document-viewer-plugin'); ?></a>
                    <a href="#security-performance-tab" class="nav-tab"><?php _e('Security Performance', 'document-viewer-plugin'); ?></a>
                </div>

                <div id="content-tab" class="tab-content active">
                    <form method="post" action="" id="office-addin-form">
                        <?php wp_nonce_field('office_addin_settings', 'office_addin_nonce'); ?>

                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('Add-in URL', 'document-viewer-plugin'); ?></th>
                                <td>
                                    <?php
                                    $addin_url = get_option('office_addin_url', trailingslashit(get_site_url()) . 'office-addin/');
                                    ?>
                                    <input type="url" name="office_addin_url" id="office_addin_url" value="<?php echo esc_url($addin_url); ?>" class="regular-text">
                                    <p class="description"><?php _e('The URL where the Excel add-in will be accessible. This should be a publicly accessible URL.', 'document-viewer-plugin'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php _e('Add-in HTML Content', 'document-viewer-plugin'); ?></th>
                                <td>
                                    <?php
                                    // Configurazione dell'editor avanzato
                                    $editor_settings = array(
                                        'media_buttons' => true, // Mostra il pulsante per caricare media
                                        'textarea_name' => 'office_addin_content',
                                        'textarea_rows' => 20,
                                        'teeny'         => false, // Usa la versione completa dell'editor
                                        'tinymce'       => array(
                                            'plugins'                      => 'textcolor,colorpicker,hr,lists,media,paste,tabfocus,wplink,wordpress,wpeditimage,wpgallery,wpview',
                                            'toolbar1'                     => 'formatselect,bold,italic,underline,strikethrough,forecolor,backcolor,|,bullist,numlist,|,blockquote,hr,|,alignleft,aligncenter,alignright,|,link,unlink,|,wp_adv',
                                            'toolbar2'                     => 'pastetext,removeformat,charmap,outdent,indent,|,undo,redo,|,wp_help',
                                            'content_css'                  => plugin_dir_url(dirname(__FILE__)) . 'assets/css/office-addin-editor.css'
                                        ),
                                        'quicktags'     => array(
                                            'buttons' => 'strong,em,link,block,del,ins,img,ul,ol,li,code,more,close'
                                        ),
                                        'editor_css'    => '<style>.wp-editor-area { height: 400px !important; }</style>'
                                    );

                                    // Usa l'editor avanzato di WordPress
                                    if (function_exists('wp_editor')) {
                                        wp_editor($office_addin_content, 'office_addin_content_editor', $editor_settings);
                                    } else {
                                        // Fallback a un semplice textarea se wp_editor non è disponibile
                                        ?>
                                        <textarea name="office_addin_content" id="office_addin_content" rows="20" cols="50" class="large-text"><?php echo esc_textarea($office_addin_content); ?></textarea>
                                        <?php
                                    }
                                    ?>

                                    <div class="addin-editor-tools">
                                        <h4><?php _e('Editor Tools', 'document-viewer-plugin'); ?></h4>
                                        <div class="addin-editor-color-tools">
                                            <label for="addin-custom-color"><?php _e('Custom Color:', 'document-viewer-plugin'); ?></label>
                                            <input type="text" id="addin-custom-color" class="color-picker" data-default-color="#3498db" />
                                            <button type="button" id="apply-text-color" class="button"><?php _e('Apply to Text', 'document-viewer-plugin'); ?></button>
                                            <button type="button" id="apply-bg-color" class="button"><?php _e('Apply to Background', 'document-viewer-plugin'); ?></button>
                                        </div>

                                        <div class="addin-editor-template-tools">
                                            <label for="addin-insert-template"><?php _e('Insert Template Element:', 'document-viewer-plugin'); ?></label>
                                            <select id="addin-insert-template" class="regular-text">
                                                <option value=""><?php _e('-- Select Element --', 'document-viewer-plugin'); ?></option>
                                                <option value="button"><?php _e('Button', 'document-viewer-plugin'); ?></option>
                                                <option value="section"><?php _e('Section', 'document-viewer-plugin'); ?></option>
                                                <option value="form-field"><?php _e('Form Field', 'document-viewer-plugin'); ?></option>
                                                <option value="result-container"><?php _e('Result Container', 'document-viewer-plugin'); ?></option>
                                            </select>
                                            <button type="button" id="insert-template-element" class="button"><?php _e('Insert', 'document-viewer-plugin'); ?></button>
                                        </div>
                                    </div>

                                    <p class="description">
                                        <?php _e('This HTML content will be used for the Excel add-in interface. Use the advanced editor to format text, add colors, and insert media.', 'document-viewer-plugin'); ?>
                                    </p>
                                </td>
                            </tr>
                            <?php
                            // Ottieni il CSS dell'add-in (nascosto all'utente ma salvato nel database)
                            $office_addin_css = get_option('office_addin_css', '');
                            if (empty($office_addin_css)) {
                                // Se non esiste, carica il CSS predefinito
                                if (class_exists('Document_Viewer_Settings')) {
                                    $settings = new Document_Viewer_Settings();
                                    if (method_exists($settings, 'get_default_office_addin_css')) {
                                        $office_addin_css = $settings->get_default_office_addin_css();
                                    } else {
                                        // CSS standard per Excel add-in
                                        $office_addin_css = '/* CSS standard per Excel add-in - Non modificare */
/* Questi stili sono necessari per il corretto funzionamento dell\'add-in */
.excel-addin-container {
    font-family: "Segoe UI", Arial, sans-serif;
    padding: 10px;
    width: 320px;
    margin: 0 auto;
    box-sizing: border-box;
}
.section {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
}
h2 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    font-size: 18px;
}
h3 {
    color: #3498db;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
}
.form-group {
    margin-bottom: 10px;
}
label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}
.primary-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}
.secondary-button {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
}';
                                    }
                                } else {
                                    // CSS standard per Excel add-in
                                    $office_addin_css = '/* CSS standard per Excel add-in - Non modificare */
/* Questi stili sono necessari per il corretto funzionamento dell\'add-in */
.excel-addin-container {
    font-family: "Segoe UI", Arial, sans-serif;
    padding: 10px;
    width: 320px;
    margin: 0 auto;
    box-sizing: border-box;
}
.section {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
}
h2 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    font-size: 18px;
}
h3 {
    color: #3498db;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
}
.form-group {
    margin-bottom: 10px;
}
label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}
.primary-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}
.secondary-button {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
}';
                                }
                            }
                            // Salva il CSS in un campo nascosto per mantenerlo disponibile per l'anteprima
                            ?>
                            <input type="hidden" name="office_addin_css" id="office_addin_css" value="<?php echo esc_attr($office_addin_css); ?>" />
                        </table>

                        <p class="submit">
                            <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php _e('Save Changes', 'document-viewer-plugin'); ?>">
                            <button type="button" id="preview-changes" class="button button-secondary"><?php _e('Preview Changes', 'document-viewer-plugin'); ?></button>
                        </p>
                    </form>
                </div>

                <div id="preview-tab" class="tab-content">
                    <h2><?php _e('Live Preview', 'document-viewer-plugin'); ?></h2>
                    <p><?php _e('This is a live preview of how your Excel add-in will look and function. You can test the UI components here before saving.', 'document-viewer-plugin'); ?></p>

                    <!-- Side-by-Side Layout: Excel Grid + Office Add-in Panel -->
                    <div class="preview-layout-container">
                        <!-- Left Side: Excel-like Grid -->
                        <div class="excel-grid-side">
                            <div class="excel-grid-external-container">
                                <h3><?php _e('Excel Data Grid (Test Environment)', 'document-viewer-plugin'); ?></h3>
                                <p><?php _e('This grid simulates Excel data for testing. Select cells and extract text exactly as you would in Excel.', 'document-viewer-plugin'); ?></p>
                                <div id="external-excel-grid-container">
                                    <!-- Grid will be inserted here by JavaScript -->
                                </div>
                                <div class="grid-actions">
                                    <button type="button" id="sync-with-addin" class="button button-primary">
                                        <?php _e('🔄 Sync with Add-in', 'document-viewer-plugin'); ?>
                                    </button>
                                    <div id="extraction-results" class="extraction-results" style="display: none;">
                                        <h4><?php _e('Extracted Text:', 'document-viewer-plugin'); ?></h4>
                                        <div id="extracted-text-display" class="extracted-text-display"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Side: Office Add-in Panel -->
                        <div class="addin-panel-side">
                            <div class="addin-preview-container">
                        <div class="addin-preview-header">
                            <span class="addin-preview-title"><?php _e('Excel Add-in Preview', 'document-viewer-plugin'); ?></span>
                            <div class="addin-preview-controls">
                                <button type="button" id="refresh-preview" class="button button-small" title="<?php _e('Refresh Preview', 'document-viewer-plugin'); ?>"><span class="dashicons dashicons-update"></span></button>
                                <button type="button" id="toggle-preview-size" class="button button-small" title="<?php _e('Toggle Size', 'document-viewer-plugin'); ?>"><span class="dashicons dashicons-editor-expand"></span></button>
                            </div>
                        </div>
                        <div class="addin-preview-frame-container">
                            <div class="addin-preview-loading" id="addin-preview-loading">
                                <div class="spinner is-active"></div>
                                <p>Loading preview...</p>
                            </div>
                            <iframe id="addin-preview-frame" src="about:blank" frameborder="0"></iframe>
                        </div>
                            </div>
                        </div>
                    </div>

                    <div class="addin-preview-info">
                        <h3><?php _e('Real Testing Environment', 'document-viewer-plugin'); ?></h3>
                        <ul>
                            <li><?php _e('🔄 <strong>Side-by-Side Testing:</strong> Excel grid on the left, add-in panel on the right - exactly like in production.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('📊 <strong>Real Data Extraction:</strong> Select cells in the grid and sync with the add-in to test actual text extraction.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('🚀 <strong>Live API Calls:</strong> Analysis requests use your actual API settings and return real results.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('⚡ <strong>Production Behavior:</strong> This environment mirrors exactly what happens in Excel with the real add-in.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('🔧 <strong>Content Updates:</strong> Changes made in the Content tab will be reflected here after clicking "Preview Changes".', 'document-viewer-plugin'); ?></li>
                        </ul>
                    </div>

                    <!-- Nota: La funzionalità di anteprima è gestita dal file office-addin-preview.js -->
                    <!-- Script di preview caricato solo tramite wp_enqueue_script -->
                        function initializePreview() {
                            console.log('Initializing preview...');

                            // Verifica che l'iframe esista
                            var previewFrame = document.getElementById('addin-preview-frame');
                            if (!previewFrame) {
                                console.error('Preview frame not found');
                                return;
                            }

                            // Verifica che la funzione updatePreview sia disponibile
                            if (typeof window.updatePreview !== 'function') {
                                console.error('updatePreview function not available');
                                showErrorMessage('La funzione updatePreview non è disponibile. Controlla la console per errori.');
                                return;
                            }

                            // Aggiorna l'anteprima
                            console.log('Calling updatePreview...');
                            try {
                                window.updatePreview();
                            } catch (error) {
                                console.error('Error calling updatePreview:', error);
                                showErrorMessage('Errore durante l\'aggiornamento dell\'anteprima: ' + error.message);
                            }
                        }

                        // Carica lo script e inizializza l'anteprima
                        loadPreviewScript()
                            .then(function() {
                                // Attendi un po' per assicurarti che lo script sia completamente inizializzato
                                setTimeout(function() {
                                    if ($('#preview-tab').hasClass('active') || $('#preview-tab').is(':visible')) {
                                        initializePreview();
                                        // Initialize external grid
                                        if (typeof window.initializeExternalGrid === 'function') {
                                            window.initializeExternalGrid();
                                        }
                                    }

                                    // We no longer need to bind tab events here since we have a centralized handler
                                    // The main tab event handler is defined in the jQuery document ready function below
                                }, 300);
                            })
                            .catch(function(error) {
                                console.error('Failed to initialize preview:', error);
                                showErrorMessage('Impossibile inizializzare l\'anteprima: ' + error.message);
                            });
                    });
                    </script>
                </div>

                <div id="manifest-tab" class="tab-content">
                    <h2><?php _e('Add-in Manifest', 'document-viewer-plugin'); ?></h2>
                    <p><?php _e('Download the manifest file to install the Excel add-in.', 'document-viewer-plugin'); ?></p>

                    <p>
                        <a href="<?php echo esc_url(admin_url('admin-ajax.php?action=download_office_addin_manifest')); ?>" class="button button-primary">
                            <?php _e('Download Manifest', 'document-viewer-plugin'); ?>
                        </a>
                    </p>

                    <div class="manifest-instructions">
                        <h3><?php _e('Installation Instructions', 'document-viewer-plugin'); ?></h3>
                        <ol>
                            <li><?php _e('Download the manifest file using the button above.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('Open Excel and go to the Insert tab.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('Click on "Get Add-ins" or "Office Add-ins".', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('Select "Upload My Add-in" and browse to the downloaded manifest file.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('Click "Install" to complete the installation.', 'document-viewer-plugin'); ?></li>
                        </ol>
                    </div>
                </div>

                <div id="documentation-tab" class="tab-content">
                    <h2><?php _e('Documentation', 'document-viewer-plugin'); ?></h2>

                    <?php
                    // Carica la documentazione dal file Markdown
                    $documentation_file = plugin_dir_path(dirname(__FILE__)) . 'office-addin-documentation.md';
                    if (file_exists($documentation_file)) {
                        $documentation = file_get_contents($documentation_file);

                        // Converti Markdown in HTML (semplice)
                        $documentation = nl2br(esc_html($documentation));
                        $documentation = preg_replace('/^# (.*?)$/m', '<h2>$1</h2>', $documentation);
                        $documentation = preg_replace('/^## (.*?)$/m', '<h3>$1</h3>', $documentation);
                        $documentation = preg_replace('/^### (.*?)$/m', '<h4>$1</h4>', $documentation);
                        $documentation = preg_replace('/\*\*(.*?)\*\*/m', '<strong>$1</strong>', $documentation);
                        $documentation = preg_replace('/\*(.*?)\*/m', '<em>$1</em>', $documentation);

                        echo '<div class="office-addin-documentation">';
                        echo $documentation;
                        echo '</div>';
                    } else {
                        echo '<p>' . __('Documentation not available.', 'document-viewer-plugin') . '</p>';
                    }
                    ?>
                </div>

                <div id="security-overview-tab" class="tab-content">
                    <h2><?php _e('Security Overview', 'document-viewer-plugin'); ?></h2>
                    <p class="description"><?php _e('Comprehensive security status and vulnerability assessment for the Office Add-in.', 'document-viewer-plugin'); ?></p>
                    
                    <div id="security-overview-content" class="security-content-container">
                        <div class="security-loading">
                            <div class="spinner is-active"></div>
                            <p><?php _e('Loading security overview...', 'document-viewer-plugin'); ?></p>
                        </div>
                    </div>
                </div>

                <div id="security-protection-tab" class="tab-content">
                    <h2><?php _e('Security Protection', 'document-viewer-plugin'); ?></h2>
                    <p class="description"><?php _e('Active security measures and protection status for the Office Add-in.', 'document-viewer-plugin'); ?></p>
                    
                    <div id="security-protection-content" class="security-content-container">
                        <div class="security-loading">
                            <div class="spinner is-active"></div>
                            <p><?php _e('Loading protection status...', 'document-viewer-plugin'); ?></p>
                        </div>
                    </div>
                </div>

                <div id="security-monitoring-tab" class="tab-content">
                    <h2><?php _e('Security Monitoring', 'document-viewer-plugin'); ?></h2>
                    <p class="description"><?php _e('Real-time security monitoring and threat detection for the Office Add-in.', 'document-viewer-plugin'); ?></p>
                    
                    <div id="security-monitoring-content" class="security-content-container">
                        <div class="security-loading">
                            <div class="spinner is-active"></div>
                            <p><?php _e('Loading monitoring data...', 'document-viewer-plugin'); ?></p>
                        </div>
                    </div>
                </div>

                <div id="security-performance-tab" class="tab-content">
                    <h2><?php _e('Security Performance', 'document-viewer-plugin'); ?></h2>
                    <p class="description"><?php _e('Security system performance metrics and optimization for the Office Add-in.', 'document-viewer-plugin'); ?></p>
                    
                    <div id="security-performance-content" class="security-content-container">
                        <div class="security-loading">
                            <div class="spinner is-active"></div>
                            <p><?php _e('Loading performance metrics...', 'document-viewer-plugin'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    console.log('🚀 Office Add-in tabs initialization started');
                    
                    // Namespace specifico per evitare conflitti con altri tab
                    var officeAddinTabsSelector = '.office-addin-tabs';
                    
                    // Funzione per cambiare tab
                    function switchOfficeAddinTab(targetTab) {
                        console.log('🔄 Switching to Office Add-in tab:', targetTab);
                        
                        // Usa selettori specifici per evitare conflitti
                        var $tabContainer = $(officeAddinTabsSelector);
                        
                        // Hide all tab content SOLO all'interno del container office-addin-tabs
                        $tabContainer.find('.tab-content').removeClass('active').hide();

                        // Remove active class from all tabs SOLO all'interno del container office-addin-tabs
                        $tabContainer.find('.nav-tab').removeClass('nav-tab-active');

                        // Add active class to clicked tab SOLO all'interno del container office-addin-tabs
                        $tabContainer.find('.nav-tab[href="' + targetTab + '"]').addClass('nav-tab-active');

                        // Show the corresponding tab content SOLO all'interno del container office-addin-tabs
                        $tabContainer.find(targetTab).addClass('active').show();

                        // Update URL hash without causing page refresh (solo se necessario)
                        if (targetTab.startsWith('#')) {
                            var currentHash = window.location.hash;
                            if (currentHash !== targetTab) {
                                if (history.replaceState) {
                                    // Usa replaceState per evitare di aggiungere alla cronologia del browser
                                    history.replaceState(null, null, targetTab);
                                }
                            }
                        }

                        // If we're on the preview tab, ensure preview is initialized
                        if (targetTab === '#preview-tab') {
                            console.log('📋 Preview tab activated, initializing preview');
                            setTimeout(function() {
                                if (typeof initializePreview === 'function') {
                                    initializePreview();
                                } else if (typeof window.updatePreview === 'function') {
                                    window.updatePreview();
                                }

                                // Initialize external grid
                                if (typeof window.initializeExternalGrid === 'function') {
                                    window.initializeExternalGrid();
                                }
                            }, 300);
                        }
                        
                        console.log('✅ Tab switched successfully to:', targetTab);
                    }
                    
                    // Tab navigation con selettori specifici per evitare conflitti
                    $(officeAddinTabsSelector + ' .nav-tab').off('click.officetabs').on('click.officetabs', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        var targetTab = $(this).attr('href');
                        console.log('🖱️ Office Add-in tab clicked:', targetTab);
                        
                        if (targetTab && targetTab.startsWith('#')) {
                            switchOfficeAddinTab(targetTab);
                        }
                        
                        return false;
                    });

                    // Inizializza il tab corretto al caricamento della pagina
                    function initializeTabs() {
                        var $tabContainer = $(officeAddinTabsSelector);
                        var hash = window.location.hash;
                        var validTabs = ['#content-tab', '#preview-tab', '#manifest-tab', '#documentation-tab', '#security-overview-tab', '#security-protection-tab', '#security-monitoring-tab', '#security-performance-tab'];
                        
                        if (hash && validTabs.includes(hash) && $tabContainer.find('.nav-tab[href="' + hash + '"]').length) {
                            console.log('📍 Initializing Office Add-in with hash:', hash);
                            switchOfficeAddinTab(hash);
                        } else {
                            // Activate the first tab by default
                            var firstTab = $tabContainer.find('.nav-tab').first().attr('href');
                            console.log('📍 Initializing Office Add-in with first tab:', firstTab);
                            if (firstTab) {
                                switchOfficeAddinTab(firstTab);
                            }
                        }
                    }

                    // Security tab functionality
                    function loadSecurityContent(tabId) {
                        console.log('🔒 Loading security content for tab:', tabId);
                        
                        // Convert tab ID to content container ID
                        // Example: #security-overview-tab -> #security-overview-content
                        var contentContainerId = tabId.replace('-tab', '-content');
                        var contentContainer = $(contentContainerId);
                        
                        if (!contentContainer.length) {
                            console.error('Security content container not found for tab:', tabId, 'Looking for:', contentContainerId);
                            return;
                        }

                        // Show loading spinner
                        contentContainer.html('<div class="security-loading"><div class="spinner is-active"></div><p>Loading security data...</p></div>');

                        // Make AJAX request to get security status
                        $.ajax({
                            url: ajaxurl || '<?php echo admin_url('admin-ajax.php'); ?>',
                            type: 'POST',
                            data: {
                                action: 'office_addin_security_status',
                                nonce: '<?php echo wp_create_nonce('office_addin_security_status'); ?>',
                                tab: tabId.replace('#security-', '').replace('-tab', '')
                            },
                            success: function(response) {
                                console.log('✅ Security data loaded successfully for:', tabId);
                                if (response.success) {
                                    contentContainer.html(response.data.content || '<p>No security data available.</p>');
                                } else {
                                    contentContainer.html('<div class="notice notice-error"><p>Error loading security data: ' + (response.data.message || 'Unknown error') + '</p></div>');
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('❌ Error loading security data for:', tabId, error);
                                contentContainer.html('<div class="notice notice-error"><p>Failed to load security data. Please try again.</p></div>');
                            }
                        });
                    }

                    // Enhanced tab switching function with security support
                    function switchOfficeAddinTab(targetTab) {
                        console.log('🔄 Switching to Office Add-in tab:', targetTab);
                        
                        // Usa selettori specifici per evitare conflitti
                        var $tabContainer = $(officeAddinTabsSelector);
                        
                        // Hide all tab content SOLO all'interno del container office-addin-tabs
                        $tabContainer.find('.tab-content').removeClass('active').hide();

                        // Remove active class from all tabs SOLO all'interno del container office-addin-tabs
                        $tabContainer.find('.nav-tab').removeClass('nav-tab-active');

                        // Add active class to clicked tab SOLO all'interno del container office-addin-tabs
                        $tabContainer.find('.nav-tab[href="' + targetTab + '"]').addClass('nav-tab-active');

                        // Show the corresponding tab content SOLO all'interno del container office-addin-tabs
                        $tabContainer.find(targetTab).addClass('active').show();

                        // Update URL hash without causing page refresh (solo se necessario)
                        if (targetTab.startsWith('#')) {
                            var currentHash = window.location.hash;
                            if (currentHash !== targetTab) {
                                if (history.replaceState) {
                                    // Usa replaceState per evitare di aggiungere alla cronologia del browser
                                    history.replaceState(null, null, targetTab);
                                }
                            }
                        }

                        // Handle specific tab initialization
                        if (targetTab === '#preview-tab') {
                            console.log('📋 Preview tab activated, initializing preview');
                            setTimeout(function() {
                                if (typeof initializePreview === 'function') {
                                    initializePreview();
                                } else if (typeof window.updatePreview === 'function') {
                                    window.updatePreview();
                                }

                                // Initialize external grid
                                if (typeof window.initializeExternalGrid === 'function') {
                                    window.initializeExternalGrid();
                                }
                            }, 300);
                        } else if (targetTab.startsWith('#security-')) {
                            console.log('🔒 Security tab activated, loading security content');
                            setTimeout(function() {
                                loadSecurityContent(targetTab);
                            }, 100);
                        }
                        
                        console.log('✅ Tab switched successfully to:', targetTab);
                    }

                    // Gestisci il pulsante "Preview Changes" con selettore specifico
                    $(document).off('click.officetabs', '#preview-changes').on('click.officetabs', '#preview-changes', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        console.log('🔄 Preview Changes button clicked');

                        // Cambia alla tab di anteprima
                        switchOfficeAddinTab('#preview-tab');

                        // Attendi un momento per assicurarsi che la tab sia completamente caricata
                        setTimeout(function() {
                            // Verifica se la funzione updatePreview è disponibile
                            if (typeof window.updatePreview === 'function') {
                                console.log('🔄 Calling updatePreview from Preview Changes button');
                                window.updatePreview();
                            } else {
                                console.error('❌ updatePreview function not available for Preview Changes button');
                                alert('Impossibile aggiornare l\'anteprima. Ricarica la pagina e riprova.');
                            }
                        }, 300);
                        
                        return false;
                    });
                    
                    // Inizializza immediatamente
                    initializeTabs();

                    // Fallback per il caricamento della pagina
                    $(window).off('load.officetabs').on('load.officetabs', function() {
                        // Re-check if tabs are correctly initialized
                        var $tabContainer = $(officeAddinTabsSelector);
                        if (!$tabContainer.find('.tab-content.active').length) {
                            console.log('⚠️ Office Add-in tab content not initialized correctly, reinitializing...');
                            setTimeout(initializeTabs, 100);
                        }
                    });

                    // Debug information
                    console.log('✅ Office Add-in tab initialization complete');
                    
                    // Aggiungi un comando di debug globale
                    window.debugOfficeAddinTabs = function() {
                        var $tabContainer = $(officeAddinTabsSelector);
                        console.log('=== OFFICE ADD-IN TABS DEBUG ===');
                        console.log('Tab container found:', $tabContainer.length);
                        console.log('Total nav tabs:', $tabContainer.find('.nav-tab').length);
                        console.log('Active nav tabs:', $tabContainer.find('.nav-tab.nav-tab-active').length);
                        console.log('Total tab content:', $tabContainer.find('.tab-content').length);
                        console.log('Active tab content:', $tabContainer.find('.tab-content.active').length);
                        console.log('Current hash:', window.location.hash);
                        $tabContainer.find('.nav-tab').each(function(i) {
                            console.log('Tab ' + i + ':', $(this).attr('href'), $(this).hasClass('nav-tab-active') ? '(active)' : '');
                        });
                        $tabContainer.find('.tab-content').each(function(i) {
                            console.log('Content ' + i + ':', $(this).attr('id'), $(this).hasClass('active') ? '(active)' : '', $(this).is(':visible') ? '(visible)' : '(hidden)');
                        });
                    };
                });
            </script>

            <!-- CSS for Office Add-in tabs -->
            <style>
                .office-addin-tabs .nav-tab-wrapper {
                    margin-bottom: 1em;
                    border-bottom: 1px solid #ccc;
                }
                
                .office-addin-tabs .nav-tab {
                    position: relative;
                    display: inline-block;
                    padding: 8px 12px;
                    margin-left: 0;
                    font-size: 13px;
                    line-height: 1.4em;
                    text-decoration: none;
                    color: #0073aa;
                    cursor: pointer;
                    background-color: #f1f1f1;
                    border: 1px solid #ccc;
                    border-bottom: none;
                    transition: all 0.2s ease;
                }
                
                .office-addin-tabs .nav-tab:hover {
                    background-color: #fefefe;
                    color: #0073aa;
                }
                
                .office-addin-tabs .nav-tab-active,
                .office-addin-tabs .nav-tab-active:hover {
                    background-color: #fff;
                    border-bottom: 1px solid #fff;
                    color: #000;
                    position: relative;
                    top: 1px;
                }
                
                .office-addin-tabs .tab-content {
                    display: none !important;
                    padding: 20px;
                    background: #fff;
                    border: 1px solid #ccc;
                    border-top: none;
                    min-height: 200px;
                }
                
                .office-addin-tabs .tab-content.active {
                    display: block !important;
                }
                
                /* Debug styles for troubleshooting */
                .office-addin-tabs .tab-content.active {
                    border-left: 3px solid #0073aa;
                }
                
                .office-addin-tabs .nav-tab-active::after {
                    content: '';
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: #fff;
                }
                
                /* Ensure content is properly laid out */
                .office-addin-tabs {
                    position: relative;
                    z-index: 1;
                }

                /* Security Content Styles */
                .security-content-container {
                    min-height: 400px;
                    position: relative;
                }

                .security-loading {
                    text-align: center;
                    padding: 40px 20px;
                }

                .security-loading .spinner {
                    margin: 0 auto 20px;
                }

                .security-status-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                    margin-top: 20px;
                }

                .security-card {
                    background: #f9f9f9;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    padding: 20px;
                    transition: all 0.3s ease;
                }

                .security-card:hover {
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    border-color: #0073aa;
                }

                .security-card h3 {
                    margin-top: 0;
                    color: #23282d;
                    font-size: 16px;
                    border-bottom: 2px solid #0073aa;
                    padding-bottom: 8px;
                }

                .security-status-indicator {
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                    padding: 4px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    font-weight: 600;
                    text-transform: uppercase;
                }

                .security-status-indicator.secure {
                    background: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                }

                .security-status-indicator.warning {
                    background: #fff3cd;
                    color: #856404;
                    border: 1px solid #ffeaa7;
                }

                .security-status-indicator.critical {
                    background: #f8d7da;
                    color: #721c24;
                    border: 1px solid #f5c6cb;
                }

                .security-metric {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 12px 0;
                    padding: 8px 0;
                    border-bottom: 1px solid #eee;
                }

                .security-metric:last-child {
                    border-bottom: none;
                }

                .security-metric-label {
                    font-weight: 500;
                    color: #555;
                }

                .security-metric-value {
                    font-weight: 600;
                    color: #0073aa;
                }

                .security-recommendations {
                    background: #f0f8ff;
                    border-left: 4px solid #0073aa;
                    padding: 15px;
                    margin-top: 20px;
                }

                .security-recommendations h4 {
                    margin-top: 0;
                    color: #0073aa;
                }

                .security-recommendations ul {
                    margin: 0;
                    padding-left: 20px;
                }

                .security-recommendations li {
                    margin-bottom: 8px;
                }

                .security-logs-container {
                    max-height: 400px;
                    overflow-y: auto;
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    padding: 15px;
                }

                .security-log-entry {
                    padding: 8px 12px;
                    margin-bottom: 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    line-height: 1.4;
                }

                .security-log-entry.info {
                    background: #e7f3ff;
                    border-left: 3px solid #0073aa;
                }

                .security-log-entry.warning {
                    background: #fff8e1;
                    border-left: 3px solid #ff9800;
                }

                .security-log-entry.error {
                    background: #ffebee;
                    border-left: 3px solid #f44336;
                }

                .security-timestamp {
                    font-weight: 600;
                    color: #666;
                }

                .security-performance-chart {
                    height: 200px;
                    margin: 20px 0;
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #666;
                    font-style: italic;
                }
            </style>
        </div>
        <?php
    }

    /**
     * Renderizza la pagina Payment Gateway Configuration
     *
     * Gestisce la visualizzazione e la configurazione dei payment gateway
     */
    public function render_payment_gateway_page() {
        // Usa il sistema di controllo accessi FA per verificare l'accesso admin
        if (!apply_filters('fa_user_can_access', false, 'admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'document-viewer-plugin'));
        }

        // Se esiste la classe Payment_Gateway_Admin e ha il metodo render_payment_gateways_page
        if (class_exists('Payment_Gateway_Admin')) {
            $payment_gateway_admin = new Payment_Gateway_Admin();
            if (method_exists($payment_gateway_admin, 'render_payment_gateways_page')) {
                $payment_gateway_admin->render_payment_gateways_page();
                return;
            }
        }

        // Fallback se la classe Payment_Gateway_Admin non è disponibile
        echo '<div class="wrap">';
        echo '<h1>' . __('Payment Gateway Configuration', 'document-viewer-plugin') . '</h1>';
        echo '<p>' . __('Payment gateway configuration not available. Please ensure the Payment Gateway Admin class is loaded.', 'document-viewer-plugin') . '</p>';
        echo '</div>';
    }

    // Note: Gateway Testing functionality is now integrated within the Payment Gateway Configuration page

    /**
     * Get fallback Office Add-in HTML content
     *
     * Questo metodo fornisce un contenuto HTML di base per l'Office Add-in
     * nel caso in cui non sia possibile ottenere il contenuto predefinito dalla classe Document_Viewer_Settings
     *
     * @return string Contenuto HTML di base per l'Office Add-in
     */
    public function get_fallback_office_addin_content() {
        return '
<div class="excel-addin-container">
    <h2>Financial Advisor Excel Add-in</h2>

    <div class="section">
        <h3>1. Extract Text from Excel</h3>
        <p>Select cells in your Excel sheet and click the button below to extract text for analysis.</p>
        <button id="extract-text" class="primary-button" title="Extract text from selected cells in Excel">
            <span class="button-icon">📋</span> Extract Selected Cells
        </button>
        <div id="extracted-text-container" style="display:none;">
            <h4>Extracted Text:</h4>
            <div id="extracted-text" class="text-display"></div>
        </div>
    </div>

    <div class="section">
        <h3>2. Choose Analysis Question</h3>
        <div class="form-group">
            <label for="predefined-query">Select a predefined query:</label>
            <select id="predefined-query" class="full-width" title="Select from predefined analysis questions">
                <option value="">-- Select a query --</option>
                <option value="1">Analyze financial performance</option>
                <option value="2">Identify key trends</option>
                <option value="3">Summarize quarterly results</option>
                <option value="4">Compare regional performance</option>
            </select>
        </div>

        <div class="form-group">
            <label for="custom-query">Or enter your own question:</label>
            <textarea id="custom-query" rows="3" class="full-width" placeholder="Enter your analysis question here..." title="Enter a custom analysis question"></textarea>
        </div>

        <button id="analyze-button" class="primary-button" title="Send data for analysis">
            <span class="button-icon">🔍</span> Analyze
        </button>
    </div>

    <div class="section">
        <h3>3. Analysis Results</h3>
        <div id="analysis-results" class="results-container">
            <p class="placeholder">Analysis results will appear here after you extract text and submit your question.</p>
        </div>
    </div>

    <div class="section">
        <h3>Connection Status</h3>
        <div class="debug-info">
            <p><strong>API Status:</strong> <span id="api-status">Not connected</span></p>
            <p><strong>Selected Model:</strong> <span id="selected-model">None</span></p>
        </div>
    </div>

    <div class="footer">
        <p>This add-in connects to your Financial Advisor WordPress plugin and uses the same API settings.</p>
    </div>
</div>

<style>
.excel-addin-container {
    font-family: "Segoe UI", Arial, sans-serif;
    padding: 15px;
    max-width: 800px;
    margin: 0 auto;
}

.section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
}

h2 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    text-align: center;
}

h3 {
    color: #3498db;
    margin-top: 0;
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.full-width {
    width: 100%;
    box-sizing: border-box;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.primary-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.primary-button:hover {
    background-color: #2980b9;
}

.button-icon {
    margin-right: 8px;
}

.secondary-button {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.secondary-button:hover {
    background-color: #7f8c8d;
}

.text-display {
    border: 1px solid #ddd;
    padding: 10px;
    background-color: white;
    min-height: 100px;
    max-height: 200px;
    overflow-y: auto;
    margin-top: 10px;
}

.results-container {
    border: 1px solid #ddd;
    padding: 15px;
    background-color: white;
    min-height: 150px;
    max-height: 300px;
    overflow-y: auto;
}

.debug-info {
    font-size: 12px;
    color: #7f8c8d;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

.debug-info p {
    margin: 5px 0;
    flex: 1 0 200px;
}

.debug-info button {
    margin-top: 5px;
}

.placeholder {
    color: #95a5a6;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
}

.footer {
    text-align: center;
    font-size: 12px;
    color: #7f8c8d;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

/* Loading indicator */
.loading {
    text-align: center;
    padding: 20px;
}

.loading:after {
    content: ".";
    animation: dots 1s steps(5, end) infinite;
}

@keyframes dots {
    0%, 20% { content: "."; }
    40% { content: ".."; }
    60% { content: "..."; }
    80%, 100% { content: ""; }
}

/* Success and error messages */
.success-message {
    color: #27ae60;
    font-weight: bold;
}

.error-message {
    color: #e74c3c;
    font-weight: bold;
}
</style>
';
    }
}

/**
 * Funzione per accedere all'istanza del Menu Manager
 *
 * @return Financial_Advisor_Menu_Manager
 */
function financial_advisor_menu_manager() {
    return Financial_Advisor_Menu_Manager::get_instance();
}

// Inizializza il menu manager
financial_advisor_menu_manager();